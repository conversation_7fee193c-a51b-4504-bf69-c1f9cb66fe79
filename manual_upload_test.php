<?php
/**
 * 手動測試自動上傳功能
 * 用於測試和調試自動上傳系統
 */

session_start();
require_once 'db.php';
require_once 'auto_upload_processor.php';
require_once 'smb_scanner.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$action = $_GET['action'] ?? '';
$config_id = $_GET['config_id'] ?? 0;

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手動測試自動上傳 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .config-list {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }
        .config-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #f9f9f9;
        }
        .config-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .config-details {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cog"></i> 自動上傳測試工具</h1>
            <a href="auto_upload_manager.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回管理頁面
            </a>
        </div>

        <?php if ($action === ''): ?>
        <!-- 主選單 -->
        <div class="config-list">
            <div class="config-item">
                <h4>測試所有設定</h4>
                <div class="config-details">執行所有啟用的自動上傳設定</div>
                <a href="?action=test_all" class="btn btn-primary">
                    <i class="fas fa-play"></i> 執行測試
                </a>
            </div>

            <div class="config-item">
                <h4>測試單一設定</h4>
                <div class="config-details">選擇特定設定進行測試</div>
                <?php
                try {
                    $stmt = $pdo->query("SELECT id, name, smb_path, is_active FROM auto_upload_configs ORDER BY name");
                    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    foreach ($configs as $config):
                ?>
                <div style="margin: 5px 0;">
                    <a href="?action=test_single&config_id=<?php echo $config['id']; ?>" 
                       class="btn <?php echo $config['is_active'] ? 'btn-success' : 'btn-warning'; ?>">
                        <i class="fas fa-<?php echo $config['is_active'] ? 'play' : 'pause'; ?>"></i>
                        <?php echo htmlspecialchars($config['name']); ?>
                    </a>
                    <small style="color: #666;"><?php echo htmlspecialchars($config['smb_path']); ?></small>
                </div>
                <?php endforeach; ?>
                <?php } catch (Exception $e): ?>
                <div class="result-box error">錯誤：<?php echo htmlspecialchars($e->getMessage()); ?></div>
                <?php endtry; ?>
            </div>

            <div class="config-item">
                <h4>測試SMB連線</h4>
                <div class="config-details">測試各個SMB路徑的連線狀態</div>
                <a href="?action=test_connections" class="btn btn-warning">
                    <i class="fas fa-network-wired"></i> 測試連線
                </a>
            </div>
        </div>

        <?php elseif ($action === 'test_all'): ?>
        <!-- 測試所有設定 -->
        <h3>測試所有自動上傳設定</h3>
        <?php
        try {
            $processor = new AutoUploadProcessor($pdo);
            $results = $processor->processAllConfigs();
            
            echo '<div class="result-box success">';
            echo "測試完成！\n\n";
            
            foreach ($results as $result) {
                if (isset($result['error'])) {
                    echo "錯誤: " . $result['error'] . "\n";
                } else {
                    echo "設定: {$result['config_name']}\n";
                    echo "  掃描檔案: {$result['result']['scanned_files']} 個\n";
                    echo "  新檔案: {$result['result']['new_files']} 個\n";
                    echo "  成功上傳: {$result['result']['uploaded_files']} 個\n";
                    echo "  失敗: {$result['result']['failed_files']} 個\n";
                    
                    if (!empty($result['result']['errors'])) {
                        echo "  錯誤訊息:\n";
                        foreach ($result['result']['errors'] as $error) {
                            echo "    - $error\n";
                        }
                    }
                    echo "\n";
                }
            }
            echo '</div>';
        } catch (Exception $e) {
            echo '<div class="result-box error">測試失敗：' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>

        <?php elseif ($action === 'test_single' && $config_id): ?>
        <!-- 測試單一設定 -->
        <?php
        try {
            $stmt = $pdo->prepare("SELECT * FROM auto_upload_configs WHERE id = ?");
            $stmt->execute([$config_id]);
            $config = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$config) {
                echo '<div class="result-box error">找不到指定的設定</div>';
            } else {
                echo '<h3>測試設定: ' . htmlspecialchars($config['name']) . '</h3>';
                echo '<div class="config-details">SMB路徑: ' . htmlspecialchars($config['smb_path']) . '</div>';
                
                $processor = new AutoUploadProcessor($pdo);
                $result = $processor->processConfig($config);
                
                echo '<div class="result-box success">';
                echo "測試完成！\n\n";
                echo "掃描檔案: {$result['scanned_files']} 個\n";
                echo "新檔案: {$result['new_files']} 個\n";
                echo "成功上傳: {$result['uploaded_files']} 個\n";
                echo "失敗: {$result['failed_files']} 個\n";
                
                if (!empty($result['errors'])) {
                    echo "\n錯誤訊息:\n";
                    foreach ($result['errors'] as $error) {
                        echo "  - $error\n";
                    }
                }
                echo '</div>';
            }
        } catch (Exception $e) {
            echo '<div class="result-box error">測試失敗：' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>

        <?php elseif ($action === 'test_connections'): ?>
        <!-- 測試SMB連線 -->
        <h3>測試SMB連線狀態</h3>
        <?php
        try {
            $stmt = $pdo->query("SELECT id, name, smb_path FROM auto_upload_configs ORDER BY name");
            $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $scanner = new SMBScanner($pdo);
            
            foreach ($configs as $config) {
                echo '<div class="config-item">';
                echo '<h4>' . htmlspecialchars($config['name']) . '</h4>';
                echo '<div class="config-details">' . htmlspecialchars($config['smb_path']) . '</div>';
                
                $test_result = $scanner->testConnection($config['smb_path']);
                
                if ($test_result['success']) {
                    echo '<div class="result-box success">連線成功</div>';
                } else {
                    echo '<div class="result-box error">連線失敗：' . htmlspecialchars($test_result['message']) . '</div>';
                }
                echo '</div>';
            }
        } catch (Exception $e) {
            echo '<div class="result-box error">測試失敗：' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>

        <?php endif; ?>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h4>使用說明</h4>
            <ul>
                <li><strong>測試所有設定</strong>：執行所有啟用的自動上傳設定，模擬實際的排程執行</li>
                <li><strong>測試單一設定</strong>：選擇特定設定進行測試，便於調試問題</li>
                <li><strong>測試SMB連線</strong>：檢查各個SMB路徑的連線狀態，確保網路連通性</li>
            </ul>
            <p><strong>注意</strong>：測試過程中會實際上傳檔案到系統中，請謹慎使用。</p>
        </div>
    </div>
</body>
</html>
