<?php
/**
 * 完整調試掃描流程
 */

require_once 'db.php';

class DebugSMBScanner {
    private $smb_username = 'paper';
    private $smb_password = 'liang55778010';
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function debugScanFiles($config) {
        echo "=== 開始調試掃描 ===\n";
        echo "設定: {$config['name']}\n";
        echo "路徑: {$config['smb_path']}\n";
        echo "遞迴: " . ($config['scan_subdirs'] ? '是' : '否') . "\n";
        echo "副檔名: {$config['file_extensions']}\n\n";
        
        $allowed_extensions = explode(',', strtolower($config['file_extensions']));
        $path_info = $this->convertToSmbPath($config['smb_path']);
        
        echo "路徑轉換結果:\n";
        echo "  基礎路徑: {$path_info['base_path']}\n";
        echo "  子路徑: {$path_info['sub_path']}\n\n";
        
        if ($config['scan_subdirs']) {
            $files = $this->debugScanDirectoryRecursive($path_info, $allowed_extensions);
        } else {
            $files = $this->debugScanDirectory($path_info, $allowed_extensions);
        }
        
        echo "\n=== 掃描結果 ===\n";
        echo "找到檔案數量: " . count($files) . "\n";
        
        if (!empty($files)) {
            foreach ($files as $index => $file) {
                echo "\n檔案 " . ($index + 1) . ":\n";
                echo "  名稱: {$file['name']}\n";
                echo "  路徑: {$file['path']}\n";
                echo "  大小: {$file['size']} bytes\n";
                echo "  副檔名: {$file['extension']}\n";
                
                // 測試日期提取
                $file_date = $this->extractFileDate($file);
                echo "  提取日期: $file_date\n";
                
                $cutoff_date = '2025-05-01';
                if ($file_date >= $cutoff_date) {
                    echo "  日期檢查: ✅ 符合條件\n";
                } else {
                    echo "  日期檢查: ❌ 不符合條件 (需要 >= $cutoff_date)\n";
                }
                
                // 檢查是否已上傳
                $file_hash = $this->calculateFileHash($file);
                $stmt = $this->pdo->prepare("
                    SELECT id FROM uploaded_file_hashes
                    WHERE file_hash = ? OR (file_name = ? AND file_path = ?)
                ");
                $stmt->execute([$file_hash, $file['name'], $file['path']]);
                
                if ($stmt->fetch()) {
                    echo "  上傳檢查: ❌ 已上傳過\n";
                } else {
                    echo "  上傳檢查: ✅ 未上傳\n";
                }
            }
        }
        
        return $files;
    }
    
    private function convertToSmbPath($windows_path) {
        $path = str_replace('\\', '/', $windows_path);
        if (substr($path, 0, 2) !== '//') {
            $path = '//' . ltrim($path, '/');
        }
        
        $parts = explode('/', ltrim($path, '/'));
        if (count($parts) >= 2) {
            $server = $parts[0];
            $share = $parts[1];
            $subpath = implode('/', array_slice($parts, 2));
            
            return [
                'base_path' => "//$server/$share",
                'sub_path' => $subpath
            ];
        }
        
        return [
            'base_path' => $path,
            'sub_path' => ''
        ];
    }
    
    private function debugScanDirectory($path_info, $allowed_extensions) {
        echo "掃描目錄: {$path_info['base_path']} / {$path_info['sub_path']}\n";

        $files = [];
        if (empty($path_info['sub_path'])) {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password
            );
        } else {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "cd %s; ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password,
                $path_info['sub_path']
            );
        }
        
        echo "執行命令: $command\n";
        $output = shell_exec($command);
        echo "命令輸出:\n$output\n";
        
        if ($output) {
            $lines = explode("\n", $output);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '.') === 0) continue;
                
                if (preg_match('/^\s*(.+?)\s+([AD])\s+(\d+)\s+(.+)$/', $line, $matches)) {
                    $filename = trim($matches[1]);
                    $type = $matches[2];
                    $size = $matches[3];
                    
                    if ($type === 'A') {
                        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                        echo "找到檔案: $filename (副檔名: $extension)\n";
                        
                        if (in_array($extension, $allowed_extensions)) {
                            $full_path = $path_info['base_path'] . '/' . 
                                        ($path_info['sub_path'] ? $path_info['sub_path'] . '/' : '') . 
                                        $filename;
                            $files[] = [
                                'name' => $filename,
                                'path' => $full_path,
                                'size' => $size,
                                'extension' => $extension
                            ];
                            echo "  -> 符合副檔名條件，已加入列表\n";
                        } else {
                            echo "  -> 不符合副檔名條件\n";
                        }
                    }
                }
            }
        }
        
        return $files;
    }
    
    private function debugScanDirectoryRecursive($path_info, $allowed_extensions, $depth = 0, $max_depth = 5) {
        echo str_repeat("  ", $depth) . "遞迴掃描 (深度 $depth): {$path_info['sub_path']}\n";
        
        $files = [];
        
        if ($depth > $max_depth) {
            echo str_repeat("  ", $depth) . "達到最大深度，停止遞迴\n";
            return $files;
        }
        
        // 先掃描當前目錄的檔案
        $current_files = $this->debugScanDirectory($path_info, $allowed_extensions);
        $files = array_merge($files, $current_files);
        
        // 然後掃描子目錄
        if (empty($path_info['sub_path'])) {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password
            );
        } else {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "cd %s; ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password,
                $path_info['sub_path']
            );
        }
        
        $output = shell_exec($command);
        
        if ($output) {
            $lines = explode("\n", $output);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '.') === 0) continue;
                
                if (preg_match('/^\s*(.+?)\s+D\s+\d+\s+(.+)$/', $line, $matches)) {
                    $dirname = trim($matches[1]);
                    if ($dirname !== '.' && $dirname !== '..') {
                        echo str_repeat("  ", $depth) . "找到子目錄: $dirname\n";
                        
                        $subdir_path_info = [
                            'base_path' => $path_info['base_path'],
                            'sub_path' => $path_info['sub_path'] ? $path_info['sub_path'] . '/' . $dirname : $dirname
                        ];
                        $subdir_files = $this->debugScanDirectoryRecursive($subdir_path_info, $allowed_extensions, $depth + 1, $max_depth);
                        $files = array_merge($files, $subdir_files);
                    }
                }
            }
        }
        
        return $files;
    }
    
    private function extractFileDate($file) {
        $path = $file['path'];
        
        if (preg_match('/11[4-9](\d{2})/', $path, $matches)) {
            $year_month = $matches[0];
            $roc_year = intval(substr($year_month, 0, 3));
            $year = $roc_year + 1911;
            $month = str_pad(substr($year_month, 3, 2), 2, '0', STR_PAD_LEFT);
            return "$year-$month-01";
        }
        
        if (preg_match('/20(\d{2})[\/\-]?(\d{1,2})/', $path, $matches)) {
            $year = '20' . $matches[1];
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            return "$year-$month-01";
        }
        
        $filename = $file['name'];
        if (preg_match('/(\d{4})[\/\-]?(\d{1,2})[\/\-]?(\d{1,2})/', $filename, $matches)) {
            $year = $matches[1];
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
            return "$year-$month-$day";
        }
        
        return date('Y-m-d');
    }
    
    private function calculateFileHash($file) {
        $data = $file['path'] . '|' . $file['name'] . '|' . $file['size'];
        return hash('sha256', $data);
    }
}

// 測試
$scanner = new DebugSMBScanner($pdo);

$stmt = $pdo->prepare("SELECT * FROM auto_upload_configs WHERE name LIKE '%鳳林台電%' AND is_active = 1");
$stmt->execute();
$config = $stmt->fetch(PDO::FETCH_ASSOC);

if ($config) {
    $scanner->debugScanFiles($config);
} else {
    echo "找不到鳳林台電的設定\n";
}
?>
