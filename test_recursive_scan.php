<?php
/**
 * 測試遞迴掃描功能
 */

require_once 'db.php';
require_once 'smb_scanner.php';

// 測試鳳林台電的遞迴掃描
$scanner = new SMBScanner($pdo);

// 獲取鳳林台電的設定
$stmt = $pdo->prepare("SELECT * FROM auto_upload_configs WHERE name LIKE '%鳳林台電%' AND is_active = 1");
$stmt->execute();
$config = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$config) {
    echo "找不到鳳林台電的設定\n";
    exit(1);
}

echo "=== 測試鳳林台電遞迴掃描 ===\n";
echo "設定名稱: {$config['name']}\n";
echo "SMB路徑: {$config['smb_path']}\n";
echo "遞迴掃描: " . ($config['scan_subdirs'] ? '啟用' : '停用') . "\n";
echo "允許副檔名: {$config['file_extensions']}\n\n";

// 執行掃描
echo "開始掃描...\n";
$result = $scanner->scanFiles($config);

if ($result['success']) {
    echo "掃描成功！\n";
    echo "總共找到檔案: {$result['total_found']} 個\n";
    echo "新檔案: {$result['new_files']} 個\n\n";
    
    if (!empty($result['files'])) {
        echo "=== 找到的檔案列表 ===\n";
        foreach ($result['files'] as $index => $file) {
            echo ($index + 1) . ". {$file['name']}\n";
            echo "   路徑: {$file['path']}\n";
            echo "   大小: " . number_format($file['size']) . " bytes\n";
            echo "   副檔名: {$file['extension']}\n";
            if (isset($file['hash'])) {
                echo "   雜湊: " . substr($file['hash'], 0, 16) . "...\n";
            }
            echo "\n";
        }
    } else {
        echo "沒有找到符合條件的新檔案\n";
        echo "可能原因：\n";
        echo "1. 檔案已經上傳過\n";
        echo "2. 檔案日期不符合過濾條件（需要2025年5月之後）\n";
        echo "3. 檔案格式不符合（只接受PDF）\n";
    }
} else {
    echo "掃描失敗: {$result['message']}\n";
}

echo "\n=== 測試完成 ===\n";
?>
