<?php
// 引入慈濟文件處理器
require_once 'tzuchi_processor_improved.php';
require_once 'tzuchi_processor_special.php';

/**
 * PDF處理工具類
 * 用於公文PDF檔案的解析與資訊提取
 */
class PDFProcessor {
    /**
     * 添加一個靜態變數來存儲標準化選項
     *
     * @var array
     */
    private static $standardizeOptions = [
        'enabled' => false,
        'preserve_original' => true
    ];

    /**
     * 添加一個靜態變數來存儲 Gemini API 選項
     *
     * @var array
     */
    private static $geminiOptions = [
        'enabled' => false,
        'api_key' => '',
        'fallback_to_traditional' => true
    ];



    /**
     * 設置PDF標準化選項
     *
     * @param array $options 標準化選項
     * @return void
     */
    public static function setStandardizeOptions($options) {
        if (is_array($options)) {
            self::$standardizeOptions = array_merge(self::$standardizeOptions, $options);
        }
    }

    /**
     * 設置 Gemini API 選項
     *
     * @param array $options Gemini API 選項
     * @return void
     */
    public static function setGeminiOptions($options) {
        if (is_array($options)) {
            self::$geminiOptions = array_merge(self::$geminiOptions, $options);
        }
    }



    /**
     * 標準化PDF文件
     *
     * @param string $filePath PDF文件路徑
     * @return string|false 標準化後的文件路徑或失敗時返回false
     */
    public static function standardizePDF($filePath) {
        if (!self::$standardizeOptions['enabled']) {
            return false;
        }

        if (!file_exists($filePath)) {
            error_log("標準化失敗：檔案不存在 - " . $filePath);
            return false;
        }

        try {
            // 建立輸出目錄
            $outputDir = dirname($filePath) . '/standardized/';
            if (!is_dir($outputDir)) {
                mkdir($outputDir, 0777, true);
            }

            // 新檔案路徑
            $fileName = basename($filePath);
            $newFilePath = $outputDir . 'std_' . $fileName;

            // 如果是測試環境，或者實際環境沒有處理工具，則簡單複製文件
            if (!function_exists('shell_exec')) {
                error_log("無法使用shell_exec，直接複製文件");
                copy($filePath, $newFilePath);
                return $newFilePath;
            }

            // 嘗試使用 Ghostscript 進行標準化
            $safeFilePath = escapeshellarg($filePath);
            $safeNewFilePath = escapeshellarg($newFilePath);

            $command = "gs -sDEVICE=pdfwrite -dPDFSETTINGS=/prepress -dNOPAUSE -dBATCH -sOutputFile=$safeNewFilePath $safeFilePath";
            error_log("執行標準化命令: " . $command);

            $output = shell_exec($command);
            error_log("標準化輸出: " . $output);

            // 檢查文件是否成功創建
            if (file_exists($newFilePath)) {
                error_log("PDF標準化成功: " . $newFilePath);
                return $newFilePath;
            } else {
                error_log("標準化失敗：無法創建新文件 - " . $newFilePath);
                return false;
            }
        } catch (Exception $e) {
            error_log("標準化過程中出錯: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 解析PDF文件內容
     *
     * @param string $filePath PDF文件路徑
     * @return array 解析出的公文資訊
     */
    public static function parsePDF($filePath) {
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'message' => '檔案不存在',
                'data' => [
                    'notes' => '檔案不存在，請重新上傳',
                    'ocr_failed' => true
                ]
            ];
        }

        // 獲取檔名資訊，以備後用
        $fileName = basename($filePath);
        $fileNameInfo = self::extractInfoFromFilename($fileName);

        // 檢查是否為慈濟文件
        $isTzuChiDoc = TzuChiSpecialProcessor::isTzuChiDocument($filePath, $fileName);
        if ($isTzuChiDoc) {
            error_log("檢測到慈濟文件: " . $filePath);
        }

        // 對所有文件都優先使用 Gemini API 處理
        if (self::$geminiOptions['enabled'] && !empty(self::$geminiOptions['api_key'])) {
            error_log("優先使用 Gemini API 解析 PDF: " . $filePath . ($isTzuChiDoc ? "（慈濟文件）" : ""));
            $geminiResult = self::analyzeWithGemini($filePath);

            // 如果 Gemini 解析成功，直接返回結果
            if ($geminiResult['success']) {
                error_log("Gemini API 解析成功");
                return $geminiResult;
            }

            // 如果 Gemini 解析失敗但設置了回退到傳統方法，則繼續使用傳統方法
            if (self::$geminiOptions['fallback_to_traditional']) {
                error_log("Gemini API 解析失敗，回退到傳統方法");
            } else {
                // 如果不回退，則直接返回失敗結果
                error_log("Gemini API 解析失敗，不回退到傳統方法");
                return $geminiResult;
            }
        } else {
            error_log("Gemini API 未啟用或API金鑰未設置，使用傳統方法");
        }

        // 如果是慈濟文件且Gemini API未啟用或解析失敗，則使用專門的處理器
        if ($isTzuChiDoc) {
            error_log("使用專門的慈濟文件處理器: " . $filePath);
            $result = TzuChiSpecialProcessor::processTzuChiDocument($filePath);

            if ($result['success']) {
                error_log("慈濟文件處理成功");
                return $result;
            } else {
                error_log("慈濟文件處理失敗，嘗試使用區域提取方法");
                $regionResult = TzuChiSpecialProcessor::processWithRegionExtraction($filePath);

                if ($regionResult['success']) {
                    error_log("使用區域提取方法處理慈濟文件成功");
                    return $regionResult;
                }

                error_log("所有慈濟文件處理方法都失敗，回退到標準處理");
            }
        }

        // 使用 pdftotext 從PDF提取文字內容
        $content = self::extractTextFromPDF($filePath);

        // 如果 pdftotext 和 PaddleOCR 都失敗，嘗試 Tesseract OCR
        if (empty($content) || strlen(trim($content)) < 100) {
            // 添加詳細日誌
            error_log("使用 pdftotext 和 PaddleOCR 提取文本失敗，內容長度: " . strlen($content) . "，嘗試使用 Tesseract OCR: " . $filePath);

            // 嘗試 Tesseract OCR 處理
            $content = self::performOCROnPDF($filePath);
        }

        // 如果仍然失敗，返回解析失敗但仍可處理的結果
        if (empty($content) || strlen(trim($content)) < 50) {
            error_log("所有文字提取方法都失敗，使用檔名作為提取依據: " . $filePath);

            // 從檔名提取信息
            $titleFromFilename = pathinfo($fileName, PATHINFO_FILENAME);
            // 清理檔名中的前綴數字
            $titleFromFilename = preg_replace('/^\d+_/', '', $titleFromFilename);

            return [
                'success' => true,  // 仍然返回success=true以確保流程繼續
                'message' => '無法提取PDF內容，使用檔名作為標題',
                'data' => [
                    'document_number' => $fileNameInfo['document_number'] ?: null,
                    'title' => $titleFromFilename ?: ('公文 ' . date('Ymd')),
                    'sender' => $fileNameInfo['sender'] ?: null,
                    'receiver' => null,
                    'issue_date' => $fileNameInfo['date'] ?: date('Y-m-d'),
                    'content_summary' => null,
                    'notes' => 'PDF內容解析失敗，需要手動編輯',
                    'site' => $fileNameInfo['site'] ?: null,
                    'ocr_failed' => true
                ]
            ];
        }

        // 正常解析公文資訊
        $documentInfo = [
            'document_number' => self::extractDocumentNumber($content, $filePath) ?: $fileNameInfo['document_number'],
            'title' => self::extractTitle($content) ?: $fileNameInfo['title'] ?: basename($fileName, '.pdf'),
            'sender' => self::postProcessSender(self::extractSender($content) ?: $fileNameInfo['sender']),
            'receiver' => self::extractReceiver($content),
            'issue_date' => self::extractIssueDate($content) ?: $fileNameInfo['date'] ?: date('Y-m-d'),
            'notes' => self::extractNotes($content) ?: $fileNameInfo['note'],
            'summary' => self::extractSummary($content),
            'site' => self::extractSiteInfo($content) ?: $fileNameInfo['site']
        ];

        return [
            'success' => true,
            'message' => '成功解析PDF內容',
            'data' => $documentInfo
        ];
    }

    /**
     * 從PDF提取文本內容
     *
     * @param string $filePath PDF文件路徑
     * @return string 提取出的文本內容
     */
    public static function extractText($filePath) {
        // 檢查文件是否存在
        if (!file_exists($filePath)) {
            error_log("PDF 文件不存在: {$filePath}");
            return '';
        }

        // 記錄更詳細的調試信息
        $debugDir = __DIR__ . '/debug_pdfs';
        if (!is_dir($debugDir)) {
            mkdir($debugDir, 0777, true);
        }

        $fileBaseName = basename($filePath);
        $allResults = [];

        // 安全處理檔案路徑
        $safeFilePath = escapeshellarg($filePath);

        if (function_exists('shell_exec')) {
            // 方法1: 優先使用 XPDF 工具
            try {
                // 使用 XPDF 的 pdftotext 工具，使用不同模式
                $command = "pdftotext -layout -enc UTF-8 {$safeFilePath} -";
                $content = shell_exec($command);

                file_put_contents($debugDir . '/xpdf_layout_' . $fileBaseName . '.txt', $content);
                $allResults['xpdf_layout'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    error_log("使用 XPDF (pdftotext layout) 模式成功提取內容，長度: " . strlen($content));
                    return $content;
                }

                // 嘗試 XPDF 的 raw 模式
                $command = "pdftotext -raw -enc UTF-8 {$safeFilePath} -";
                $content = shell_exec($command);

                file_put_contents($debugDir . '/xpdf_raw_' . $fileBaseName . '.txt', $content);
                $allResults['xpdf_raw'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    // 針對慈濟基金會公文的特殊後處理
                    if (strpos($content, '慈濟') !== false || strpos($content, 'TZU-CHI') !== false) {
                        // 移除多餘的空格和特殊字符
                        $content = preg_replace('/\s+/', ' ', $content);
                        $content = preg_replace('/([：:])(\s+)/', '$1', $content);
                    }
                    error_log("使用 XPDF (pdftotext raw) 模式成功提取內容，長度: " . strlen($content));
                    return $content;
                }

                // 嘗試 XPDF 的其他選項
                $command = "pdftotext -nopgbrk -enc UTF-8 {$safeFilePath} -";
                $content = shell_exec($command);

                file_put_contents($debugDir . '/xpdf_nopgbrk_' . $fileBaseName . '.txt', $content);
                $allResults['xpdf_nopgbrk'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    error_log("使用 XPDF (pdftotext nopgbrk) 模式成功提取內容，長度: " . strlen($content));
                    return $content;
                }
            } catch (Exception $e) {
                error_log("XPDF 執行錯誤: " . $e->getMessage());
            }

            // 方法2: 使用 MuPDF 工具
            try {
                // 首先檢查mutool版本並使用正確的語法
                $versionCheck = shell_exec("mutool --help 2>&1");

                if (strpos($versionCheck, 'extract') !== false) {
                    // 新版本mutool，嘗試不同的語法
                    $commands = [
                        "mutool draw -F txt {$safeFilePath}",  // 新版本語法
                        "mutool extract {$safeFilePath}",      // 舊版本語法
                    ];

                    foreach ($commands as $command) {
                        $content = shell_exec($command . " 2>/dev/null");

                        if (!empty($content) && strlen($content) > 20) {
                            file_put_contents($debugDir . '/mupdf_extract_' . $fileBaseName . '.txt', $content);
                            $allResults['mupdf_extract'] = $content;
                            error_log("使用 MuPDF 成功提取內容，長度: " . strlen($content));
                            return $content;
                        }
                    }

                    // 嘗試 MuPDF 的 mutool draw 方式（輸出到檔案）
                    $tempFile = $debugDir . '/mupdf_temp_' . $fileBaseName . '.txt';
                    $command = "mutool draw -F txt -o {$tempFile} {$safeFilePath} 2>/dev/null";
                    shell_exec($command);

                    if (file_exists($tempFile)) {
                        $content = file_get_contents($tempFile);
                        $allResults['mupdf_draw'] = $content;

                        if (!empty($content) && strlen($content) > 20) {
                            error_log("使用 MuPDF (mutool draw) 模式成功提取內容，長度: " . strlen($content));
                            return $content;
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("MuPDF 執行錯誤: " . $e->getMessage());
            }

            // 方法3: 使用 Poppler-utils (pdf2text) 工具
            try {
                // 使用 Poppler 中的 pdftotext 工具
                $command = "pdftotext -layout {$safeFilePath} -";
                $content = shell_exec($command);

                file_put_contents($debugDir . '/poppler_layout_' . $fileBaseName . '.txt', $content);
                $allResults['poppler_layout'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    error_log("使用 Poppler-utils (layout) 模式成功提取內容，長度: " . strlen($content));
                    return $content;
                }

                // 嘗試不同選項
                $command = "pdftotext -raw {$safeFilePath} -";
                $content = shell_exec($command);

                file_put_contents($debugDir . '/poppler_raw_' . $fileBaseName . '.txt', $content);
                $allResults['poppler_raw'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    // 針對慈濟基金會公文的特殊後處理
                    if (strpos($content, '慈濟') !== false || strpos($content, 'TZU-CHI') !== false) {
                        // 移除多餘的空格和特殊字符
                        $content = preg_replace('/\s+/', ' ', $content);
                        $content = preg_replace('/([：:])(\s+)/', '$1', $content);
                    }
                    error_log("使用 Poppler-utils (raw) 模式成功提取內容，長度: " . strlen($content));
                    return $content;
                }
            } catch (Exception $e) {
                error_log("Poppler-utils 執行錯誤: " . $e->getMessage());
            }

            // 方法4: 傳統 pdftotext (如果尚未提取到有效內容)
            try {
                // 由於 pdftotext 是 XPDF 的一部分，這部分可能是重複的，但保留以確保向後兼容
                $command = "pdftotext -layout -enc UTF-8 {$safeFilePath} -";
                $content = shell_exec($command);

                file_put_contents($debugDir . '/pdftotext_layout_' . $fileBaseName . '.txt', $content);
                $allResults['pdftotext_layout'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    error_log("使用 pdftotext layout 模式成功提取內容，長度: " . strlen($content));
                    return $content;
                }
            } catch (Exception $e) {
                error_log("pdftotext layout 模式執行錯誤: " . $e->getMessage());
            }
        } else {
            error_log("shell_exec 函數不可用，無法使用命令行工具");
        }

        // 方法5: 使用 PdfParser 庫 (如有安裝)
        if (class_exists('Smalot\PdfParser\Parser')) {
            try {
                $parser = new \Smalot\PdfParser\Parser();
                $pdf = $parser->parseFile($filePath);
                $content = $pdf->getText();

                file_put_contents($debugDir . '/pdfparser_' . $fileBaseName . '.txt', $content);
                $allResults['pdfparser'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    error_log("使用 PdfParser 成功提取內容，長度: " . strlen($content));
                    return $content;
                }

                // 嘗試逐頁提取
                $pages = $pdf->getPages();
                $allContent = '';
                foreach ($pages as $pageNum => $page) {
                    $pageText = $page->getText();
                    $allContent .= $pageText . "\n";
                }

                file_put_contents($debugDir . '/pdfparser_pages_' . $fileBaseName . '.txt', $allContent);
                $allResults['pdfparser_pages'] = $allContent;

                if (!empty($allContent) && strlen($allContent) > 20) {
                    error_log("使用 PdfParser 逐頁提取成功，長度: " . strlen($allContent));
                    return $allContent;
                }
            } catch (Exception $e) {
                error_log("PdfParser 錯誤: " . $e->getMessage());
            }
        } else {
            error_log("PdfParser 庫不可用");
        }

        // 方法6: 嘗試使用 pdfbox 提取文本 (如果已安裝)
        try {
            $command = "java -jar /usr/local/bin/pdfbox-app.jar ExtractText {$safeFilePath} -o " . escapeshellarg($debugDir . '/pdfbox_' . $fileBaseName . '.txt');
            shell_exec($command);

            $pdfboxOutput = $debugDir . '/pdfbox_' . $fileBaseName . '.txt';
            if (file_exists($pdfboxOutput)) {
                $content = file_get_contents($pdfboxOutput);
                $allResults['pdfbox'] = $content;

                if (!empty($content) && strlen($content) > 20) {
                    error_log("使用 PDFBox 成功提取內容，長度: " . strlen($content));
                    return $content;
                }
            }
        } catch (Exception $e) {
            error_log("PDFBox 執行錯誤: " . $e->getMessage());
        }

        // 合併所有結果，選擇最長的內容
        $bestContent = '';
        $maxLength = 0;

        foreach ($allResults as $method => $content) {
            $length = strlen($content);
            if ($length > $maxLength) {
                $maxLength = $length;
                $bestContent = $content;
            }
        }

        if (!empty($bestContent) && strlen($bestContent) > 10) {
            error_log("使用合併結果方法，提取內容長度: " . strlen($bestContent));
            return $bestContent;
        }

        // 在所有方法嘗試後，如果仍然無法提取有效內容，使用OCR
        if (empty($bestContent) || strlen($bestContent) <= 10) {
            error_log("嘗試使用Tesseract OCR處理PDF: " . $filePath);
            return self::performOCROnPDF($filePath);
        }

        return '';
    }

    /**
     * 使用 OCR 服務處理 PDF
     * 注意：此方法已經移除 Adobe Acrobat OCR 功能，但保留方法簿以保持向下相容性
     *
     * @param string $filePath PDF 文件路徑
     * @return string 提取的文本內容
     */
    public static function useAdobeOCRService($filePath) {
        // 此方法已經移除 Adobe Acrobat OCR 功能
        // 直接返回空字符串，讓程序使用其他方法提取文本
        error_log("已移除 Adobe Acrobat OCR 功能，將使用其他方法提取文本: " . $filePath);
        return "";
    }

    /**
     * 從PDF提取文字的方法（作為向下相容性的橋接函數）
     * 調用 extractText 方法
     */
    public static function extractTextFromPDF($filePath) {
        // 直接使用原有方法提取文本
        $content = self::extractText($filePath);

        // 如果原有方法失敗，嘗試使用 PaddleOCR
        if (empty($content) || strlen(trim($content)) < 100) {
            error_log("使用原有方法提取文本失敗，內容長度: " . strlen($content) . "，嘗試使用 PaddleOCR: " . $filePath);
            $content = self::performPaddleOCROnPDF($filePath);
        }

        return $content;
    }

    /**
     * 使用 PaddleOCR 進行文字識別處理
     *
     * @param string $filePath PDF 文件路徑
     * @return string 處理後的文本
     */
    public static function performPaddleOCROnPDF($filePath) {
        // 首先將 PDF 轉換為圖像
        $debugDir = __DIR__ . '/debug_pdfs';
        $fileBaseName = basename($filePath, '.pdf');
        $imageDir = $debugDir . '/images_' . $fileBaseName;

        if (!is_dir($imageDir)) {
            if (!mkdir($imageDir, 0777, true)) {
                error_log("無法為 OCR 創建圖像目錄: " . $imageDir);
                return "OCR 處理失敗：無法創建圖像目錄";
            }
        }

        // 使用 ImageMagick 將 PDF 轉換為圖像
        $safeFilePath = escapeshellarg($filePath);
        $outputPattern = escapeshellarg($imageDir . '/page-%03d.png');

        // 檢查是否為慈濟文件
        $isTzuChiDocument = (strpos($filePath, '慈濟') !== false ||
                              strpos($filePath, '常慈') !== false ||
                              strpos($filePath, '慈志') !== false ||
                              strpos($filePath, '慈書字') !== false ||
                              strpos($filePath, '財團法人') !== false ||
                              strpos($filePath, '佛教') !== false ||
                              strpos($filePath, 'TZU-CHI') !== false ||
                              strpos($filePath, 'BUDDHIST COMPASSION RELIEF') !== false);

        if ($isTzuChiDocument) {
            error_log("在 PaddleOCR 中偵測到慈濟文件，使用增強的圖像處理參數: {$filePath}");
            $convertCmd = "convert -density 1500 {$safeFilePath} -quality 100 -background white -alpha remove -sharpen 0x3.0 -contrast-stretch 3% -normalize -despeckle -deskew 40% -level 10%,90% -unsharp 0x1.0 {$outputPattern}";
        } else {
            $convertCmd = "convert -density 300 {$safeFilePath} -quality 100 {$outputPattern}";
        }

        $convertOutput = shell_exec($convertCmd . " 2>&1");

        // 檢查轉換是否成功
        $imageFiles = glob($imageDir . '/page-*.png');
        if (empty($imageFiles)) {
            error_log("沒有產生圖像文件，PDF 可能為空或格式異常: " . $filePath);
            return "PDF 解析失敗：未能產生圖像文件，文件需要手動處理";
        }

        error_log("PDF 轉圖像成功，產生了 " . count($imageFiles) . " 個圖像文件");

        // 使用 PaddleOCR 處理每個圖像
        $ocrContent = '';
        $pythonScript = __DIR__ . '/paddle_ocr.py';

        foreach ($imageFiles as $imageFile) {
            $safeImagePath = escapeshellarg($imageFile);
            // 檢查是否為慈濟公文，如果是則使用特別的語言模型
            $isSpecialDocument = (strpos($filePath, '慈濟') !== false ||
                                  strpos($filePath, '常慈') !== false ||
                                  strpos($filePath, '慈志') !== false ||
                                  strpos($filePath, '慈志營字') !== false ||
                                  strpos($filePath, '慈書字') !== false ||
                                  strpos($filePath, '財團法人') !== false ||
                                  strpos($filePath, '佛教') !== false ||
                                  strpos($filePath, 'TZU-CHI') !== false ||
                                  strpos($filePath, 'BUDDHIST COMPASSION RELIEF') !== false);

            // 如果是慈濟公文，使用繁體中文和英文混合模式
            $langParam = $isSpecialDocument ? "ch_tra_en" : "ch";
            error_log("使用 PaddleOCR 處理圖像: {$imageFile}, 語言模式: {$langParam}");

            // 對慶濟文件進行多次處理，使用不同的參數並合併結果
            if ($isSpecialDocument) {
                // 對慶志營字的文件進行特別處理
                if (strpos($filePath, '慈志營字') !== false) {
                    error_log("偵測到慈志營字文件: {$filePath}");

                    // 如果啟用了 Gemini API，則嘗試使用 Gemini 進行解析
                    if (self::$geminiOptions['enabled'] && !empty(self::$geminiOptions['api_key'])) {
                        error_log("嘗試使用 Gemini API 解析慈志營字文件: " . $filePath);
                        $geminiResult = self::analyzeWithGemini($filePath);

                        // 如果 Gemini 解析成功，直接返回結果
                        if ($geminiResult['success']) {
                            error_log("Gemini API 成功解析慈志營字文件");
                            return isset($geminiResult['data']['notes']) ? $geminiResult['data']['notes'] : $geminiResult['text'];
                        }

                        error_log("Gemini API 解析慈志營字文件失敗，嘗試使用專門處理器");
                    }

                    // 使用專門的慈濟文件處理器
                    error_log("使用專門的慈濟文件處理器: {$filePath}");
                    $tzuchiResult = TzuChiSpecialProcessor::processTzuChiDocument($filePath);

                    if (isset($tzuchiResult['success']) && $tzuchiResult['success'] && isset($tzuchiResult['text'])) {
                        error_log("專門慈濟文件處理器處理成功，內容長度: " . strlen($tzuchiResult['text']));

                        // 返回結果，不需要繼續處理
                        return $tzuchiResult['text'];
                    } else {
                        error_log("專門慈濟文件處理器失敗，嘗試使用區域提取方法");

                        // 嘗試使用區域提取方法
                        $regionResult = TzuChiSpecialProcessor::processWithRegionExtraction($filePath);

                        if ($regionResult['success'] && isset($regionResult['data']['notes']) && !empty($regionResult['data']['notes'])) {
                            error_log("使用區域提取方法處理慈濟文件成功");
                            return $regionResult['data']['notes'];
                        }

                        error_log("所有專門方法都失敗，回退到原有的處理方式");
                    }
                } else {
                    // 其他慶濟文件使用原有的多次處理
                    // 第一次處理，使用標準參數
                    $command = "python {$pythonScript} {$safeImagePath} {$langParam}";
                    $result1 = shell_exec($command);
                    $parsedResult1 = json_decode($result1, true);

                    // 將原始圖像旋轉 90 度後再次處理，可能有助於捕捉不同角度的文本
                    $rotatedImagePath = $imageDir . '/' . basename($imageFile, '.png') . '_rotated.png';
                    $rotateCmd = "convert {$safeImagePath} -rotate 90 {$rotatedImagePath}";
                    shell_exec($rotateCmd);

                    $safeRotatedImagePath = escapeshellarg($rotatedImagePath);
                    $command = "python {$pythonScript} {$safeRotatedImagePath} {$langParam}";
                    $result2 = shell_exec($command);
                    $parsedResult2 = json_decode($result2, true);

                    // 合併結果
                    $combinedText = "";
                    if ($parsedResult1 && isset($parsedResult1['success']) && $parsedResult1['success'] && isset($parsedResult1['text'])) {
                        $combinedText .= $parsedResult1['text'];
                    }

                    if ($parsedResult2 && isset($parsedResult2['success']) && $parsedResult2['success'] && isset($parsedResult2['text'])) {
                        $combinedText .= "\n" . $parsedResult2['text'];
                    }

                    // 創建合併的結果對象
                    $result = json_encode([
                        'success' => true,
                        'text' => $combinedText
                    ]);

                    error_log("合併處理結果長度: " . strlen($combinedText));
                }
            } else {
                // 非慶濟文件使用標準處理
                $command = "python {$pythonScript} {$safeImagePath} {$langParam}";
                $result = shell_exec($command);
            }

            // 解析結果
            $parsedResult = json_decode($result, true);
            if ($parsedResult && isset($parsedResult['success']) && $parsedResult['success'] && isset($parsedResult['text'])) {
                $ocrContent .= $parsedResult['text'] . "\n\n";
                error_log("PaddleOCR 成功處理圖像: " . $imageFile);
            } else {
                $errorMsg = isset($parsedResult['error']) ? $parsedResult['error'] : "未知錯誤";
                error_log("PaddleOCR 處理圖像失敗: " . $imageFile . ", 錯誤: " . $errorMsg);
            }
        }

        // 如果 PaddleOCR 提取的內容為空，嘗試使用原有的 Tesseract OCR
        if (empty($ocrContent) || strlen(trim($ocrContent)) < 50) {
            error_log("PaddleOCR 提取內容為空或太短，嘗試使用 Tesseract OCR");
            return self::performOCROnPDF($filePath);
        }

        // 後處理OCR結果，修正常見的辨識錯誤
        $processedContent = self::postProcessOCRText($ocrContent);
        error_log("PaddleOCR 成功處理圖像，原始內容長度: " . strlen($ocrContent) . ", 處理後長度: " . strlen($processedContent));

        return $processedContent;
    }

    /**
     * 對PDF進行OCR文字識別處理
     *
     * @param string $filePath PDF文件路徑
     * @return string 處理後的文本
     */
    public static function performOCROnPDF($filePath) {
        // 首先將PDF轉換為圖像
        $debugDir = __DIR__ . '/debug_pdfs';
        $fileBaseName = basename($filePath, '.pdf');
        $imageDir = $debugDir . '/images_' . $fileBaseName;

        if (!is_dir($imageDir)) {
            if (!mkdir($imageDir, 0777, true)) {
                error_log("無法為OCR創建圖像目錄: " . $imageDir);
                return "OCR處理失敗：無法創建圖像目錄";
            }
        }

        // 使用ImageMagick將PDF轉換為圖像 - 提高解析度並優化預處理
        $safeFilePath = escapeshellarg($filePath);
        $outputPattern = escapeshellarg($imageDir . '/page-%03d.png');

        // 慈濟公文的OCR特殊處理 - 擴大檢測條件
        $isTzuChiDocument = (strpos($filePath, '慈濟') !== false ||
                              strpos($fileBaseName, '慈濟') !== false ||
                              strpos($filePath, 'TZU-CHI') !== false ||
                              strpos($filePath, 'BUDDHIST COMPASSION RELIEF') !== false ||
                              strpos($fileBaseName, 'BUDDHIST COMPASSION RELIEF') !== false ||
                              strpos($filePath, '常慈') !== false ||
                              strpos($fileBaseName, '常慈') !== false ||
                              strpos($filePath, '慈志') !== false ||
                              strpos($fileBaseName, '慈志') !== false ||
                              strpos($filePath, '慈書字') !== false ||
                              strpos($fileBaseName, '慈書字') !== false ||
                              strpos($filePath, '財團法人') !== false ||
                              strpos($fileBaseName, '財團法人') !== false ||
                              strpos($filePath, '佛教') !== false ||
                              strpos($fileBaseName, '佛教') !== false);

        if ($isTzuChiDocument) {
            error_log("偵測到慈濟公文，使用特殊OCR處理程序: " . $filePath);
            // 使用更高解析度進行掃描，並進行圖像優化
            // -density 800: 設置更高解析度
            // -quality 100: 最高質量
            // -background white: 確保背景為白色
            // -alpha remove: 移除透明度通道
            // -sharpen 0x1.5: 增強銳銳度
            // -contrast-stretch 1%: 增強對比度
            // -normalize: 正規化圖像
            // -despeckle: 去除圖像中的小隱點
            // -deskew 40%: 自動精確的校正圖像傾斜
            error_log("使用增強的圖像處理參數處理慈濟文件: {$filePath}");

            // 對慶濟文件使用更強的圖像處理參數
            // -density 1500: 設置更高解析度
            // -quality 100: 最高質量
            // -background white: 確保背景為白色
            // -alpha remove: 移除透明度通道
            // -sharpen 0x3.0: 增強銳銳度
            // -contrast-stretch 3%: 增強對比度
            // -normalize: 正規化圖像
            // -despeckle: 去除圖像中的小隱點
            // -deskew 40%: 自動精確的校正圖像傾斜
            // -level 10%,90%: 調整圖像的層次
            // -unsharp 0x1.0: 增強銳銳度
            $convertCmd = "convert -density 1500 {$safeFilePath} -quality 100 -background white -alpha remove -sharpen 0x3.0 -contrast-stretch 3% -normalize -despeckle -deskew 40% -level 10%,90% -unsharp 0x1.0 {$outputPattern}";
            $convertOutput = shell_exec($convertCmd . " 2>&1");

            // 檢查轉換是否成功
            $imageFiles = glob($imageDir . '/page-*.png');
            if (!empty($imageFiles)) {
                error_log("慈濟公文PDF轉圖像成功，產生了 " . count($imageFiles) . " 個圖像文件");

                // 使用針對繁體中文和英文混合文本優化的OCR處理
                $ocrContent = '';
                // 使用更高級的引擎模式和更精確的頁面分割模式
                // --oem 1: 使用LSTM神經網絡引擎
                // --psm 6: 假設一個統一的文本區塊
                // -c preserve_interword_spaces=1: 保留詞間空格
                // -c tessedit_char_whitelist="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-+(),.:": 限制字符集
                $ocrOption = "-l chi_tra+eng --oem 1 --psm 3 -c preserve_interword_spaces=1 -c tessedit_pageseg_mode=3 -c textord_tabfind_find_tables=1 -c textord_tablefind_recognize_tables=1 -c tessedit_do_invert=0 -c tessedit_char_blacklist=\"\|~`\"";

                foreach ($imageFiles as $imageFile) {
                    $safeImagePath = escapeshellarg($imageFile);
                    $tempOutput = $imageDir . '/' . basename($imageFile, '.png') . "_tzuchi";

                    // 執行Tesseract
                    $ocrCmd = "tesseract {$safeImagePath} {$tempOutput} " . $ocrOption;
                    shell_exec($ocrCmd);

                    // 讀取生成的文本文件
                    $textFile = $tempOutput . '.txt';
                    if (file_exists($textFile)) {
                        $pageContent = file_get_contents($textFile);
                        $ocrContent .= $pageContent . "\n\n";
                    }
                }

                // 如果得到有效內容，先進行後處理再返回
                if (!empty($ocrContent) && strlen(trim($ocrContent)) > 100) {
                    // 後處理OCR結果，修正常見的辨識錯誤
                    $processedContent = self::postProcessOCRText($ocrContent);
                    error_log("慈濟公文OCR成功，原始內容長度: " . strlen($ocrContent) . ", 處理後長度: " . strlen($processedContent));
                    return $processedContent;
                }
            }
        }

        // 嘗試不同的預處理參數
        $conversionMethods = [
            // 方法1: 標準高解析度轉換
            "convert -density 400 {$safeFilePath} -quality 100 {$outputPattern}",

            // 方法2: 增強對比度
            "convert -density 400 {$safeFilePath} -normalize -contrast-stretch 0% -quality 100 {$outputPattern}",

            // 方法3: 黑白處理
            "convert -density 400 {$safeFilePath} -threshold 50% -quality 100 {$outputPattern}",

            // 方法4: 銳化處理
            "convert -density 400 {$safeFilePath} -sharpen 0x1.0 -quality 100 {$outputPattern}",

            // 方法5: 綜合處理
            "convert -density 400 {$safeFilePath} -normalize -threshold 60% -sharpen 0x1.0 -quality 100 {$outputPattern}"
        ];

        // 嘗試各種轉換方法，直到成功
        $conversionSuccess = false;
        foreach ($conversionMethods as $index => $convertCmd) {
            error_log("嘗試PDF轉圖像方法 #" . ($index + 1));
            $convertOutput = shell_exec($convertCmd . " 2>&1");

            // 檢查轉換是否成功
            $imageFiles = glob($imageDir . '/page-*.png');
            if (!empty($imageFiles)) {
                $conversionSuccess = true;
                error_log("PDF轉圖像成功，使用方法 #" . ($index + 1) . "，產生了 " . count($imageFiles) . " 個圖像文件");
                break;
            }

            // 如果轉換失敗，清理目錄，準備下一個方法
            if ($convertOutput && strpos($convertOutput, 'error') !== false) {
                error_log("ImageMagick轉換方法 #" . ($index + 1) . " 錯誤: " . $convertOutput);
                array_map('unlink', glob($imageDir . '/*'));
            }
        }

        if (!$conversionSuccess) {
            error_log("所有PDF轉圖像方法都失敗: " . $filePath);
            return "PDF解析失敗：無法將PDF轉換為圖像，文件需要手動處理";
        }

        // 檢查是否產生圖像文件
        $imageFiles = glob($imageDir . '/page-*.png');
        if (empty($imageFiles)) {
            error_log("沒有產生圖像文件，PDF可能為空或格式異常: " . $filePath);
            return "PDF解析失敗：未能產生圖像文件，文件需要手動處理";
        }

        // 使用Tesseract OCR處理每個圖像 - 嘗試不同的處理選項
        $ocrOptions = [
            // 選項1: 繁體中文 + 英文 (標準模式)
            "-l chi_tra+eng --oem 1 --psm 3 -c preserve_interword_spaces=1",

            // 選項2: 繁體中文 + 英文 (多語言版面分析)
            "-l chi_tra+eng --oem 1 --psm 1 -c preserve_interword_spaces=1",

            // 選項3: 繁體中文 + 英文 (預設為單列文本)
            "-l chi_tra+eng --oem 1 --psm 6 -c preserve_interword_spaces=1",

            // 選項4: 只使用繁體中文 (適合純中文文件)
            "-l chi_tra --oem 1 --psm 3 -c preserve_interword_spaces=1",

            // 選項5: 使用舊版OCR引擎 (可能對某些文件有效)
            "-l chi_tra+eng --oem 0 --psm 3 -c preserve_interword_spaces=1"
        ];

        // 用於存儲所有OCR結果
        $allOcrResults = [];

        // 嘗試不同的OCR選項
        foreach ($ocrOptions as $optionIndex => $ocrOption) {
            error_log("嘗試OCR選項 #" . ($optionIndex + 1) . ": " . $ocrOption);
            $ocrContent = '';

            foreach ($imageFiles as $imageFile) {
                $safeImagePath = escapeshellarg($imageFile);
                $tempOutput = $imageDir . '/' . basename($imageFile, '.png') . "_method" . ($optionIndex + 1);

                // 執行Tesseract
                $ocrCmd = "tesseract {$safeImagePath} {$tempOutput} " . $ocrOption;
                shell_exec($ocrCmd);

                // 讀取生成的文本文件
                $textFile = $tempOutput . '.txt';
                if (file_exists($textFile)) {
                    $pageContent = file_get_contents($textFile);
                    $ocrContent .= $pageContent . "\n\n";
                }
            }

            // 保存此OCR選項的結果
            $allOcrResults[$optionIndex] = $ocrContent;
            file_put_contents($debugDir . '/ocr_result_method' . ($optionIndex + 1) . '_' . $fileBaseName . '.txt', $ocrContent);

            // 如果得到有效內容，立即返回
            if (!empty($ocrContent) && strlen(trim($ocrContent)) > 100) {
                error_log("使用OCR選項 #" . ($optionIndex + 1) . " 成功提取內容，長度: " . strlen($ocrContent));
                return $ocrContent;
            }
        }

        // 如果所有選項都嘗試了，選擇最好的結果
        $bestOcrContent = '';
        $maxLength = 0;

        foreach ($allOcrResults as $optionIndex => $content) {
            $contentLength = strlen(trim($content));
            if ($contentLength > $maxLength) {
                $maxLength = $contentLength;
                $bestOcrContent = $content;
            }
        }

        // 如果有任何有效內容，返回
        if (!empty($bestOcrContent) && strlen(trim($bestOcrContent)) > 20) {
            error_log("使用最佳OCR結果，內容長度: " . strlen($bestOcrContent));
            return $bestOcrContent;
        }

        // 如果OCR處理仍然失敗，返回錯誤信息
        $errorMsg = "PDF解析失敗：文件需要手動編輯";
        error_log("所有OCR方法都失敗: " . $filePath);
        return $errorMsg;
    }

    /**
     * 提取說明區塊，尤其是數字編號的說明點
     *
     * @param string $content PDF內容
     * @return string|null 說明內容
     */
    public static function extractExplanationBlock($content) {
        // 先嘗試提取說明標題下的所有點
        if (preg_match('/說\s*明\s*[：:][\s\n\r]*((?:[一二三四五六七八九十][、.．][^\n\r]*[\n\r]*)+)/us', $content, $wholeMatch)) {
            $explanationContent = $wholeMatch[1];
            error_log("找到完整說明區塊: " . substr($explanationContent, 0, 100) . "...");

            // 從完整說明區塊中提取各點
            $allPoints = [];
            if (preg_match_all('/([一二三四五六七八九十]+)[、.．]([^\n\r]*(?:[\n\r]+(?![一二三四五六七八九十]+[、.．])[^\n\r]*)*)/us', $explanationContent, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $pointNumber = $match[1] . "、";
                    $pointContent = trim(preg_replace('/\s+/', ' ', $match[2]));
                    if (!empty($pointContent)) {
                        $allPoints[] = $pointNumber . " " . $pointContent;
                    }
                }

                if (count($allPoints) >= 1) {
                    error_log("從完整說明區塊提取到" . count($allPoints) . "點說明");
                    return implode("\n", $allPoints);
                }
            }
        }

        // 嘗試提取數字編號的說明點
        if (preg_match('/說\s*明\s*[：:][\s\n\r]*((?:\d+[、.．][^\n\r]*[\n\r]*)+)/us', $content, $wholeMatch)) {
            $explanationContent = $wholeMatch[1];
            error_log("找到數字編號的說明區塊: " . substr($explanationContent, 0, 100) . "...");

            // 從完整說明區塊中提取各點
            $allPoints = [];
            if (preg_match_all('/(\d+)[、.．]([^\n\r]*(?:[\n\r]+(?!\d+[、.．])[^\n\r]*)*)/us', $explanationContent, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $pointNumber = $match[1] . "、";
                    $pointContent = trim(preg_replace('/\s+/', ' ', $match[2]));
                    if (!empty($pointContent)) {
                        $allPoints[] = $pointNumber . " " . $pointContent;
                    }
                }

                if (count($allPoints) >= 1) {
                    error_log("從數字編號說明區塊提取到" . count($allPoints) . "點說明");
                    return implode("\n", $allPoints);
                }
            }
        }

        // 嘗試提取說明標題下的所有內容，直到下一個標題
        if (preg_match('/說\s*明\s*[：:]\s*(.+?)(?=\s*辦\s*法\s*[：:]|\s*正本|\s*副本|$)/us', $content, $matches)) {
            $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($explanation) && strlen($explanation) > 10) {
                error_log("從說明標題提取完整內容: " . substr($explanation, 0, 100) . "...");
                return $explanation;
            }
        }

        return null;
    }

    /**
     * 清理PDF中提取的文本，移除多餘的點號和空格模式
     *
     * @param string $text 要清理的文本
     * @return string 清理後的文本
     */
    public static function cleanExtractedText($text) {
        // 先保存可能的欄位分隔符號位置
        $separators = [];
        $patterns = ['/發文單位[：:]/u', '/受文者[：:]/u', '/發文日期[：:]/u', '/主旨[：:]/u', '/說明[：:]/u'];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
                $separators[] = $matches[0][1];
            }
        }

        // 移除連續的點號和空格模式 (如 ". . ." 或 ". . . .")
        $cleaned = preg_replace('/\.\s+\.\s+\.(\s+\.)*/', '', $text);

        // 移除行尾的點號和空格模式
        $cleaned = preg_replace('/\s*\.\s*\.\s*\.\s*$/', '', $cleaned);

        // 移除行尾的點號
        $cleaned = preg_replace('/\s*\.\s*$/', '', $cleaned);

        // 替換連續多個空格為單個空格，但保留欄位分隔符號前後的換行
        foreach ($separators as $pos) {
            // 在分隔符號前後10個字元範圍內保留換行
            $safeArea = 10;
            $start = max(0, $pos - $safeArea);
            $length = $safeArea * 2;
            $segment = substr($cleaned, $start, $length);
            $segmentPreserved = $segment;
            $cleaned = substr_replace($cleaned, $segmentPreserved, $start, $length);
        }

        // 最後統一處理連續空格
        $cleaned = preg_replace('/(?<![：:])\s{2,}(?![\r\n])/', ' ', $cleaned);

        return $cleaned;
    }


    /**
     * 從檔名提取可能的元資訊
     *
     * 支援的檔名格式:
     * 1. 日期_字號_標題.pdf: 20250306_國北都第1234號_關於建築申請案.pdf
     * 2. 字號_日期_標題.pdf: 國北都第1234號_20250306_關於建築申請案.pdf
     * 3. 標題_字號_日期.pdf: 關於建築申請案_國北都第1234號_20250306.pdf
     * 4. 通用時間戳格式: 1741494731_6490.pdf (保留原始時間戳)
     *
     * @param string $fileName 檔案名稱
     * @return array 提取的資訊
     */
    public static function extractInfoFromFilename($filename) {
        $info = [
            'document_number' => null,
            'date' => null,
            'sender' => null,
            'site' => null,
            'title' => null,
            'note' => null
        ];

        // 移除檔案副檔名和路徑
        $filename = pathinfo($filename, PATHINFO_FILENAME);

        // 移除前綴數字和時間戳
        $cleanName = preg_replace('/^\d+_\d+_\d+_*/', '', $filename);
        $cleanName = preg_replace('/^\d+_\d+_*/', '', $cleanName);
        $cleanName = preg_replace('/^\d+_*/', '', $cleanName);

        // 嘗試從檔名中提取文號
        if (preg_match('/字第(\d+)[號|号]*/u', $filename, $matches) ||
            preg_match('/[\(（](\d+)[號|号|）\)]*/u', $filename, $matches) ||
            preg_match('/^([A-Z0-9]{2,})[-_]?\d+/i', $cleanName, $matches)) {
            $info['document_number'] = $matches[1];
        }

        // 嘗試從檔名中提取日期
        // 中文年月日
        if (preg_match('/(\d{2,3})年(\d{1,2})月(\d{1,2})日/u', $filename, $matches)) {
            $year = intval($matches[1]) + 1911; // 民國年轉西元年
            $month = intval($matches[2]);
            $day = intval($matches[3]);

            // 檢查日期有效性
            if (checkdate($month, $day, $year)) {
                $info['date'] = sprintf('%04d-%02d-%02d', $year, $month, $day);
            }
        }
        // 數字日期格式
        else if (preg_match('/(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})/', $filename, $matches)) {
            $year = intval($matches[1]);
            $month = intval($matches[2]);
            $day = intval($matches[3]);

            // 檢查日期有效性
            if (checkdate($month, $day, $year)) {
                $info['date'] = sprintf('%04d-%02d-%02d', $year, $month, $day);
            }
        }

        // 嘗試從檔名中提取發文單位
        $senderPatterns = [
            '/來自[：:](.*?)[\s_-]/' => 1,
            '/發文者[：:](.*?)[\s_-]/' => 1,
            '/(.*?)[發文|來函]/' => 1,
            '/(協會|公司|機關|部門|中心|單位|委員會)/' => 0
        ];

        foreach ($senderPatterns as $pattern => $groupIndex) {
            if (preg_match($pattern, $filename, $matches) && isset($matches[$groupIndex])) {
                $potentialSender = trim($matches[$groupIndex]);
                // 避免提取到過短或過長的值
                if (mb_strlen($potentialSender, 'UTF-8') >= 2 && mb_strlen($potentialSender, 'UTF-8') <= 20) {
                    $info['sender'] = $potentialSender;
                    break;
                }
            }
        }

        // 嘗試提取工地信息
        $sitePatterns = [
            '/(美崙|臺東|台東|花蓮|台中|臺中|烏日|太平|豐原|北屯|西屯|南屯|東區|西區|中區|北區|小港|銅鑼|頭份|竹北|竹東|大里|鹿港|彰化|宜蘭|礁溪|太平|埔里|草屯)/' => 1
        ];

        foreach ($sitePatterns as $pattern => $groupIndex) {
            if (preg_match($pattern, $filename, $matches) && isset($matches[$groupIndex])) {
                $info['site'] = trim($matches[$groupIndex]);
                break;
            }
        }

        // 嘗試提取檔名作為標題
        if (mb_strlen($cleanName, 'UTF-8') > 0) {
            // 如果清理後還有內容，使用該內容作為標題
            $info['title'] = $cleanName;
        } else if (mb_strlen($filename, 'UTF-8') > 0) {
            // 否則使用原始檔名
            $info['title'] = $filename;
        }

        return $info;
    }

    /**
     * 使用 Google Gemini API 直接處理 PDF 文件
     *
     * @param string $filePath PDF 文件路徑
     * @return array 解析結果
     */
    public static function analyzeWithGemini($filePath) {
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'message' => '檔案不存在',
                'data' => [
                    'notes' => '檔案不存在，請重新上傳',
                    'ocr_failed' => true
                ]
            ];
        }

        // 記錄開始處理
        error_log("Gemini API: 開始處理PDF文件: " . $filePath);

        // 確保使用最新的API金鑰
        $api_key = self::$geminiOptions['api_key'];
        error_log("Gemini API: 使用API金鑰: " . substr($api_key, 0, 10) . "...");
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

        // 讀取PDF並轉為base64
        $pdf_content = base64_encode(file_get_contents($filePath));
        $pdf_size = strlen($pdf_content);
        error_log("Gemini API: PDF文件大小: " . $pdf_size . " bytes (base64編碼後)");

        // 對所有PDF文件都只處理第一頁
        error_log("Gemini API: 對所有PDF文件都只處理第一頁");

        // 建立臨時目錄
        $tempDir = __DIR__ . '/temp_pdf';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0777, true);
        }

        $tempFile = $tempDir . '/' . basename($filePath, '.pdf') . '_page1.pdf';
        $safeFilePath = escapeshellarg($filePath);
        $safeTempFile = escapeshellarg($tempFile);

        // 方法1: 使用pdftk提取第一頁
        $command = "pdftk $safeFilePath cat 1 output $safeTempFile";
        exec($command, $output, $returnVar);

        // 如果pdftk失敗，嘗試使用ghostscript
        if ($returnVar !== 0 || !file_exists($tempFile)) {
            error_log("Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript");
            $gsCommand = "gs -sDEVICE=pdfwrite -dFirstPage=1 -dLastPage=1 -sOutputFile=$safeTempFile -dBATCH -dNOPAUSE $safeFilePath";
            exec($gsCommand, $gsOutput, $gsReturnVar);

            // 如果ghostscript也失敗，嘗試使用qpdf
            if ($gsReturnVar !== 0 || !file_exists($tempFile)) {
                error_log("Gemini API: ghostscript提取第一頁失敗，嘗試使用qpdf");
                $qpdfCommand = "qpdf --pages $safeFilePath 1 -- $safeTempFile";
                exec($qpdfCommand, $qpdfOutput, $qpdfReturnVar);

                // 如果所有方法都失敗，嘗試使用原始文件
                if ($qpdfReturnVar !== 0 || !file_exists($tempFile)) {
                    error_log("Gemini API: 所有提取第一頁的方法都失敗，嘗試使用原始文件");

                    // 嘗試使用傳統方法提取文本
                    $textContent = self::extractTextFromPDF($filePath);

                    if (!empty($textContent) && strlen($textContent) > 100) {
                        error_log("Gemini API: 成功提取文本內容，長度: " . strlen($textContent));

                        // 嘗試使用文本內容與Gemini API互動
                        return self::analyzeTextWithGemini($textContent);
                    }

                    // 如果文本提取也失敗，返回失敗結果
                    return [
                        'success' => false,
                        'message' => '無法提取PDF內容，無法使用 Gemini API 處理',
                        'data' => [
                            'notes' => '無法提取PDF內容，需要手動編輯',
                            'ocr_failed' => true
                        ]
                    ];
                }
            }
        }

        // 如果成功提取第一頁
        if (file_exists($tempFile)) {
            $pdf_content = base64_encode(file_get_contents($tempFile));
            $pdf_size = strlen($pdf_content);
            error_log("Gemini API: 只處理第一頁後的大小: " . $pdf_size . " bytes");

            // 清理臨時文件
            unlink($tempFile);

            // 如果第一頁仍然太大，嘗試降低圖像質量
            if ($pdf_size > 1000000) {
                error_log("Gemini API: 第一頁仍然太大，嘗試降低圖像質量");

                // 嘗試使用傳統方法提取文本
                $textContent = self::extractTextFromPDF($filePath);

                if (!empty($textContent) && strlen($textContent) > 100) {
                    error_log("Gemini API: 成功提取文本內容，長度: " . strlen($textContent));

                    // 嘗試使用文本內容與Gemini API互動
                    return self::analyzeTextWithGemini($textContent);
                }
            }
        } else {
            error_log("Gemini API: 無法提取PDF第一頁，嘗試使用原始文件");

            // 嘗試使用傳統方法提取文本
            $textContent = self::extractTextFromPDF($filePath);

            if (!empty($textContent) && strlen($textContent) > 100) {
                error_log("Gemini API: 成功提取文本內容，長度: " . strlen($textContent));

                // 嘗試使用文本內容與Gemini API互動
                return self::analyzeTextWithGemini($textContent);
            }
        }

        // 構建 API 請求
        $promptText = '你是一個專業的文件分析助手，專門從PDF公文中提取關鍵信息。

請從此PDF中提取這些信息：
- 發文單位
- 受文者
- 發文字號
- 發文日期
- 主旨
- 說明

請以JSON格式返回結果，格式如下：
{
  "發文單位": "提取的發文單位",
  "受文者": "提取的受文者",
  "發文字號": "提取的發文字號",
  "發文日期": "提取的發文日期，如「112年10月18日」",
  "主旨": "提取的主旨",
  "說明": "提取的說明"
}

只返回JSON格式的結果，不要有其他文字。如果文件中有民國年日期，請直接提取原始格式，不要轉換為西元年。';

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $promptText
                        ],
                        [
                            'inline_data' => [
                                'mime_type' => 'application/pdf',
                                'data' => $pdf_content
                            ]
                        ]
                    ]
                ]
            ],
            'generation_config' => [
                'temperature' => 0.1,
                'max_output_tokens' => 2048
            ]
        ];

        // 設定HTTP選項
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'x-goog-api-key: ' . $api_key
                ],
                'content' => json_encode($data),
                'ignore_errors' => true,
                'timeout' => 60 // 增加超時時間為60秒
            ]
        ];

        // 發送請求
        error_log("Gemini API: 發送請求到 " . $url);
        $context = stream_context_create($options);

        try {
            $response = file_get_contents($url, false, $context);

            // 取得HTTP狀態碼
            $status_line = $http_response_header[0];
            preg_match('{HTTP/\S*\s(\d{3})}', $status_line, $match);
            $status = $match[1];

            error_log("Gemini API: 回應狀態碼: " . $status);

            // 處理回應
            $result = json_decode($response, true);

            if ($status == 200 && isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                $jsonText = $result['candidates'][0]['content']['parts'][0]['text'];
                error_log("Gemini API: 成功取得回應，內容長度: " . strlen($jsonText));

                // 嘗試解析JSON
                try {
                    $jsonData = json_decode($jsonText, true);

                    // 處理可能包含在代碼塊中的JSON
                    if (preg_match('/```json\s*(.+?)\s*```/s', $jsonText, $matches)) {
                        $jsonText = $matches[1];
                        $jsonData = json_decode($jsonText, true);
                        error_log("Gemini API: 從代碼塊中提取JSON");
                    }

                    if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                        // 成功解析JSON
                        error_log("Gemini API: 成功解析JSON回應");
                        return [
                            'success' => true,
                            'message' => '使用 Gemini API 成功解析PDF內容',
                            'data' => [
                                'document_number' => isset($jsonData['發文字號']) ? $jsonData['發文字號'] : null,
                                'title' => isset($jsonData['主旨']) ? $jsonData['主旨'] : null,
                                'sender' => isset($jsonData['發文單位']) ? $jsonData['發文單位'] : null,
                                'receiver' => isset($jsonData['受文者']) ? $jsonData['受文者'] : null,
                                'issue_date' => isset($jsonData['發文日期']) ? $jsonData['發文日期'] : null, // 直接使用Gemini提取的日期
                                'content_summary' => isset($jsonData['說明']) ? $jsonData['說明'] : null,
                                'notes' => isset($jsonData['說明']) ? $jsonData['說明'] : null,
                                'site' => null
                            ]
                        ];
                    } else {
                        error_log("Gemini API: JSON解析失敗: " . json_last_error_msg());
                        error_log("Gemini API: 原始回應: " . $jsonText);
                    }
                } catch (Exception $e) {
                    error_log("Gemini API: JSON解析異常: " . $e->getMessage());
                }

                // 如果JSON解析失敗，但仍然有文字回應
                return [
                    'success' => false,
                    'message' => 'Gemini API 回應格式不正確',
                    'data' => [
                        'notes' => $jsonText,
                        'ocr_failed' => false
                    ]
                ];
            } else {
                // API 請求失敗
                $errorMessage = isset($result['error']['message']) ? $result['error']['message'] : '未知錯誤';
                error_log("Gemini API: 請求失敗，錯誤訊息: " . $errorMessage);

                if (isset($result['error']['details'])) {
                    error_log("Gemini API: 錯誤詳細資訊: " . json_encode($result['error']['details']));
                }

                return [
                    'success' => false,
                    'message' => 'Gemini API 請求失敗: ' . $errorMessage,
                    'data' => [
                        'notes' => $errorMessage,
                        'ocr_failed' => true
                    ]
                ];
            }
        } catch (Exception $e) {
            error_log("Gemini API: 請求異常: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Gemini API 請求異常: ' . $e->getMessage(),
                'data' => [
                    'notes' => $e->getMessage(),
                    'ocr_failed' => true
                ]
            ];
        }
    }

    /**
     * 使用文本內容與Gemini API互動
     *
     * @param string $textContent 提取的文本內容
     * @return array 解析結果
     */
    public static function analyzeTextWithGemini($textContent) {
        error_log("Gemini API: 使用文本內容與Gemini API互動");

        // 確保使用最新的API金鑰
        $api_key = self::$geminiOptions['api_key'];
        error_log("Gemini API: 使用API金鑰: " . substr($api_key, 0, 10) . "...");
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

        // 構建 API 請求
        $promptText = '你是一個專業的文件分析助手，專門從PDF公文中提取關鍵信息。

以下是從PDF中提取的文本內容，請從中提取這些信息：
- 發文單位
- 受文者
- 發文字號
- 發文日期
- 主旨
- 說明

請以JSON格式返回結果，格式如下：
{
  "發文單位": "提取的發文單位",
  "受文者": "提取的受文者",
  "發文字號": "提取的發文字號",
  "發文日期": "提取的發文日期，如「112年10月18日」",
  "主旨": "提取的主旨",
  "說明": "提取的說明"
}

只返回JSON格式的結果，不要有其他文字。如果文件中有民國年日期，請直接提取原始格式，不要轉換為西元年。

文本內容：
' . $textContent;

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $promptText
                        ]
                    ]
                ]
            ],
            'generation_config' => [
                'temperature' => 0.1,
                'max_output_tokens' => 2048
            ]
        ];

        // 設定HTTP選項
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'x-goog-api-key: ' . $api_key
                ],
                'content' => json_encode($data),
                'ignore_errors' => true,
                'timeout' => 60 // 增加超時時間為60秒
            ]
        ];

        // 發送請求
        error_log("Gemini API: 發送文本請求到 " . $url);
        $context = stream_context_create($options);

        try {
            $response = file_get_contents($url, false, $context);

            // 取得HTTP狀態碼
            $status_line = $http_response_header[0];
            preg_match('{HTTP/\S*\s(\d{3})}', $status_line, $match);
            $status = $match[1];

            error_log("Gemini API: 回應狀態碼: " . $status);

            // 處理回應
            $result = json_decode($response, true);

            if ($status == 200 && isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                $jsonText = $result['candidates'][0]['content']['parts'][0]['text'];
                error_log("Gemini API: 成功取得回應，內容長度: " . strlen($jsonText));

                // 嘗試解析JSON
                try {
                    $jsonData = json_decode($jsonText, true);

                    // 處理可能包含在代碼塊中的JSON
                    if (preg_match('/```json\s*(.+?)\s*```/s', $jsonText, $matches)) {
                        $jsonText = $matches[1];
                        $jsonData = json_decode($jsonText, true);
                        error_log("Gemini API: 從代碼塊中提取JSON");
                    }

                    if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                        // 成功解析JSON
                        error_log("Gemini API: 成功解析JSON回應");
                        return [
                            'success' => true,
                            'message' => '使用 Gemini API 成功解析文本內容',
                            'data' => [
                                'document_number' => isset($jsonData['發文字號']) ? $jsonData['發文字號'] : null,
                                'title' => isset($jsonData['主旨']) ? $jsonData['主旨'] : null,
                                'sender' => isset($jsonData['發文單位']) ? $jsonData['發文單位'] : null,
                                'receiver' => isset($jsonData['受文者']) ? $jsonData['受文者'] : null,
                                'issue_date' => isset($jsonData['發文日期']) ? $jsonData['發文日期'] : null, // 直接使用Gemini提取的日期
                                'content_summary' => isset($jsonData['說明']) ? $jsonData['說明'] : null,
                                'notes' => isset($jsonData['說明']) ? $jsonData['說明'] : null,
                                'site' => null
                            ]
                        ];
                    } else {
                        error_log("Gemini API: JSON解析失敗: " . json_last_error_msg());
                        error_log("Gemini API: 原始回應: " . $jsonText);
                    }
                } catch (Exception $e) {
                    error_log("Gemini API: JSON解析異常: " . $e->getMessage());
                }

                // 如果JSON解析失敗，但仍然有文字回應
                return [
                    'success' => false,
                    'message' => 'Gemini API 回應格式不正確',
                    'data' => [
                        'notes' => $jsonText,
                        'ocr_failed' => false
                    ]
                ];
            } else {
                // API 請求失敗
                $errorMessage = isset($result['error']['message']) ? $result['error']['message'] : '未知錯誤';
                error_log("Gemini API: 請求失敗，錯誤訊息: " . $errorMessage);

                if (isset($result['error']['details'])) {
                    error_log("Gemini API: 錯誤詳細資訊: " . json_encode($result['error']['details']));
                }

                return [
                    'success' => false,
                    'message' => 'Gemini API 請求失敗: ' . $errorMessage,
                    'data' => [
                        'notes' => $errorMessage,
                        'ocr_failed' => true
                    ]
                ];
            }
        } catch (Exception $e) {
            error_log("Gemini API: 請求異常: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Gemini API 請求異常: ' . $e->getMessage(),
                'data' => [
                    'notes' => $e->getMessage(),
                    'ocr_failed' => true
                ]
            ];
        }
    }

    /**
     * 從檔名提取發文字號
     *
     * @param string $fileName 檔案名稱
     * @return string|null 發文字號
     */
    public static function extractDocumentNumberFromFilename($fileName) {
        if (preg_match('/([a-zA-Z\x{4e00}-\x{9fa5}]+(?:都|字|第)[a-zA-Z\x{4e00}-\x{9fa5}]+第?[0-9]+號)/u', $fileName, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * 擷取公文字號
     *
     * @param string $content PDF內容
     * @param string $filePath PDF文件路徑，用於提取檔名信息
     * @return string|null 公文字號
     */
    public static function extractDocumentNumber($content, $filePath = null) {
        // 慈濟基金會特定格式
        if (preg_match('/\((\d+)\)慈志營字第(\d+)號/u', $content, $matches)) {
            $year = $matches[1];
            $number = $matches[2];
            return "({$year})慈志營字第{$number}號";
        }

        // 增強專有公文字號格式識別
        $patterns = [
            // 標準公文格式
            '/發文字號[：:]\s*([^\r\n]+)/u',

            // 新增：長建美宗字等專屬字號格式
            '/發文字號[：:]\s*(長建美宗字第\s*\d+\s*號)/u',
            '/發文字號[：:]\s*([^\s]+字第[^\s]+號)/u',

            // 花蓮縣政府格式
            '/花蓮縣政府\s+([^\r\n]*?字第[^\r\n]*?號)/u',

            // 內政部營建署格式
            '/內政部營建署\s+([^\r\n]*?字第[^\r\n]*?號)/u',

            // 特定格式：字號在字串中
            '/((?:工程|規劃|預算|契約|文書|總務)字(?:第|)[a-zA-Z0-9\-]+(?:號|))/u',

            // 萬用模式 - 較寬鬆的比對方式
            '/((?:[^\s]+字第[0-9]+號)|(?:第[0-9]+號))/u'
        ];

        // 依序嘗試所有模式
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                // 清理結果
                $result = trim($matches[1]);
                // 排除異常結果
                if (mb_strlen($result, 'UTF-8') < 50) {
                    error_log("提取公文字號: " . $result);
                    return $result;
                }
            }
        }

        // 如果沒有通過正則找到，嘗試透過標記位置分析
        if (preg_match('/發文字號.{0,10}[：:]/u', $content)) {
            // 找到標記後的位置
            $pos = mb_strpos($content, '發文字號');
            if ($pos !== false) {
                // 取標記後的50個字元
                $segment = mb_substr($content, $pos, 50, 'UTF-8');

                // 從中找出可能的字號部分
                if (preg_match('/[:：]\s*([^\r\n]{3,20})/u', $segment, $matches)) {
                    $potentialNumber = trim($matches[1]);
                    if (strpos($potentialNumber, '字第') !== false || strpos($potentialNumber, '號') !== false) {
                        error_log("透過位置分析提取公文字號: " . $potentialNumber);
                        return $potentialNumber;
                    }
                }
            }
        }

        // 如果還是找不到，嘗試從檔名獲取
        if ($filePath) {
            $result = self::extractDocumentNumberFromFilename(basename($filePath));
            if ($result) {
                error_log("從檔名提取公文字號: " . $result);
                return $result;
            }
        }

        return null;
    }

    /**
     * 擷取主旨
     *
     * @param string $content PDF內容
     * @return string|null 標題或主旨
     */
    public static function extractTitle($content) {
        // 特殊處理 - 檢查是否是慈濟文件
        if (mb_strpos($content, "財團法人中華民國佛教慈濟慈善事業基金會") !== false ||
            mb_strpos($content, "TZU-CHI FOUNDATION") !== false ||
            mb_strpos($content, "BUDDHIST COMPASSION RELIEF") !== false ||
            mb_strpos($content, "慈志營字") !== false ||
            mb_strpos($content, "慈濟") !== false) {
            error_log("檢測到慈濟文件，嘗試提取主旨");

            // 嘗試提取主旨 - 針對慈濟文件的特殊格式
            if (preg_match('/主\\s*旨\\s*[：:]\\s*有關[「『](.+?)[」』](.+?)(?=\\s*說\\s*明\\s*[：:]|\\s*辦\\s*法\\s*[：:]|\\s*正本|\\s*副本|$)/us', $content, $matches)) {
                $subject = "有關「" . $matches[1] . "」" . trim(preg_replace('/\\s+/', ' ', $matches[2]));
                if (!empty($subject)) {
                    error_log("從慈濟文件提取主旨: " . $subject);
                    return $subject;
                }
            }

            // 嘗試提取主旨 - 針對其他「有關」開頭的主旨
            if (preg_match('/主\\s*旨\\s*[：:]\\s*有關(.+?)(?=\\s*說\\s*明\\s*[：:]|\\s*辦\\s*法\\s*[：:]|\\s*正本|\\s*副本|$)/us', $content, $matches)) {
                $subject = "有關" . trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從慈濟文件提取其他有關格式主旨: " . $subject);
                    return $subject;
                }
            }

            // 嘗試提取主旨 - 更寬鬆的模式
            if (preg_match('/主\\s*旨\\s*[：:]\\s*(.+?)(?=\\s*說\\s*明\\s*[：:]|\\s*辦\\s*法\\s*[：:]|\\s*正本|\\s*副本|$)/us', $content, $matches)) {
                $subject = trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從慈濟文件提取主旨: " . $subject);
                    return $subject;
                }
            }

            // 嘗試更寬鬆的匹配，處理可能的OCR錯誤
            if (preg_match('/主.{0,3}旨.{0,3}[：:].{0,5}(.+?)(?=.{0,5}說.{0,3}明|.{0,5}辦.{0,3}法|.{0,5}正本|.{0,5}副本|$)/us', $content, $matches)) {
                $subject = trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從慈濟文件使用寬鬆匹配提取主旨: " . $subject);
                    return $subject;
                }
            }
        }

        // 特殊處理 - 檢查是否是花蓮縣地方稅務局的文件
        if (mb_strpos($content, "花蓮縣地方稅務局") !== false ||
            (mb_strpos($content, "花蓮") !== false && mb_strpos($content, "稅務局") !== false)) {
            error_log("檢測到花蓮縣地方稅務局的文件，嘗試提取主旨");

            // 嘗試提取主旨 - 更精確的正則表達式，處理多行主旨
            if (preg_match('/主\\s*旨\\s*[：:]\\s*(.+?)(?=\\s*說\\s*明\\s*[：:]|\\s*辦\\s*法\\s*[：:]|\\s*正本|\\s*副本|$)/us', $content, $matches)) {
                $subject = trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從花蓮縣地方稅務局文件提取主旨: " . $subject);
                    return $subject;
                }
            }

            // 嘗試提取主旨，使用更寬鬆的模式，處理可能的格式問題
            if (preg_match('/主\\s*旨\\s*[：:]\\s*([^\\n\\r]*(?:\\n\\r?(?!說\\s*明|辦\\s*法|正本|副本)[^\\n\\r]*)*)/us', $content, $matches)) {
                $subject = trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從花蓮縣地方稅務局文件提取多行主旨: " . $subject);
                    return $subject;
                }
            }

            // 嘗試更寬鬆的匹配，處理可能的OCR錯誤
            if (preg_match('/主.{0,3}旨.{0,3}[：:].{0,5}(.+?)(?=.{0,5}說.{0,3}明|.{0,5}辦.{0,3}法|.{0,5}正本|.{0,5}副本|$)/us', $content, $matches)) {
                $subject = trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從花蓮縣地方稅務局文件使用寬鬆匹配提取主旨: " . $subject);
                    return $subject;
                }
            }

            // 嘗試提取主旨，使用更簡單的模式
            if (preg_match('/主\\s*旨\\s*[：:]\\s*(.+)/u', $content, $matches)) {
                $subject = trim(preg_replace('/\\s+/', ' ', $matches[1]));
                if (!empty($subject)) {
                    error_log("從簡單模式提取主旨: " . $subject);
                    return $subject;
                }
            }
        }

        // 先嘗試尋找標準格式的主旨標記和說明標記
        $hasSubjectMark = preg_match('/主\s*旨[：:]/u', $content);
        $hasExplanationMark = preg_match('/說\s*明[：:]/u', $content);

        // 特別處理慈濟工地協調會議紀錄格式
        if ($hasSubjectMark &&
            (strpos($content, '慈志營字') !== false ||
             strpos($content, '財團法人中華民國佛教慈濟慈善事業基金會') !== false ||
             strpos($content, 'BUDDHIST COMPASSION RELIEF') !== false ||
             strpos($content, 'TZU-CHI FOUNDATION') !== false) &&
            (strpos($content, '工地協調會議紀錄') !== false ||
             strpos($content, '營建處簡便行文表') !== false)) {

            // 識別項目名稱
            $projectName = null;
            if (preg_match('/有關(.*?(?:工程|新建工程))(?:（含設備）|\(含設備\))?/u', $content, $projectMatches)) {
                $projectName = trim($projectMatches[1]);
            }

            // 識別會議次數
            $meetingNumber = null;
            if (preg_match('/第([一二三四五六七八九十百零\d]+)次工地協調會/u', $content, $numberMatches)) {
                $meetingNumber = trim($numberMatches[1]);
            }

            // 識別是否包含"含設備"描述
            $includeEquipment = preg_match('/（含設備）|\(含設備\)/u', $content) ? "（含設備）" : "";

            // 如果是工地協調會議紀錄
            if (strpos($content, '工地協調會議紀錄') !== false && $projectName && $meetingNumber) {
                $subject = "有關{$projectName}{$includeEquipment}第{$meetingNumber}次工地協調會議紀錄，請查照。";
                error_log("智能提取慈濟工地協調會議主旨: " . $subject);
                return $subject;
            }

            // 如果是營建處簡便行文表
            if (strpos($content, '營建處簡便行文表') !== false) {
                // 嘗試直接提取主旨部分
                if (preg_match('/主\s*旨[\uff1a:].{0,5}(.+?)(?=\s*說\s*明[\uff1a:]|\s*辦\s*法[\uff1a:]|\s*正本|\s*副本|$)/us', $content, $matches)) {
                    $subject = trim(preg_replace('/\s+/', ' ', $matches[1]));
                    if (!empty($subject)) {
                        error_log("從營建處簡便行文表提取主旨: " . $subject);
                        return $subject;
                    }
                }

                // 如果有項目名稱，但沒有會議次數，創建一個一般性的主旨
                if ($projectName) {
                    $subject = "有關{$projectName}{$includeEquipment}，請查照。";
                    error_log("從營建處簡便行文表創建一般性主旨: " . $subject);
                    return $subject;
                }
            }
        }

        // 定義一個更精確的主旨提取函數，明確以「說明：」為界限
        if ($hasSubjectMark && $hasExplanationMark) {
            // 方法1：從「主旨：」到第一個句號，排除「詳說明」
            if (preg_match('/主\s*旨[：:]\s*([^。]*?(?:。|；))(?:\s*(?:詳說明|，詳說明)[^。]*)?/us', $content, $matches)) {
                $subject = trim(preg_replace('/\s+/', ' ', $matches[1]));
                $subject = rtrim($subject, '，'); // 移除尾部的逗號

                // 驗證提取結果是否合理
                if (!empty($subject) && mb_strlen($subject, 'UTF-8') > 5 && mb_strlen($subject, 'UTF-8') < 500) {
                    error_log("精確提取主旨（排除詳說明）: " . $subject);
                    return $subject;
                }
            }

            // 方法2：如果上面的方法失敗，嘗試更寬鬆的匹配
            if (preg_match('/主\s*旨[：:]\s*(.*?)(?=\s*說\s*明[：:]|\s*(?:詳說明|，詳說明))/us', $content, $matches)) {
                $subject = trim(preg_replace('/\s+/', ' ', $matches[1]));
                $subject = rtrim($subject, '，'); // 移除尾部的逗號

                if (!empty($subject) && mb_strlen($subject, 'UTF-8') > 5) {
                    error_log("精確提取主旨（限制在說明之前）: " . $subject);
                    return $subject;
                }
            }
        }

        // 有關特定類型公文的智能處理
        if ($hasSubjectMark && preg_match('/有關|關於|檢送/u', $content)) {
            // 尋找以「有關」或「關於」或「檢送」開頭的完整句子
            if (preg_match('/主\s*旨[：:]\s*(?:有關|關於|檢送)([^。]+?(?:。|；))/u', $content, $matches)) {
                $subject = trim(preg_replace('/\s+/', ' ', $matches[0]));
                $subject = preg_replace('/^主\s*旨[：:]\s*/', '', $subject);
                $subject = rtrim($subject, '，'); // 移除尾部的逗號

                if (!empty($subject)) {
                    error_log("智能提取特定類型主旨: " . $subject);
                    return $subject;
                }
            }
        }

        // 如果上述方法都失敗，嘗試提取主旨標記後的第一個完整句子
        if ($hasSubjectMark) {
            if (preg_match('/主\s*旨[：:]\s*([^。]+?(?:。|；))/u', $content, $matches)) {
                $subject = trim(preg_replace('/\s+/', ' ', $matches[1]));
                $subject = rtrim($subject, '，'); // 移除尾部的逗號
                if (!empty($subject)) {
                    error_log("提取主旨標記後的第一個完整句子: " . $subject);
                    return $subject;
                }
            }
        }

        return null;
    }

    /**
     * 擷取發文單位
     *
     * @param string $content PDF內容
     * @return string|null 發文單位
     */
    public static function extractSender($content) {
        error_log("開始提取發文單位，內容長度: " . strlen($content));
        // 特殊處理 - 檢查是否是許常吉建築師事務所的文件（常慈書字號）
        if (mb_strpos($content, "常慈書字") !== false ||
            mb_strpos($content, "常關書字") !== false ||
            mb_strpos($content, "常書字") !== false) {
            error_log("檢測到許常吉建築師事務所的文件（常慈書字號）");
            return "許常吉建築師事務所";
        }

        // 特殊處理 - 檢查是否是花蓮縣政府的文件
        if (mb_strpos($content, "花蓮縣政府") !== false ||
            (mb_strpos($content, "花蓮縣") !== false && mb_strpos($content, "府") !== false)) {
            error_log("檢測到花蓮縣政府的文件");
            return "花蓮縣政府";
        }

        // 特殊處理 - 檢查是否是花蓮縣地方稅務局的文件
        if (mb_strpos($content, "花蓮縣地方稅務局") !== false ||
            (mb_strpos($content, "花蓮") !== false && mb_strpos($content, "稅務局") !== false)) {
            error_log("檢測到花蓮縣地方稅務局的文件");
            return "花蓮縣地方稅務局";
        }


        // 首先檢查內容是否為空
        if (empty($content)) {
            error_log("提取發文單位失敗: 內容為空");
            return null;
        }

        // 保存前1000個字符用於調試
        $debugContent = mb_substr($content, 0, 1000, 'UTF-8');
        error_log("發文單位提取內容前1000字符: " . $debugContent);

        // 預處理 - 將常見的全形符號轉換為半形符號，以提高匹配率
        $content = str_replace('：', ':', $content);

        // 先檢查慈濟基金會特殊格式
        if ((mb_strpos($content, '財團法人中華民國佛教慈濟慈善事業基金會') !== false ||
             mb_strpos($content, '財團法人佛教慈濟慈善事業基金會') !== false ||
             mb_strpos($content, 'BUDDHIST COMPASSION RELIEF') !== false ||
             mb_strpos($content, 'TZU-CHI FOUNDATION') !== false) &&
            mb_strpos($content, '營建處') !== false) {
            error_log("匹配到特殊格式：慈濟基金會營建處");
            return '財團法人中華民國佛教慈濟慈善事業基金會';
        }

        // 直接檢查建築師事務所格式 - 改進匹配模式
        if (preg_match('/([\p{Han}\w]+\s*建築師事務所)/u', $content, $architectMatches)) {
            $sender = trim($architectMatches[1]);
            // 清理結果，去除可能的雜質
            $sender = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $sender);
            $sender = trim($sender);
            // 移除中間的空格
            $sender = preg_replace('/\s+/', '', $sender);
            error_log("直接匹配到建築師事務所: " . $sender);
            return $sender;
        }

        // 特別檢查許常吉建築師事務所
        if (mb_strpos($content, '許') !== false &&
            mb_strpos($content, '常') !== false &&
            mb_strpos($content, '吉') !== false &&
            mb_strpos($content, '建築師') !== false &&
            mb_strpos($content, '事務所') !== false) {
            error_log("匹配到特殊格式：許常吉建築師事務所");
            return '許常吉建築師事務所';
        }

        // 特別檢查許常吉建築師事務所書函
        if (mb_strpos($content, '許常吉建築師事務所') !== false ||
            mb_strpos($content, '許常吉建築師') !== false ||
            (mb_strpos($content, '許常吉') !== false && mb_strpos($content, '書函') !== false)) {
            error_log("匹配到特殊格式：許常吉建築師事務所書函");
            return '許常吉建築師事務所';
        }

        // 直接尋找發文單位標記 - 最優先處理
        $senderPatterns = [
            // 基本模式
            '/發文單位[：:]\s*([^\r\n]+)/u',
            '/發文機關[：:]\s*([^\r\n]+)/u',
            '/發文者[：:]\s*([^\r\n]+)/u',

            // 空格分隔的模式
            '/發文單位\s+([^\r\n]+?)(?=\s+受文|$)/u',

            // 抬頭與文號之間的模式
            '/^(.+?)\s*(?:函|書|令|呈|報告|公告|開會通知單)\s*(?:文號)?[：:]/um',

            // 特殊格式
            '/^([\p{Han}\w]+?(?:府|廳|局|處|會|部|中心|司|公所|署|法院|檢察署))[函書]/um'
        ];

        foreach ($senderPatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $sender = trim($matches[1]);
                // 清理格式並防止過長
                if (!empty($sender) && mb_strlen($sender, 'UTF-8') < 100) {
                    $sender = preg_replace('/\s+/', ' ', $sender);
                    // 去除前導的點、逗號等符號
                    $sender = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $sender);
                    $sender = trim($sender, ",:：;；,.。、，　 \t\n\r\0\x0B");
                    error_log("使用標記模式提取發文單位: " . $sender);
                    return $sender;
                }
            }
        }

        // 特殊處理慈濟文件 - 從"財團法人"和"營建處"組合提取
        if (mb_strpos($content, '財團法人中華民國佛教慈濟慈善事業基金會') !== false &&
            mb_strpos($content, '營建處') !== false) {
            error_log("特殊處理慈濟文件：財團法人中華民國佛教慈濟慈善事業基金會 營建處");
            return '財團法人中華民國佛教慈濟慈善事業基金會 營建處';
        }

        // 特定單位名稱的完整匹配
        $specificOrganizations = [
            '財團法人中華民國佛教慈濟慈善事業基金會',
            '財團法人佛教慈濟慈善事業基金會',
            '佛教慈濟慈善事業基金會',
            '花蓮縣政府',
            '花蓮縣地方稅務局',
            '內政部營建署',
            '臺灣臺北地方檢察署',
            '臺灣花蓮地方檢察署',
            '台灣自來水股份有限公司',
            '台灣電力股份有限公司',
            '國家住宅及都市更新中心'
        ];

        foreach ($specificOrganizations as $org) {
            if (mb_strpos($content, $org) !== false) {
                error_log("匹配到特定單位: " . $org);
                return $org;
            }
        }

        // 財團法人格式匹配
        if (preg_match('/(財團法人[^，。\r\n]+?基金會)/u', $content, $matches)) {
            $sender = trim($matches[1]);
            error_log("匹配到財團法人格式: " . $sender);
            return $sender;
        }

        // 特定處理花蓮縣地方稅務局
        if (mb_strpos($content, '花蓮縣地方稅務局') !== false) {
            error_log("找到發文單位: 花蓮縣地方稅務局");
            return '花蓮縣地方稅務局';
        }

        // 檔頭處理，避免取到檔號等非單位資訊
        if (preg_match('/(.+?)[函書]/u', $content, $matches)) {
            $sender = trim($matches[1]);
            // 移除可能混入的檔號和保存年限資訊
            $sender = preg_replace('/檔\s*號[：:].+?保存年限[：:].+?/u', '', $sender);
            // 去除前導的點、逗號等符號
            $sender = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $sender);
            $sender = trim($sender);
            error_log("從檔頭提取發文單位: " . $sender);
            return trim($sender);
        }

        // 嘗試匹配常見政府單位格式
        $govPatterns = [
            '/^([\p{Han}\w]+?(?:府|廳|局|處|會|部|中心|司|公所|署))/mu',
            '/([\p{Han}\w]+?(?:政府|縣政府|市政府|地方法院|地方檢察署))/u',
            '/((?:內政部|交通部|經濟部|教育部|財政部|國防部).+?(?:署|處|司|局|中心|辦公室))/u',
        ];

        foreach ($govPatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $sender = trim($matches[1]);
                if (!empty($sender)) {
                    // 去除前導的點、逗號等符號
                    $sender = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $sender);
                    $sender = trim($sender);
                    error_log("匹配到政府單位格式: " . $sender);
                    return $sender;
                }
            }
        }

        // 嘗試匹配常見企業格式
        if (preg_match('/([^\s，。；,;]{2,20}(?:股份有限公司|有限公司|公司|企業|建設|建築師事務所))/u', $content, $matches)) {
            $sender = trim($matches[1]);
            if (!preg_match('/(受文|收文|主旨|說明|辦法|附件)/u', $sender)) {
                // 去除前導的點、逗號等符號
                $sender = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $sender);
                $sender = trim($sender);
                error_log("匹配到企業格式: " . $sender);
                $sender = self::postProcessSender($sender);
                return $sender;
            }
        }

        // 如果所有方法都失敗，嘗試從文件內容關鍵詞判斷
        if (strpos($content, '慈濟') !== false || strpos($content, '常慈') !== false || strpos($content, '慈志') !== false || strpos($content, '慈書字') !== false) {
            error_log("從內容關鍵詞判斷為慈濟相關公文");
            return self::postProcessSender('財團法人中華民國佛教慈濟慈善事業基金會');
        }

        return null;
    }

    /**
     * 擷取受文者
     *
     * @param string $content PDF內容
     * @return string|null 受文者名稱
     */
    public static function extractReceiver($content) {
        error_log("開始提取受文者，內容長度: " . strlen($content));
        // 特殊處理 - 檢查是否是慈濟基金會發文給特定公司的文件
        if (mb_strpos($content, "財團法人中華民國佛教慈濟慈善事業基金會") !== false) {
            // 尋找文件中提到的公司名稱
            if (preg_match('/([^\s\n\r,，;；]{2,20}(?:股份有限公司|有限公司|公司|營造))/u', $content, $companyMatches)) {
                $company = trim($companyMatches[1]);
                error_log("檢測到慈濟基金會發文給" . $company . "的文件");
                return $company;
            }
        }

        // 特殊處理 - 檢查正本是否包含良有營造
        if (preg_match('/正本[：:]\s*([^\r\n]*?)良有營造/u', $content)) {
            error_log("從正本中找到良有營造");
            return $matches[2];
        }


        // 首先檢查內容是否為空
        if (empty($content)) {
            error_log("提取受文者失敗: 內容為空");
            return null;
        }

        // 保存前1000個字符用於調試
        $debugContent = mb_substr($content, 0, 1000, 'UTF-8');
        error_log("受文者提取內容前1000字符: " . $debugContent);

        // 預處理 - 將常見的全形符號轉換為半形符號，以提高匹配率
        $content = str_replace(['：', '　'], [':', ' '], $content);

        // 特殊處理 - 如果發現說明中包含依良有營造等字樣，立即返回良有營造
        if (preg_match('/(?:依|復|據|函復)(?:\s*)良有營造(?:股份有限公司)?(?:\s*)(?:\d{3}|\d{1,3}年)/u', $content)) {
            error_log("從說明中依據關鍵詞找到受文者: 良有營造股份有限公司");
            return $matches[2];
        }

        // 特殊處理 - 如果是長建美崙的文書，檢查是否是給良有營造的
        if (preg_match('/長建美崙字第\s*\d+\s*號/u', $content) &&
            preg_match('/良有營造/u', $content)) {
            error_log("從長建美崙文書中找到受文者: 良有營造股份有限公司");
            return $matches[2];
        }

        // 極高優先級 - 從標準格式提取，特別處理正本受文者格式
        $highPriorityPatterns = [
            // 處理正本受文者格式，確保只匹配公司名稱部分
            '/正本受文者[：:]\s*([^：\r\n]{2,40})(?=\s*發文|$)/u',
            '/正本受文者[：:]\s*([^\s]{2,40}[^\s]{0,2}公司)/u',

            // 標準受文者格式，限制匹配範圍避免包含發文日期
            '/受文者[：:]\s*([^：\r\n]{2,40})(?=\s*發文|$)/u',
            '/受文者[：:]\s*([^\s]{2,40}[^\s]{0,2}公司)/u',

            // 空格分隔或直接相鄰格式
            '/受文者\s+([^,，;；\r\n]{2,40})(?=\s*發文|$)/u',
            '/受\s*文\s*者[：:]\s*([^,，;；\r\n]{2,40})(?=\s*發文|$)/u',
            '/受\s*文\s*者\s+([^,，;；\r\n]{2,40})(?=\s*發文|$)/u',
        ];

        foreach ($highPriorityPatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $receiver = trim($matches[1]);

                // 特殊清理 - 移除任何日期格式和不相關內容
                $receiver = preg_replace('/\s*\d{1,3}年\d{1,2}月\d{1,2}日.*$/u', '', $receiver);
                $receiver = preg_replace('/\s*發文日期.*$/u', '', $receiver);
                $receiver = preg_replace('/\s+附件.*$/u', '', $receiver);

                // 清理結果，去除前導和尾部的雜質
                $receiver = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $receiver);
                $receiver = trim($receiver);

                // 確保結果是合理的公司名稱
                if (!empty($receiver) &&
                    $receiver != '如行文單位' &&
                    $receiver != '如正副本' &&
                    mb_strlen($receiver, 'UTF-8') < 40 &&
                    mb_strlen($receiver, 'UTF-8') > 2 &&
                    !preg_match('/發文日期|[：:]\d+年|\d+月|\d+日/u', $receiver)) {

                    error_log("從標準格式提取受文者(高優先級): " . $receiver);
                    return $receiver;
                }
            }
        }

        // 直接檢查是否包含明確的營造公司名稱
        $companyNames = [
            '良有營造股份有限公司',
            '良有營造',
            '富宇營造股份有限公司',
            '禾棟營造股份有限公司',
            '永聖營造股份有限公司'
        ];

        foreach ($companyNames as $company) {
            if (mb_strpos($content, $company) !== false) {
                error_log("直接匹配到公司名稱: " . $company);

                // 如果是簡稱"良有營造"，返回全稱
                if ($company === '良有營造') {
                    return '良有營造股份有限公司';
                }

                return $company;
            }
        }

        // 從說明部分尋找可能的受文者
        if (preg_match('/說明[：:](.*?)(?=\s*辦法[：:]|正本|副本|$)/us', $content, $explanationMatches)) {
            $explanation = $explanationMatches[1];

            // 檢查說明中是否提到某公司函文
            if (preg_match('/([^\s，。]{2,15}(?:公司|營造))(?:.*?)(?:函|文號|來文)/u', $explanation, $companyMatches)) {
                $potentialReceiver = trim($companyMatches[1]);
                if (mb_strlen($potentialReceiver, 'UTF-8') >= 3) {
                    error_log("從說明中提取可能的受文者: " . $potentialReceiver);

                    // 如果是包含"良有"的簡稱，返回全稱
                    if (mb_strpos($potentialReceiver, '良有') !== false) {
                        return '良有營造股份有限公司';
                    }

                    return $potentialReceiver;
                }
            }

            // 特別檢查說明中是否提到許常吉建築師事務所
            if (mb_strpos($explanation, '許常吉建築師事務所') !== false ||
                (mb_strpos($explanation, '許常吉') !== false && mb_strpos($explanation, '建築師') !== false)) {
                error_log("從說明中找到許常吉建築師事務所");
                return '良有營造股份有限公司';
            }
        }

        // 從主旨中尋找提到的公司名稱，特別是包含「因變更」「申請」等關鍵詞前的公司
        if (preg_match('/主\s*旨[：:]\s*.*?[「『](.*?(?:股份有限公司|有限公司|公司))[」』].*?(?:因變更|申請|函送)/us', $content, $matches)) {
            $receiver = trim($matches[1]);
            if (!empty($receiver) && mb_strlen($receiver, 'UTF-8') > 2) {
                error_log("從主旨中提取受文者: " . $receiver);
                return $receiver;
            }
        }

        // 檢查主旨是否包含檢送/函送給某公司
        if (preg_match('/主\s*旨[：:]\s*(?:檢送|函送|送|函覆).*?(?:予|給|至|向)\s*([^\s，。]{2,15}(?:公司|營造))/u', $content, $matches)) {
            $receiver = trim($matches[1]);
            if (!empty($receiver) && mb_strlen($receiver, 'UTF-8') > 2) {
                error_log("從主旨檢送/函送關係提取受文者: " . $receiver);

                // 如果是包含"良有"的簡稱，返回全稱
                if (mb_strpos($receiver, '良有') !== false) {
                    return '良有營造股份有限公司';
                }

                return $receiver;
            }
        }

        // 從說明中尋找文號的提取公司名稱
        // 例如：良營住字第1140214501號 -> 良有營造
        if (preg_match('/([良富禾永力][^字]{0,6})(?:營|建)(?:[^第]{0,5})字第\d+號/u', $content, $matches)) {
            $companyPrefix = trim($matches[1]);
            // 根據前綴推測公司名稱
            $companyMap = [
                '良' => '良有營造股份有限公司',
                '良營' => '良有營造股份有限公司',
                '良茂' => '良茂營造股份有限公司',
                '富' => '富宇營造股份有限公司',
                '富宇' => '富宇營造股份有限公司',
                '禾' => '禾棟營造股份有限公司',
                '禾棟' => '禾棟營造股份有限公司',
                '永' => '永聖營造股份有限公司',
                '永聖' => '永聖營造股份有限公司',
                '力' => '力麗營造股份有限公司',
                '力麗' => '力麗營造股份有限公司'
            ];

            foreach ($companyMap as $prefix => $company) {
                if (mb_strpos($companyPrefix, $prefix) !== false) {
                    error_log("從文號前綴「{$companyPrefix}」推測受文者: " . $company);
                    return $company;
                }
            }
        }

        // 尋找「貴公司」，通常表示這是發給特定公司的文件
        if (mb_strpos($content, '貴公司') !== false) {
            // 尋找可能在附近提到的公司名稱
            if (preg_match('/(?:貴公司|請|函復).*?([^，。；\r\n]*?(?:股份有限公司|有限公司|公司))/u', $content, $matches)) {
                $receiver = trim($matches[1]);
                if (!empty($receiver) && mb_strlen($receiver, 'UTF-8') > 2) {
                    error_log("從「貴公司」上下文提取受文者: " . $receiver);
                    return $receiver;
                }
            }

            // 如果找到「貴公司」但無法確定具體哪家公司，檢查是否與良有營造相關
            if (mb_strpos($content, '良有') !== false ||
                mb_strpos($content, '良營') !== false ||
                preg_match('/良.{0,2}(?:營造|公司)/u', $content)) {
                error_log("從「貴公司」上下文推測受文者為良有營造");
                return '良有營造股份有限公司';
            }

            // 如果找到「貴公司」但無法確定具體哪家公司，可以從整個文檔中找最可能的公司名稱
            if (preg_match_all('/([^，。；\r\n]*?(?:股份有限公司|有限公司|公司))/u', $content, $allMatches)) {
                foreach ($allMatches[1] as $potentialReceiver) {
                    $trimmed = trim($potentialReceiver);
                    // 排除發文單位和一些常見的不是受文者的公司名稱
                    if (!empty($trimmed) &&
                        mb_strlen($trimmed, 'UTF-8') > 4 &&
                        mb_strpos($content, $trimmed) !== false &&
                        !preg_match('/(國家住宅及都市更新中心|財團法人|本公司|發文單位)/u', $trimmed)) {
                        error_log("從文檔中提取最可能的受文者公司: " . $trimmed);
                        return $trimmed;
                    }
                }
            }
        }

        // 嘗試從"函"和"主旨"之間提取
        if (preg_match('/函\s*([^主旨]+?)主旨/us', $content, $matches)) {
            $potentialReceiver = trim($matches[1]);
            // 清理可能包含的發文日期等信息
            $potentialReceiver = preg_replace('/\s*發文日期.*$/u', '', $potentialReceiver);
            if (mb_strlen($potentialReceiver, 'UTF-8') < 40 && mb_strlen($potentialReceiver, 'UTF-8') > 2) {
                error_log("從函和主旨之間提取可能的受文者: " . $potentialReceiver);
                return $potentialReceiver;
            }
        }

        // 嘗試匹配通用格式：XXX公司/單位
        if (preg_match('/([^\s，。；,;]{2,20}(?:股份有限公司|有限公司|公司|企業|機構|單位|廠商))/u', $content, $matches)) {
            $potentialReceiver = trim($matches[1]);
            if (!preg_match('/(發文單位|本公司|本單位|主辦單位|協辦單位|國家住宅)/u', $potentialReceiver)) {
                error_log("從通用企業格式提取可能的受文者: " . $potentialReceiver);
                return $potentialReceiver;
            }
        }

        // 最後檢查：如果說明部分包含良有營造
        if (preg_match('/說明[：:](.*?)(?=\s*辦法[：:]|正本|副本|$)/us', $content, $explanationMatches)) {
            $explanation = $explanationMatches[1];
            if (preg_match('/([^\s\n\r,，;；]{2,20}(?:股份有限公司|有限公司|公司|營造))/u', $explanation, $companyMatches)) {
                $company = trim($companyMatches[1]);
                error_log("最後檢查：從說明部分發現公司關鍵詞: " . $company);
                return $company;
            }
        }

        // 檢查整個文件是否是慈濟基金會發給良有營造的
        if (preg_match('/(?:慈濟|佛教慈濟|TZU-CHI FOUNDATION)/ui', $content) &&
            preg_match('/([^\s\n\r,，;；]{2,20}(?:股份有限公司|有限公司|公司|營造))/u', $content, $companyMatches)) {
            $company = trim($companyMatches[1]);
            error_log("從慈濟基金會文件中推測受文者為" . $company);
            return $company;
        }

        error_log("無法提取受文者");
        return null;
    }

    /**
     * 擷取發文日期
     *
     * @param string $content PDF內容
     * @return string|null 發文日期 (YYYY-MM-DD)
     */
    public static function extractIssueDate($content) {
        error_log("開始提取發文日期，內容長度: " . strlen($content));

        // 首先檢查內容是否為空
        if (empty($content)) {
            error_log("提取發文日期失敗: 內容為空");
            return date('Y-m-d'); // 返回當前日期作為後備
        }

        // 保存前1000個字符用於調試
        $debugContent = mb_substr($content, 0, 1000, 'UTF-8');
        error_log("發文日期提取內容前1000字符: " . $debugContent);

        $issueDate = null;

        // 特別檢查文件中是否包含“112年10月18日”的模式
        if (preg_match('/([0-9]{3})[年\/\-\.]([0-9]{1,2})[月\/\-\.]([0-9]{1,2})[日]/u', $content, $matches)) {
            // 民國年轉西元年
            $year = intval($matches[1]) + 1911;
            $month = intval($matches[2]);
            $day = intval($matches[3]);

            // 確保日期有效
            if (checkdate($month, $day, $year)) {
                $issueDate = sprintf('%04d-%02d-%02d', $year, $month, $day);
                error_log("從文件中直接提取民國年日期: {$matches[1]}年{$matches[2]}月{$matches[3]}日 -> {$issueDate}");
                return $issueDate;
            }
        }

        // 特別檢查文件中是否包含“112年10月18”的模式（無日字）
        if (preg_match('/([0-9]{3})[年\/\-\.]([0-9]{1,2})[月\/\-\.]([0-9]{1,2})(?!日)/u', $content, $matches)) {
            // 民國年轉西元年
            $year = intval($matches[1]) + 1911;
            $month = intval($matches[2]);
            $day = intval($matches[3]);

            // 確保日期有效
            if (checkdate($month, $day, $year)) {
                $issueDate = sprintf('%04d-%02d-%02d', $year, $month, $day);
                error_log("從文件中直接提取民國年日期(無日字): {$matches[1]}年{$matches[2]}月{$matches[3]} -> {$issueDate}");
                return $issueDate;
            }
        }

        // 優先找尋發文日期標記
        $datePatterns = [
            // 標準格式帶標記
            '/發文日期[：:]\s*(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/u',
            '/發文日期[：:]\s*(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})/u',
            '/發文日期[：:]\s*中華民國[\s]*(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/u',

            // 標準格式不帶標記
            '/中華民國[\s]*(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/u',

            // 空格分隔的日期標記
            '/發文日期\s+(\d{2,3})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*[日號]/u',

            // 行首標記模式
            '/^[\s]*發文日期[：:]\s*(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/mu',

            // 西元日期模式
            '/發文日期[：:]\s*(\d{4})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/u'
        ];

        foreach ($datePatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                try {
                    // 根據匹配的格式處理日期
                    if (strlen($matches[1]) <= 3 && intval($matches[1]) < 200) {
                        // 民國年轉西元年
                        $year = intval($matches[1]) + 1911;
                    } else {
                        $year = intval($matches[1]);
                    }

                    $month = intval($matches[2]);
                    $day = intval($matches[3]);

                    // 驗證日期合法性
                    if ($year >= 1911 && $year <= 2100 && $month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                        // 使用 DateTime 驗證日期
                        $date = new DateTime();
                        $date->setDate($year, $month, $day);
                        $issueDate = $date->format('Y-m-d');
                        error_log("從標記模式提取發文日期: " . $issueDate);
                        return $issueDate;
                    }
                } catch (Exception $e) {
                    error_log("日期格式錯誤: " . $e->getMessage());
                    continue; // 繼續嘗試其他模式
                }
            }
        }

        // 如果標記提取失敗，嘗試通用日期模式
        $generalPatterns = [
            // 民國年日期格式：111年5月12日
            '/(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*日/u',

            // 數字日期格式：2022-05-12, 2022/05/12
            '/(\d{4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,2})/',

            // 年月日個別出現的模式
            '/[發文日期|日期][\s]*[:：][\s]*([\d]{2,3})[\s]*年[\s]*([\d]{1,2})[\s]*月[\s]*([\d]{1,2})[\s]*[日號]/u',

            // 中文前綴日期
            '/民國[\s]*(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/u',

            // 獨立行日期格式
            '/^[\s]*(\d{2,3})[\s]*年[\s]*(\d{1,2})[\s]*月[\s]*(\d{1,2})[\s]*[日號]/mu'
        ];

        foreach ($generalPatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                if (count($matches) >= 4) {
                    try {
                        // 根據匹配的格式處理日期
                        if (strlen($matches[1]) <= 3 && intval($matches[1]) < 200) {
                            // 民國年轉西元年
                            $year = intval($matches[1]) + 1911;
                        } else {
                            $year = intval($matches[1]);
                        }

                        $month = intval($matches[2]);
                        $day = intval($matches[3]);

                        // 驗證日期合法性
                        if ($year >= 1911 && $year <= 2100 && $month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                            // 使用 DateTime 驗證日期
                            $date = new DateTime();
                            $date->setDate($year, $month, $day);

                            // 檢查是否為未來日期
                            $now = new DateTime();
                            if ($date > $now && $date->diff($now)->days > 30) {
                                // 太遠的未來日期可能是錯誤的，使用當前日期
                                error_log("發現未來日期，使用當前日期代替: " . $date->format('Y-m-d'));
                                $issueDate = date('Y-m-d');
                            } else {
                                $issueDate = $date->format('Y-m-d');
                                error_log("從通用模式提取發文日期: " . $issueDate);
                            }

                            break;
                        }
                    } catch (Exception $e) {
                        error_log("日期轉換錯誤: " . $e->getMessage() . ", 原始匹配: " . print_r($matches, true));
                        // 發生錯誤時繼續嘗試其他模式
                        continue;
                    }
                }
            }
        }

        // 如果仍然無法提取日期，嘗試使用數字序列作為日期
        if (empty($issueDate)) {
            // 尋找任何像是年月日的序列
            if (preg_match('/[^\d](\d{8})[^\d]/u', $content, $matches)) {
                $dateString = $matches[1];
                $year = intval(substr($dateString, 0, 4));
                $month = intval(substr($dateString, 4, 2));
                $day = intval(substr($dateString, 6, 2));

                if ($year >= 1911 && $year <= 2100 && $month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                    try {
                        $date = new DateTime();
                        $date->setDate($year, $month, $day);
                        $issueDate = $date->format('Y-m-d');
                        error_log("從數字序列提取發文日期: " . $issueDate);
                    } catch (Exception $e) {
                        error_log("數字序列日期轉換錯誤: " . $e->getMessage());
                    }
                }
            }
        }

        // 如果無法提取日期，使用當前日期
        if (empty($issueDate)) {
            $issueDate = date('Y-m-d');
            error_log("無法提取發文日期，使用當前日期: " . $issueDate);
        }

        return $issueDate;
    }

    /**
     * 擷取備註（說明）內容
     *
     * @param string $content PDF內容
     * @return string|null 備註內容
     */
    public static function extractNotes($content) {
        // 嘗試使用新的方法提取說明區塊
        $result = self::extractExplanationBlock($content);
        if (!empty($result)) {
            return $result;
        }

        // 如果新方法失敗，使用原有的方法

        // 檢查是否為慈濟文件和工地協調會議
        $isTzuChiDocument = (mb_strpos($content, '財團法人中華民國佛教慈濟慈善事業基金會') !== false ||
                             mb_strpos($content, '慈志營字') !== false);
        $isCoordinationMeeting = mb_strpos($content, '工地協調會議紀錄') !== false;

        // 超精確處理慈濟工地協調會議紀錄
        if ($isTzuChiDocument && $isCoordinationMeeting) {
            // 直接尋找完整的兩點式說明格式
            if (preg_match('/說\s*明[：:][\s\r\n]*一[、.]\s*請承攬廠商依本次會議決議事項辦理.{0,20}(?:詳附件|詳如附件).*?二[、.]\s*下次會議時間[：:]\s*.*?(?:敬請準時出席|請準時出席)/us', $content, $matches)) {
                // 移除"說明："標記，只保留實際內容
                $notes = preg_replace('/^說\s*明[：:]\s*/u', '', $matches[0]);
                return trim(preg_replace('/\s+/', ' ', $notes));
            }

            // 分別尋找第一點和第二點
            $firstPoint = null;
            $secondPoint = null;

            // 尋找第一點 - 請承攬廠商依本次會議決議事項辦理
            if (preg_match('/一[、.]\s*(請承攬廠商依本次會議決議事項辦理.*?)(?:二[、.]|$)/us', $content, $firstMatches)) {
                $firstPoint = trim($firstMatches[1]);

                // 確保包含"詳附件"
                if (strpos($firstPoint, '詳附件') === false) {
                    $firstPoint .= '詳附件。';
                }
            }

            // 尋找第二點 - 下次會議時間
            if (preg_match('/二[、.]\s*(下次會議時間[：:]\s*.*?(?:敬請準時出席|請準時出席))/us', $content, $secondMatches)) {
                $secondPoint = trim($secondMatches[1]);
            }

            // 組合結果
            if ($firstPoint && $secondPoint) {
                return "一、 " . $firstPoint . " 二、 " . $secondPoint;
            } else if ($firstPoint) {
                return "一、 " . $firstPoint;
            }
        }

        // 備用處理：檢查是否存在協調會議記錄標準格式
        if ($isCoordinationMeeting || mb_strpos($content, '協調會議記錄') !== false) {
            // 嘗試找出決議事項
            if (preg_match('/決議(?:事項)?[：:](.*?)(?=出席人員|參與人員|$)/us', $content, $matches)) {
                return trim(preg_replace('/\s+/', ' ', $matches[1]));
            }
        }

        // 處理慈濟基金會的文件 - 多點式說明
        if ($isTzuChiDocument) {
            // 尋找多點式說明
            $points = array();
            if (preg_match_all('/([一二三四五六七八九十][、.．])\s*([^一二三四五六七八九十]*?)(?=(?:[一二三四五六七八九十][、.．])|正本|副本|$)/us', $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $pointNumber = $match[1];
                    $pointContent = trim(preg_replace('/\s+/', ' ', $match[2]));
                    if (!empty($pointContent)) {
                        $points[] = $pointNumber . ' ' . $pointContent;
                    }
                }

                if (count($points) >= 1) {
                    error_log("從慈濟文件提取到" . count($points) . "點說明");
                    return implode("\n", $points); // 使用換行符分隔各點，而不是空格
                }
            }

            // 如果上面的方法失敗，嘗試另一種更寬鬆的模式
            $points = array();
            if (preg_match_all('/([一二三四五六七八九十][、.．])[\s\n\r]*(.+?)(?=(?:[一二三四五六七八九十][、.．])|正本|副本|$)/us', $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $pointNumber = $match[1];
                    $pointContent = trim(preg_replace('/\s+/', ' ', $match[2]));
                    if (!empty($pointContent)) {
                        $points[] = $pointNumber . ' ' . $pointContent;
                    }
                }

                if (count($points) >= 1) {
                    error_log("從慈濟文件使用寬鬆模式提取到" . count($points) . "點說明");
                    return implode("\n", $points); // 使用換行符分隔各點
                }
            }

        // 嘗試提取所有的說明點（一、二、三、四、五、六、七、八、九、十等）
        $allPoints = [];
        if (preg_match_all('/([\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d\u5341]+)[\u3001.\uff0e]([^\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d\u5341\n]+)/u', $content, $matches)) {
            foreach ($matches[0] as $index => $fullMatch) {
                $pointContent = trim($matches[2][$index]);
                if (!empty($pointContent)) {
                    $allPoints[] = trim($fullMatch);
                }
            }
        } elseif (preg_match_all('/(\d+)[\u3001.\uff0e]([^\d\n]+)/u', $content, $matches)) {
            foreach ($matches[0] as $index => $fullMatch) {
                $pointContent = trim($matches[2][$index]);
                if (!empty($pointContent)) {
                    $allPoints[] = trim($fullMatch);
                }
            }
        }

        if (!empty($allPoints)) {
            $notes = implode("\n", $allPoints);
            error_log("使用點列表提取到說明，共" . count($allPoints) . "點: " . substr($notes, 0, 100) . "...");
            return $notes;
        }
        }

        // 特殊處理 - 檢查是否是花蓮縣地方稅務局的文件
        if (mb_strpos($content, "花蓮縣地方稅務局") !== false ||
            (mb_strpos($content, "花蓮") !== false && mb_strpos($content, "稅務局") !== false)) {
            error_log("檢測到花蓮縣地方稅務局的文件，嘗試提取說明");

            // 嘗試提取說明 - 改進的正則表達式，處理多行說明
            if (preg_match('/說\s*明\s*[：:]\s*([^\n\r]*(?:\n\r?(?!辦\s*法|正本|副本)[^\n\r]*)*)/us', $content, $matches)) {
                $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
                if (!empty($explanation)) {
                    error_log("從花蓮縣地方稅務局文件提取說明: " . substr($explanation, 0, 100) . "...");
                    return $explanation;
                }
            }

            // 嘗試提取編號式說明，處理可能的格式問題
            if (preg_match('/說\s*明\s*[：:].*?([一二三四五六七八九十]\s*[、.．]\s*.+?)(?=\s*辦\s*法\s*[：:]|\s*正本|\s*副本|$)/us', $content, $matches)) {
                $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
                if (!empty($explanation)) {
                    error_log("從花蓮縣地方稅務局文件提取編號式說明: " . substr($explanation, 0, 100) . "...");
                    return $explanation;
                }
            }

            // 嘗試提取完整的編號式說明（包含多個編號）
            if (preg_match('/說\s*明\s*[：:].*?([一二三四五六七八九十]\s*[、.．]\s*.+?(?:\s*[一二三四五六七八九十]\s*[、.．]\s*.+?)*?)(?=\s*辦\s*法\s*[：:]|\s*正本|\s*副本|$)/us', $content, $matches)) {
                $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
                if (!empty($explanation)) {
                    error_log("從花蓮縣地方稅務局文件提取完整編號式說明: " . substr($explanation, 0, 100) . "...");
                    return $explanation;
                }
            }

            // 嘗試更寬鬆的匹配，處理可能的OCR錯誤
            if (preg_match('/說.{0,3}明.{0,3}[：:].{0,5}([^\n\r]*(?:\n\r?(?!辦.{0,3}法|正本|副本)[^\n\r]*)*)/us', $content, $matches)) {
                $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
                if (!empty($explanation)) {
                    error_log("從花蓮縣地方稅務局文件使用寬鬆匹配提取說明: " . substr($explanation, 0, 100) . "...");
                    return $explanation;
                }
            }

            // 嘗試提取說明部分中的所有內容，直到辦法或正副本
            if (preg_match('/說.{0,3}明.{0,5}[\s\S]*?([一二三四五六七八九十].{0,3}[、.．].{0,5}[\s\S]+?)(?=辦.{0,3}法|正本|副本|$)/us', $content, $matches)) {
                $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
                if (!empty($explanation)) {
                    error_log("從花蓮縣地方稅務局文件提取完整說明內容: " . substr($explanation, 0, 100) . "...");
                    return $explanation;
                }
            }
        }

        // 通用說明提取方法 - 改進匹配模式
        if (preg_match('/說\s*明\s*[：:]\s*([^\n\r]*(?:\n\r?(?!正本|副本|附件|辦\s*法)[^\n\r]*)*)/us', $content, $matches)) {
            $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($explanation)) {
                error_log("從通用模式提取說明: " . substr($explanation, 0, 100) . "...");
                return $explanation;
            }
        }

        // 如果上面的方法失敗，嘗試更寬鬆的模式
        if (preg_match('/說\s*明[^\n\r]*\n\r?([^\n\r]*(?:\n\r?(?!正本|副本|附件|辦\s*法)[^\n\r]*)*)/us', $content, $matches)) {
            $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($explanation)) {
                error_log("從寬鬆模式提取說明: " . substr($explanation, 0, 100) . "...");
                return $explanation;
            }
        }

        // 嘗試更寬鬆的匹配，處理可能的OCR錯誤
        if (preg_match('/說.{0,3}明.{0,3}[：:].{0,5}([^\n\r]*(?:\n\r?(?!正本|副本|附件|辦.{0,3}法)[^\n\r]*)*)/us', $content, $matches)) {
            $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($explanation)) {
                error_log("使用寬鬆匹配提取說明: " . substr($explanation, 0, 100) . "...");
                return $explanation;
            }
        }

        // 尋找說明標題下的編號式說明
        if (preg_match('/說\s*明[^\n\r]*\n\r?(?:[^\n\r]*\n\r?)*?([\d一二三四五六七八九十零][\s\.\、\。].*?)(?=\n\r?\s*(?:正本|副本|附件|辦\s*法|$))/us', $content, $matches)) {
            $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($explanation)) {
                error_log("從編號式說明提取: " . substr($explanation, 0, 100) . "...");
                return $explanation;
            }
        }

        // 嘗試提取完整的編號式說明（包含多個編號）
        if (preg_match('/說\s*明[^\n\r]*\n\r?(?:[^\n\r]*\n\r?)*?([\d一二三四五六七八九十零][\s\.\、\。].*?(?:\s*[\d一二三四五六七八九十零][\s\.\、\。].*?)*?)(?=\n\r?\s*(?:正本|副本|附件|辦\s*法|$))/us', $content, $matches)) {
            $explanation = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($explanation)) {
                error_log("從完整編號式說明提取: " . substr($explanation, 0, 100) . "...");
                return $explanation;
            }
        }

        return null;
    }

    /**
     * 擷取內容摘要
     *
     * @param string $content PDF內容
     * @return string 內容摘要
     */
    public static function extractSummary($content) {
        // 直接調用提取備註的方法
        $notes = self::extractNotes($content);

        // 如果能提取到備註內容，使用它作為摘要
        if (!empty($notes)) {
            return mb_substr($notes, 0, 1000, 'UTF-8');
        }

        // 如果有「說明」段落，使用其內容（確保移除說明標籤）
        if (preg_match('/說\s*明\s*[：:]\s*([^\n\r]*(?:\n\r?(?!正本|副本|附件|辦\s*法)[^\n\r]*)*)/us', $content, $matches)) {
            $summary = trim($matches[1]);
            // 清理提取的摘要內容
            $summary = self::cleanExtractedText($summary);
            // 檢查提取內容是否只包含句點和空格
            if (preg_match('/^[\s\.]+$/', $summary)) {
                $summary = '';
            } else {
                $summary = preg_replace('/\s+/', ' ', $summary);
            }

            if (!empty($summary)) {
                return mb_substr($summary, 0, 1000, 'UTF-8');
            }
        }

        // 嘗試更寬鬆的匹配，處理可能的OCR錯誤
        if (preg_match('/說.{0,3}明.{0,3}[：:].{0,5}([^\n\r]*(?:\n\r?(?!正本|副本|附件|辦.{0,3}法)[^\n\r]*)*)/us', $content, $matches)) {
            $summary = trim(preg_replace('/\s+/', ' ', $matches[1]));
            // 清理提取的摘要內容
            $summary = self::cleanExtractedText($summary);
            if (!empty($summary) && !preg_match('/^[\s\.]+$/', $summary)) {
                error_log("使用寬鬆匹配提取摘要: " . substr($summary, 0, 100) . "...");
                return mb_substr($summary, 0, 1000, 'UTF-8');
            }
        }

        // 嘗試提取編號式說明作為摘要
        if (preg_match('/說\s*明[^\n\r]*\n\r?(?:[^\n\r]*\n\r?)*?([\d一二三四五六七八九十零][\s\.\、\。].*?(?:\s*[\d一二三四五六七八九十零][\s\.\、\。].*?)*?)(?=\n\r?\s*(?:正本|副本|附件|辦\s*法|$))/us', $content, $matches)) {
            $summary = trim(preg_replace('/\s+/', ' ', $matches[1]));
            if (!empty($summary)) {
                return mb_substr($summary, 0, 1000, 'UTF-8');
            }
        }

        // 否則使用文件前1000個字
        $initialContent = mb_substr(trim($content), 0, 1000, 'UTF-8');
        // 清理初始內容
        $initialContent = self::cleanExtractedText($initialContent);
        // 確保摘要不是只包含句點和空格
        if (preg_match('/^[\s\.]+$/', $initialContent)) {
            return '無法提取有效摘要';
        }

        return $initialContent;
    }

    /**
     * 擷取工地資訊
     *
     * @param string $content PDF內容
     * @return string|null 相關工地
     */
    public static function extractSiteInfo($content) {
        // 常見工地名稱和關鍵詞對照表
        $sitePatterns = [
            '美崙安居' => '美崙',
            '美崙社會住宅' => '美崙',
            '慈濟' => '慈濟',
            '慈濟五期' => '慈濟五期',
            '五穀行安居' => '五穀行安居',
            '花蓮市' => '花蓮市',
            '花蓮縣花蓮市' => '美崙',  // 通常花蓮縣花蓮市的案子是美崙工地
            '花蓮縣' => '花蓮市'
        ];

        foreach ($sitePatterns as $pattern => $site) {
            if (mb_strpos($content, $pattern) !== false) {
                error_log("從關鍵詞「{$pattern}」辨識到工地: {$site}");
                return $site;
            }
        }

        return null;
    }

    /**
     * 後處理OCR結果，修正常見的辨識錯誤
     *
     * @param string $text OCR辨識的文本
     * @return string 修正後的文本
     */
    public static function postProcessOCRText($text) {
        // 如果 TzuChiProcessor 存在，則使用它的後處理方法
        if (class_exists('TzuChiProcessor')) {
            return TzuChiProcessor::postProcessOCRText($text);
        }

        if (empty($text)) {
            return $text;
        }

        // 先備份原始文本
        $originalText = $text;

        // 1. 修正常見的辨識錯誤
        $replacements = [
            // 修正常見的英文字母辨識錯誤
            '/\bah\s+re\b/i' => 'ah re',
            '/\bFe\s+<\s+-\s+are\b/i' => 'Fe < - are',
            '/\bREFRS\s+BA\s+FH\b/i' => 'REFERS BA FH',
            '/\bete\s+hs\b/i' => 'etc hs',
            '/\bRE\s+FERS\b/i' => 'REFERS',
            '/\bB\s+A\s+F\s+H\b/i' => 'BA FH',

            // 修正常見的數字辨識錯誤
            '/\b(\d+)\s+(\d+)\b/' => '$1$2',  // 將被空格分開的數字合併
            '/\b(\d{2,4})\s*-\s*(\d{2})\s*-\s*(\d{2})\b/' => '$1-$2-$3',  // 修正日期格式

            // 修正常見的標點符號辨識錯誤
            '/([:\-\+\(\)\.,])\s+/' => '$1',  // 移除標點符號後的空格
            '/\s+([:\-\+\)\.,])/' => '$1',   // 移除標點符號前的空格

            // 修正常見的中文辨識錯誤
            '/\u5de5\s+\u7a0b/' => '工程',  // 工 程 -> 工程
            '/\u5c55\s+\u5ef6/' => '展延',  // 展 延 -> 展延
            '/\u826f\s+\u6709/' => '良有',  // 良 有 -> 良有
            '/\u767c\s+\u6587/' => '發文',  // 發 文 -> 發文
            '/\u55ae\s+\u4f4d/' => '單位',  // 單 位 -> 單位
            '/\u8aaa\s+\u660e/' => '說明',  // 說 明 -> 說明
            '/\u4e3b\s+\u65e8/' => '主旨',  // 主 旨 -> 主旨
            '/\u53d7\s+\u6587/' => '受文',  // 受 文 -> 受文
            '/\u8ca1\s+\u5718/' => '財團',  // 財 團 -> 財團
            '/\u6cd5\s+\u4eba/' => '法人',  // 法 人 -> 法人
            '/\u4e2d\s+\u83ef/' => '中華',  // 中 華 -> 中華
            '/\u6c11\s+\u570b/' => '民國',  // 民 國 -> 民國
            '/\u4f5b\s+\u6559/' => '佛教',  // 佛 教 -> 佛教
            '/\u57fa\s+\u91d1/' => '基金',  // 基 金 -> 基金
            '/\u6703\s+\u71df/' => '會營',  // 會 營 -> 會營
            '/\u71df\s+\u5efa/' => '營建',  // 營 建 -> 營建
            '/\u5efa\s+\u8655/' => '建處',  // 建 處 -> 建處

            // 修正說明部分常見錯誤
            '/\u5730\s+\u5740/' => '地址',  // 地 址 -> 地址
            '/\u82b1\s+\u84ee/' => '花蓮',  // 花 蓮 -> 花蓮
            '/\u5e02\s+\u4e2d/' => '市中',  // 市 中 -> 市中山
            '/\u4e2d\s+\u5c71/' => '中山',  // 中 山 -> 中山
            '/\u8def\s+\u4e00/' => '路一',  // 路 一 -> 路一
            '/\u6bb5\s+\u4e8c/' => '段二',  // 段 二 -> 段二
            '/\u865f\s+\u96fb/' => '號電',  // 號 電 -> 號電
            '/\u96fb\s+\u8a71/' => '電話',  // 電 話 -> 電話
        ];

        // 應用所有替換規則
        foreach ($replacements as $pattern => $replacement) {
            $text = preg_replace($pattern, $replacement, $text);
        }

        // 2. 特別處理慈濟公文中的工程名稱和發文單位
        if (strpos($text, '慈濟') !== false || strpos($text, '常慈') !== false || strpos($text, '慈志') !== false || strpos($text, '慈書字') !== false ||
            strpos($text, '財團法人') !== false || strpos($text, '佛教') !== false || strpos($text, 'TZU-CHI') !== false || strpos($text, 'BUDDHIST COMPASSION RELIEF') !== false) {

            // 將「慈濟 慈善」等常見的辨識錯誤修正
            $text = str_replace('慈濟 慈善', '慈濟慈善', $text);

            // 將「慈濟五期 工程」等常見的辨識錯誤修正
            $text = str_replace('慈濟五期 工程', '慈濟五期工程', $text);

            // 將「財 團法 人」等常見的辨識錯誤修正
            $text = str_replace('財 團法 人', '財團法人', $text);

            // 將「中 華民 國」等常見的辨識錯誤修正
            $text = str_replace('中 華民 國', '中華民國', $text);

            // 將「佛 教慈 濟」等常見的辨識錯誤修正
            $text = str_replace('佛 教慈 濟', '佛教慈濟', $text);

            // 將「慈 善事 業」等常見的辨識錯誤修正
            $text = str_replace('慈 善事 業', '慈善事業', $text);

            // 將「基 金會 」等常見的辨識錯誤修正
            $text = str_replace('基 金會 ', '基金會', $text);

            // 將「主 旨」等常見的辨識錯誤修正
            $text = str_replace('主 旨', '主旨', $text);

            // 將「說 明」等常見的辨識錯誤修正
            $text = str_replace('說 明', '說明', $text);

            // 將「工 地」等常見的辨識錯誤修正
            $text = str_replace('工 地', '工地', $text);

            // 將「協 調」等常見的辨識錯誤修正
            $text = str_replace('協 調', '協調', $text);

            // 將「會 議」等常見的辨識錯誤修正
            $text = str_replace('會 議', '會議', $text);

            // 將「紀 錄」等常見的辨識錯誤修正
            $text = str_replace('紀 錄', '紀錄', $text);

            // 修正發文單位的常見錯誤
            $commonErrors = [
                // 辨識錯誤的完整名稱
                '財團法人 中華民國 佛教慈濟慈善事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
                '財團法人中華 民國佛教慈濟慈善事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
                '財團法人中華民國佛教 慈濟慈善事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
                '財團法人中華民國佛教慈濟慈善 事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',

                // 常見的簡化名稱錯誤
                '佛教 慈濟 慈善事業基金會' => '佛教慈濟慈善事業基金會',
                '佛教慈濟 慈善事業基金會' => '佛教慈濟慈善事業基金會',

                // 常慈書字相關錯誤
                '常 慈書字' => '常慈書字',
                '常慈 書字' => '常慈書字',
                '常慈書 字' => '常慈書字',

                // 英文名稱相關錯誤
                'BUDDHIST COMPASSION RELIEF TZU-CHI FOUNDATION' => 'BUDDHIST COMPASSION RELIEF TZU-CHI FOUNDATION',
                'BUDDHIST COMPASSION RELIEF' => 'BUDDHIST COMPASSION RELIEF',
                'TZU-CHI FOUNDATION' => 'TZU-CHI FOUNDATION',
                'TZU CHI FOUNDATION' => 'TZU-CHI FOUNDATION',
                'TZU - CHI FOUNDATION' => 'TZU-CHI FOUNDATION',

                // 標題相關錯誤
                '營建處函稿簽核表' => '營建處函稿簽核表',
                '工地協調會議紀錄' => '工地協調會議紀錄',
                '慈志營字' => '慈志營字',
            ];

            foreach ($commonErrors as $error => $correction) {
                $text = str_replace($error, $correction, $text);
            }
        }

        // 3. 特別處理工程展延相關文字
        if (strpos($text, '展延') !== false || strpos($text, '工期') !== false) {
            // 將「工期 展延」等常見的辨識錯誤修正
            $text = str_replace('工期 展延', '工期展延', $text);

            // 將「申請 工期展延」等常見的辨識錯誤修正
            $text = str_replace('申請 工期', '申請工期', $text);
        }

        // 如果文本有變化，記錄日誌
        if ($text !== $originalText) {
            error_log("OCR文本後處理完成，修正了" . (strlen($originalText) - strlen($text)) . "個字符");
        }

        return $text;
    }

    /**
     * 後處理發文單位的辨識結果
     *
     * @param string $sender 辨識到的發文單位
     * @return string 修正後的發文單位
     */
    public static function postProcessSender($sender) {
        if (empty($sender)) {
            return $sender;
        }

        // 先備份原始文本
        $originalSender = $sender;

        // 1. 移除多餘的空格
        $sender = preg_replace('/\s+/', ' ', $sender);
        $sender = trim($sender);

        // 2. 修正常見的發文單位錯誤
        $senderReplacements = [
            // 慶濟相關
            '財團法人 中華民國 佛教慈濟慈善事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
            '財團法人中華 民國佛教慈濟慈善事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
            '財團法人中華民國佛教 慈濟慈善事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
            '財團法人中華民國佛教慈濟慈善 事業基金會' => '財團法人中華民國佛教慈濟慈善事業基金會',
            '佛教 慈濟 慈善事業基金會' => '佛教慈濟慈善事業基金會',
            '佛教慈濟 慈善事業基金會' => '佛教慈濟慈善事業基金會',
            '慈濟慈善事業基金會 營建處' => '財團法人中華民國佛教慈濟慈善事業基金會 營建處',
            '慈濟基金會 營建處' => '財團法人中華民國佛教慈濟慈善事業基金會 營建處',
            '慈濟基金會營建處' => '財團法人中華民國佛教慈濟慈善事業基金會 營建處',

            // 常慈相關
            '常 慈書字' => '常慈書字',
            '常慈 書字' => '常慈書字',
            '常慈書 字' => '常慈書字',

            // 建築師事務所相關
            '許 常吉建築師事務所' => '許常吉建築師事務所',
            '許常 吉建築師事務所' => '許常吉建築師事務所',
            '許常吉 建築師事務所' => '許常吉建築師事務所',
            '許常吉建築師 事務所' => '許常吉建築師事務所',
            '許常吉建築師事 務所' => '許常吉建築師事務所',
            '許常吉建築師事務 所' => '許常吉建築師事務所',

            // 良有營造相關
            '良 有營造股份有限公司' => '良有營造股份有限公司',
            '良有 營造股份有限公司' => '良有營造股份有限公司',
            '良有營 造股份有限公司' => '良有營造股份有限公司',
            '良有營造 股份有限公司' => '良有營造股份有限公司',

            // 其他常見單位
            '花 蓮縣政府' => '花蓮縣政府',
            '花蓮 縣政府' => '花蓮縣政府',
            '花蓮縣 政府' => '花蓮縣政府',
            '內政 部營建署' => '內政部營建署',
            '內政部 營建署' => '內政部營建署',
            '內政部營 建署' => '內政部營建署',
        ];

        foreach ($senderReplacements as $error => $correction) {
            if (strpos($sender, $error) !== false) {
                $sender = str_replace($error, $correction, $sender);
            }
        }

        // 3. 特別處理慶濟相關發文單位
        if (strpos($sender, '慈濟') !== false || strpos($sender, '常慈') !== false) {
            // 如果包含營建處，但不包含完整名稱，添加完整名稱
            if (strpos($sender, '營建處') !== false && strpos($sender, '財團法人中華民國佛教慈濟慈善事業基金會') === false) {
                $sender = '財團法人中華民國佛教慈濟慈善事業基金會 營建處';
            }

            // 如果只有「慈濟」或「慈濟基金會」，使用完整名稱
            if ($sender === '慈濟' || $sender === '慈濟基金會') {
                $sender = '財團法人中華民國佛教慈濟慈善事業基金會';
            }
        }

        // 如果文本有變化，記錄日誌
        if ($sender !== $originalSender) {
            error_log("發文單位後處理完成，從 '{$originalSender}' 修正為 '{$sender}'");
        }

        return $sender;
    }


}