<?php
/**
 * 自動上傳系統測試腳本
 * 用於驗證系統各組件是否正常運作
 */

require_once 'db.php';

echo "=== 良有營造公文自動上傳系統測試 ===\n\n";

$tests_passed = 0;
$tests_total = 0;

function runTest($test_name, $test_function) {
    global $tests_passed, $tests_total;
    $tests_total++;
    
    echo "測試 $tests_total: $test_name ... ";
    
    try {
        $result = $test_function();
        if ($result) {
            echo "✓ 通過\n";
            $tests_passed++;
        } else {
            echo "✗ 失敗\n";
        }
    } catch (Exception $e) {
        echo "✗ 錯誤: " . $e->getMessage() . "\n";
    }
}

// 測試1: 資料庫連接
runTest("資料庫連接", function() use ($pdo) {
    return $pdo instanceof PDO;
});

// 測試2: 檢查資料表是否存在
runTest("檢查auto_upload_configs表", function() use ($pdo) {
    $stmt = $pdo->query("SHOW TABLES LIKE 'auto_upload_configs'");
    return $stmt->rowCount() > 0;
});

runTest("檢查auto_upload_logs表", function() use ($pdo) {
    $stmt = $pdo->query("SHOW TABLES LIKE 'auto_upload_logs'");
    return $stmt->rowCount() > 0;
});

runTest("檢查uploaded_file_hashes表", function() use ($pdo) {
    $stmt = $pdo->query("SHOW TABLES LIKE 'uploaded_file_hashes'");
    return $stmt->rowCount() > 0;
});

// 測試3: 檢查必要檔案是否存在
runTest("檢查SMB掃描器檔案", function() {
    return file_exists('smb_scanner.php');
});

runTest("檢查自動上傳處理器檔案", function() {
    return file_exists('auto_upload_processor.php');
});

runTest("檢查管理介面檔案", function() {
    return file_exists('auto_upload_manager.php');
});

// 測試4: 檢查PHP擴展和命令
runTest("檢查smbclient命令", function() {
    $output = shell_exec('which smbclient 2>/dev/null');
    return !empty(trim($output));
});

// 測試5: 檢查目錄權限
runTest("檢查uploads目錄", function() {
    $upload_dir = 'uploads/auto_upload/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    return is_writable($upload_dir);
});

runTest("檢查logs目錄", function() {
    $log_dir = 'logs/';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    return is_writable($log_dir);
});

// 測試6: 測試類別載入
runTest("測試SMBScanner類別載入", function() {
    require_once 'smb_scanner.php';
    return class_exists('SMBScanner');
});

runTest("測試AutoUploadProcessor類別載入", function() {
    require_once 'auto_upload_processor.php';
    return class_exists('AutoUploadProcessor');
});

// 測試7: 檢查初始設定資料
runTest("檢查初始設定資料", function() use ($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM auto_upload_configs");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['count'] > 0;
});

// 測試8: 測試基本SMB連線（如果有設定的話）
runTest("測試SMB連線功能", function() use ($pdo) {
    require_once 'smb_scanner.php';
    
    // 獲取第一個設定來測試
    $stmt = $pdo->query("SELECT * FROM auto_upload_configs LIMIT 1");
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$config) {
        return true; // 沒有設定就跳過
    }
    
    $scanner = new SMBScanner($pdo);
    $result = $scanner->testConnection($config['smb_path']);
    
    // 即使連線失敗也算通過，因為可能是網路問題
    return isset($result['success']);
});

echo "\n=== 測試結果 ===\n";
echo "通過: $tests_passed / $tests_total\n";

if ($tests_passed === $tests_total) {
    echo "✓ 所有測試通過！系統已準備就緒。\n\n";
    
    echo "=== 下一步操作 ===\n";
    echo "1. 訪問管理介面: http://your-domain/auto_upload_manager.php\n";
    echo "2. 設定SMB路徑和對應工地\n";
    echo "3. 測試SMB連線\n";
    echo "4. 設定cron job: ./setup_cron.sh\n";
    echo "5. 手動測試: http://your-domain/manual_upload_test.php\n\n";
    
    echo "=== 現有設定 ===\n";
    try {
        $stmt = $pdo->query("
            SELECT auc.name, auc.smb_path, s.name as site_name, auc.is_active 
            FROM auto_upload_configs auc 
            LEFT JOIN sites s ON auc.site_id = s.id 
            ORDER BY auc.name
        ");
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($configs as $config) {
            $status = $config['is_active'] ? '啟用' : '停用';
            echo "- {$config['name']} ({$status})\n";
            echo "  路徑: {$config['smb_path']}\n";
            echo "  工地: {$config['site_name']}\n\n";
        }
    } catch (Exception $e) {
        echo "無法獲取設定列表: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "✗ 有測試失敗，請檢查系統設定。\n\n";
    
    echo "=== 常見問題解決 ===\n";
    echo "1. 如果smbclient測試失敗:\n";
    echo "   sudo apt-get install samba-client\n\n";
    echo "2. 如果目錄權限測試失敗:\n";
    echo "   sudo chown -R www-data:www-data uploads/ logs/\n";
    echo "   sudo chmod -R 755 uploads/ logs/\n\n";
    echo "3. 如果資料庫測試失敗:\n";
    echo "   檢查 db.php 中的連線設定\n";
    echo "   確保資料庫使用者有足夠權限\n\n";
}

echo "=== 系統資訊 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "當前目錄: " . getcwd() . "\n";
echo "系統時間: " . date('Y-m-d H:i:s') . "\n";
echo "時區: " . date_default_timezone_get() . "\n";

// 檢查重要的PHP設定
echo "\n=== PHP設定檢查 ===\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
echo "file_uploads: " . (ini_get('file_uploads') ? 'enabled' : 'disabled') . "\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";

echo "\n測試完成！\n";
?>
