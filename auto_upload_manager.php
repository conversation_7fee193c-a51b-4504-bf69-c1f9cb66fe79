<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
session_start();
require_once 'db.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

// 初始化用戶資訊
$site = $_SESSION['site'] ?? '總公司';
$username = $_SESSION['username'] ?? '';
$role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;
$role_mapping = [
    'admin' => '系統管理員',
    'user' => '一般用戶',
    'manager' => '主管'
];

$message = '';
$message_type = '';

// 處理表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'add':
                $stmt = $pdo->prepare("
                    INSERT INTO auto_upload_configs 
                    (name, smb_path, site_id, category_id, upload_time, scan_subdirs, file_extensions, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['smb_path'],
                    $_POST['site_id'],
                    $_POST['category_id'] ?: null,
                    $_POST['upload_time'],
                    isset($_POST['scan_subdirs']) ? 1 : 0,
                    $_POST['file_extensions'],
                    $user_id
                ]);
                $message = '自動上傳設定已新增成功！';
                $message_type = 'success';
                break;
                
            case 'edit':
                $stmt = $pdo->prepare("
                    UPDATE auto_upload_configs 
                    SET name=?, smb_path=?, site_id=?, category_id=?, upload_time=?, 
                        scan_subdirs=?, file_extensions=?, updated_at=NOW()
                    WHERE id=?
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['smb_path'],
                    $_POST['site_id'],
                    $_POST['category_id'] ?: null,
                    $_POST['upload_time'],
                    isset($_POST['scan_subdirs']) ? 1 : 0,
                    $_POST['file_extensions'],
                    $_POST['config_id']
                ]);
                $message = '自動上傳設定已更新成功！';
                $message_type = 'success';
                break;
                
            case 'toggle':
                $config_id = $_POST['config_id'];
                $stmt = $pdo->prepare("UPDATE auto_upload_configs SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$config_id]);
                $message = '設定狀態已更新！';
                $message_type = 'success';
                break;
                
            case 'delete':
                $config_id = $_POST['config_id'];
                $stmt = $pdo->prepare("DELETE FROM auto_upload_configs WHERE id = ?");
                $stmt->execute([$config_id]);
                $message = '設定已刪除！';
                $message_type = 'success';
                break;
                
            case 'test_connection':
                $smb_path = $_POST['smb_path'];
                // 測試SMB連線
                $test_result = testSMBConnection($smb_path);
                echo json_encode($test_result);
                exit;
        }
    } catch (PDOException $e) {
        $message = '操作失敗：' . $e->getMessage();
        $message_type = 'error';
    }
}

// 獲取所有工地
$sites = [];
try {
    $stmt = $pdo->query("SELECT id, name FROM sites ORDER BY name");
    $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching sites: " . $e->getMessage());
}

// 獲取所有文件類別
$categories = [];
try {
    $stmt = $pdo->query("SELECT id, name FROM document_categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
}

// 獲取自動上傳設定
$configs = [];
try {
    $stmt = $pdo->query("
        SELECT auc.*, s.name as site_name, dc.name as category_name, u.name as creator_name
        FROM auto_upload_configs auc
        LEFT JOIN sites s ON auc.site_id = s.id
        LEFT JOIN document_categories dc ON auc.category_id = dc.id
        LEFT JOIN users u ON auc.created_by = u.id
        ORDER BY auc.created_at DESC
    ");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching configs: " . $e->getMessage());
}

// 測試SMB連線的函數
function testSMBConnection($smb_path) {
    // 這裡實作SMB連線測試
    // 暫時返回模擬結果
    return [
        'success' => true,
        'message' => 'SMB連線測試成功',
        'path' => $smb_path
    ];
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公文自動上傳管理 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
            --white: #ffffff;
            --text-dark: #2c3e50;
            --border-color: #bdc3c7;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: var(--white);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            color: var(--primary-color);
            font-size: 24px;
            font-weight: 600;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--secondary-color);
            color: var(--white);
        }

        .btn-success {
            background-color: var(--success-color);
            color: var(--white);
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: var(--white);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: var(--white);
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .card {
            background: var(--white);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--primary-color);
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-dark);
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background-color: var(--light-bg);
            font-weight: 600;
            color: var(--primary-color);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--white);
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .back-link {
            color: var(--secondary-color);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="document_manager.php" class="back-link">
            <i class="fas fa-arrow-left"></i> 返回公文整理系統
        </a>

        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-robot"></i> 公文自動上傳管理
            </h1>
            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                <i class="fas fa-plus"></i> 新增設定
            </button>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type; ?>">
            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> 自動上傳設定列表
            </div>
            <div class="card-body">
                <?php if (empty($configs)): ?>
                <p class="text-center">尚未建立任何自動上傳設定</p>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>設定名稱</th>
                                <th>SMB路徑</th>
                                <th>對應工地</th>
                                <th>上傳時間</th>
                                <th>狀態</th>
                                <th>最後掃描</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($configs as $config): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($config['name']); ?></td>
                                <td><code><?php echo htmlspecialchars($config['smb_path']); ?></code></td>
                                <td><?php echo htmlspecialchars($config['site_name']); ?></td>
                                <td><?php echo $config['upload_time']; ?></td>
                                <td>
                                    <span class="status-badge <?php echo $config['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $config['is_active'] ? '啟用' : '停用'; ?>
                                    </span>
                                </td>
                                <td><?php echo $config['last_scan_time'] ? date('Y-m-d H:i', strtotime($config['last_scan_time'])) : '未掃描'; ?></td>
                                <td>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="editConfig(<?php echo $config['id']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-<?php echo $config['is_active'] ? 'warning' : 'success'; ?> btn-sm" onclick="toggleConfig(<?php echo $config['id']; ?>)">
                                        <i class="fas fa-<?php echo $config['is_active'] ? 'pause' : 'play'; ?>"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteConfig(<?php echo $config['id']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 新增/編輯設定模態視窗 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">新增自動上傳設定</h2>

            <form id="configForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="config_id" id="configId">

                <div class="form-group">
                    <label class="form-label" for="configName">設定名稱 <span style="color: red;">*</span></label>
                    <input type="text" class="form-control" id="configName" name="name" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="smbPath">SMB路徑 <span style="color: red;">*</span></label>
                    <input type="text" class="form-control" id="smbPath" name="smb_path" required
                           placeholder="例如：\\192.168.55.251\公文\04中壢前寮段\收文">
                    <small class="form-text text-muted">請輸入完整的SMB網路路徑</small>
                </div>

                <div class="form-group">
                    <label class="form-label" for="siteId">對應工地 <span style="color: red;">*</span></label>
                    <select class="form-control" id="siteId" name="site_id" required>
                        <option value="">-- 選擇工地 --</option>
                        <?php foreach ($sites as $site): ?>
                        <option value="<?php echo $site['id']; ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="categoryId">預設文件類別</label>
                    <select class="form-control" id="categoryId" name="category_id">
                        <option value="">-- 自動判斷 --</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="uploadTime">每日上傳時間</label>
                    <input type="time" class="form-control" id="uploadTime" name="upload_time" value="09:00">
                </div>

                <div class="form-group">
                    <label class="form-label" for="fileExtensions">允許的檔案副檔名</label>
                    <input type="text" class="form-control" id="fileExtensions" name="file_extensions"
                           value="pdf,doc,docx,jpg,jpeg,png" placeholder="用逗號分隔，例如：pdf,doc,docx">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="scanSubdirs" name="scan_subdirs" checked>
                        掃描子目錄
                    </label>
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn btn-warning" onclick="testConnection()">測試連線</button>
                    <button type="submit" class="btn btn-success">儲存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 顯示新增模態視窗
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增自動上傳設定';
            document.getElementById('formAction').value = 'add';
            document.getElementById('configId').value = '';
            document.getElementById('configForm').reset();
            document.getElementById('scanSubdirs').checked = true;
            document.getElementById('uploadTime').value = '09:00';
            document.getElementById('fileExtensions').value = 'pdf,doc,docx,jpg,jpeg,png';
            document.getElementById('configModal').style.display = 'block';
        }

        // 編輯設定
        function editConfig(configId) {
            // 這裡需要通過AJAX獲取設定詳情
            fetch('get_config.php?id=' + configId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        document.getElementById('modalTitle').textContent = '編輯自動上傳設定';
                        document.getElementById('formAction').value = 'edit';
                        document.getElementById('configId').value = config.id;
                        document.getElementById('configName').value = config.name;
                        document.getElementById('smbPath').value = config.smb_path;
                        document.getElementById('siteId').value = config.site_id;
                        document.getElementById('categoryId').value = config.category_id || '';
                        document.getElementById('uploadTime').value = config.upload_time;
                        document.getElementById('fileExtensions').value = config.file_extensions;
                        document.getElementById('scanSubdirs').checked = config.scan_subdirs == 1;
                        document.getElementById('configModal').style.display = 'block';
                    } else {
                        alert('獲取設定失敗：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('獲取設定時發生錯誤');
                });
        }

        // 切換設定狀態
        function toggleConfig(configId) {
            if (confirm('確定要切換此設定的狀態嗎？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle">
                    <input type="hidden" name="config_id" value="${configId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 刪除設定
        function deleteConfig(configId) {
            if (confirm('確定要刪除此設定嗎？此操作無法復原。')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="config_id" value="${configId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 測試SMB連線
        function testConnection() {
            const smbPath = document.getElementById('smbPath').value;
            if (!smbPath) {
                alert('請先輸入SMB路徑');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'test_connection');
            formData.append('smb_path', smbPath);

            fetch('auto_upload_manager.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('連線測試成功！');
                } else {
                    alert('連線測試失敗：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('測試連線時發生錯誤');
            });
        }

        // 關閉模態視窗
        function closeModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // 點擊模態視窗外部關閉
        window.onclick = function(event) {
            const modal = document.getElementById('configModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
