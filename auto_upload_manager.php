<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
session_start();
require_once 'db.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

// 初始化用戶資訊
$site = $_SESSION['site'] ?? '總公司';
$username = $_SESSION['username'] ?? '';
$role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;
$role_mapping = [
    'admin' => '系統管理員',
    'user' => '一般用戶',
    'manager' => '主管'
];

// 檢查權限
$isAdmin = ($role === 'admin');
$isHqSupervisor = ($role === 'hq_supervisor');
$isHqStaff = ($site === '總公司');
$is_hq_role = ($isAdmin || $isHqSupervisor || $isHqStaff);

// 獲取通知
$notifications = [];
$notification_count = 0;
$all_notifications = [];
try {
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$user_id]);
    $all_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND `read` = 0");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $notification_count = $result ? $result['count'] : 0;
} catch (PDOException $e) {
    error_log("Error fetching notifications: " . $e->getMessage());
}

$message = '';
$message_type = '';

// 處理表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'add':
                $stmt = $pdo->prepare("
                    INSERT INTO auto_upload_configs
                    (name, smb_path, site_id, category_id, upload_time, scan_subdirs, file_extensions, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['smb_path'],
                    $_POST['site_id'],
                    $_POST['category_id'] ?: null,
                    $_POST['upload_time'],
                    isset($_POST['scan_subdirs']) ? 1 : 0,
                    $_POST['file_extensions'],
                    $user_id
                ]);
                $message = '自動上傳設定已新增成功！';
                $message_type = 'success';
                break;

            case 'edit':
                $stmt = $pdo->prepare("
                    UPDATE auto_upload_configs
                    SET name=?, smb_path=?, site_id=?, category_id=?, upload_time=?,
                        scan_subdirs=?, file_extensions=?, updated_at=NOW()
                    WHERE id=?
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['smb_path'],
                    $_POST['site_id'],
                    $_POST['category_id'] ?: null,
                    $_POST['upload_time'],
                    isset($_POST['scan_subdirs']) ? 1 : 0,
                    $_POST['file_extensions'],
                    $_POST['config_id']
                ]);
                $message = '自動上傳設定已更新成功！';
                $message_type = 'success';
                break;

            case 'toggle':
                $config_id = $_POST['config_id'];
                $stmt = $pdo->prepare("UPDATE auto_upload_configs SET is_active = NOT is_active WHERE id = ?");
                $stmt->execute([$config_id]);
                $message = '設定狀態已更新！';
                $message_type = 'success';
                break;

            case 'delete':
                $config_id = $_POST['config_id'];
                $stmt = $pdo->prepare("DELETE FROM auto_upload_configs WHERE id = ?");
                $stmt->execute([$config_id]);
                $message = '設定已刪除！';
                $message_type = 'success';
                break;

            case 'manual_scan':
                require_once 'auto_upload_processor.php';
                $config_id = $_POST['config_id'];

                // 獲取設定
                $stmt = $pdo->prepare("SELECT * FROM auto_upload_configs WHERE id = ?");
                $stmt->execute([$config_id]);
                $config = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($config) {
                    $processor = new AutoUploadProcessor($pdo);
                    $result = $processor->processConfig($config);

                    // 更新最後掃描時間
                    $stmt = $pdo->prepare("UPDATE auto_upload_configs SET last_scan_time = NOW() WHERE id = ?");
                    $stmt->execute([$config_id]);

                    $message = "手動掃描完成！掃描 {$result['scanned_files']} 個檔案，發現 {$result['new_files']} 個新檔案，成功上傳 {$result['uploaded_files']} 個檔案";
                    if ($result['failed_files'] > 0) {
                        $message .= "，失敗 {$result['failed_files']} 個檔案";
                    }
                    $message_type = $result['uploaded_files'] > 0 || $result['failed_files'] == 0 ? 'success' : 'warning';
                } else {
                    $message = '找不到指定的設定';
                    $message_type = 'error';
                }
                break;

            case 'test_connection':
                $smb_path = $_POST['smb_path'];
                // 測試SMB連線
                $test_result = testSMBConnection($smb_path);
                echo json_encode($test_result);
                exit;
        }
    } catch (PDOException $e) {
        $message = '操作失敗：' . $e->getMessage();
        $message_type = 'error';
    }
}

// 獲取所有工地
$sites = [];
try {
    $stmt = $pdo->query("SELECT id, name FROM sites ORDER BY name");
    $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching sites: " . $e->getMessage());
}

// 獲取所有文件類別
$categories = [];
try {
    $stmt = $pdo->query("SELECT id, name FROM document_categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
}

// 獲取自動上傳設定
$configs = [];
try {
    $stmt = $pdo->query("
        SELECT auc.*, s.name as site_name, dc.name as category_name, u.name as creator_name
        FROM auto_upload_configs auc
        LEFT JOIN sites s ON auc.site_id = s.id
        LEFT JOIN document_categories dc ON auc.category_id = dc.id
        LEFT JOIN users u ON auc.created_by = u.id
        ORDER BY auc.created_at DESC
    ");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching configs: " . $e->getMessage());
}

// 測試SMB連線的函數
function testSMBConnection($smb_path) {
    // 這裡實作SMB連線測試
    // 暫時返回模擬結果
    return [
        'success' => true,
        'message' => 'SMB連線測試成功',
        'path' => $smb_path
    ];
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公文自動上傳管理 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #9c27b0;
            --info-hover: #7b1fa2;
            --dark-bg: #f5f5f5;
            --card-bg: #ffffff;
            --card-header: #f8f9fa;
            --table-header: #f8f9fa;
            --table-row-hover: #f5f5f5;
            --border-color: #e0e0e0;
            --text-color: #333333;
            --text-muted: #6c757d;
            --text-secondary: #6c757d;
            --light-bg: #f8f9fa;
            --navbar-bg: #ffffff;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        /* 暗黑模式變數 */
        body.theme-dark {
            --dark-bg: #121212;
            --card-bg: #1e1e1e;
            --card-header: #2d2d2d;
            --table-header: #2d2d2d;
            --table-row-hover: #2d2d2d;
            --border-color: #404040;
            --text-color: #ffffff;
            --text-muted: #b0b0b0;
            --text-secondary: #b0b0b0;
            --light-bg: #2d2d2d;
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft JhengHei', sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        /* 頂部導航樣式 */
        header {
            display: flex;
            align-items: center;
            background-color: var(--navbar-bg);
            padding: 0 20px;
            height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            margin-bottom: 0;
            padding-bottom: 0;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .logo-area {
            display: flex;
            align-items: center;
        }

        .logo-link {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-link img {
            height: 36px;
            margin-right: 10px;
        }

        .logo-link span {
            color: var(--text-color);
            font-size: 16px;
            font-weight: 600;
        }

        .main-nav {
            display: flex;
            align-items: center;
            margin-left: 20px;
            gap: 5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            color: var(--text-color);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s;
            font-size: 14px;
            white-space: nowrap;
        }

        .nav-item:hover {
            background-color: var(--navbar-hover);
        }

        .nav-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-item i {
            margin-right: 6px;
            font-size: 14px;
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .theme-toggle:hover {
            background-color: var(--navbar-hover);
        }

        .logout {
            color: #ea4335 !important;
            font-weight: 500;
        }

        .logout:hover {
            background-color: rgba(234, 67, 53, 0.1) !important;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-color);
        }

        .user-info i {
            font-size: 14px;
            color: #4285f4 !important;
        }

        .user-info-details {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
        }

        .user-position {
            font-size: 11px;
            color: var(--text-muted);
        }

        .user-site {
            font-size: 14px;
            color: var(--text-color);
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: var(--text-color);
        }

        .notification-icon:hover {
            background-color: var(--navbar-hover);
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: #ea4335;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 300px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        .notification-item {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            font-size: 13px;
            line-height: 1.4;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: rgba(26, 115, 232, 0.05);
            border-left: 3px solid var(--primary-color);
        }

        .notification-time {
            color: var(--text-muted);
            font-size: 11px;
            margin-top: 4px;
        }

        .main-content {
            margin-top: 60px;
            padding: 30px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 0;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-title i {
            color: var(--primary-color);
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-header h2 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-header h2 i {
            color: var(--primary-color);
        }

        .card-body {
            padding: 20px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-secondary {
            background-color: var(--text-muted);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-success {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--secondary-hover);
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: #212529;
        }

        .btn-warning:hover {
            background-color: var(--warning-hover);
        }

        .btn-delete {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-delete:hover {
            background-color: var(--danger-hover);
        }

        .btn-view {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-view:hover {
            background-color: var(--primary-hover);
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            margin: 2px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .document-table {
            width: 100%;
            border-collapse: collapse;
            background-color: var(--card-bg);
            margin: 0;
        }

        .document-table th {
            background-color: var(--table-header);
            color: var(--text-color);
            font-weight: 600;
            padding: 12px 15px;
            text-align: left;
            border-bottom: 2px solid var(--border-color);
            font-size: 14px;
        }

        .document-table td {
            padding: 12px 15px;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .document-table tr:hover {
            background-color: var(--table-row-hover);
        }

        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .tag-site {
            background-color: rgba(26, 115, 232, 0.1);
            color: #1a73e8;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background-color: rgba(52, 168, 83, 0.1);
            border: 1px solid rgba(52, 168, 83, 0.3);
            color: #1e7e34;
        }

        .alert-warning {
            background-color: rgba(251, 188, 5, 0.1);
            border: 1px solid rgba(251, 188, 5, 0.3);
            color: #856404;
        }

        .alert-danger {
            background-color: rgba(234, 67, 53, 0.1);
            border: 1px solid rgba(234, 67, 53, 0.3);
            color: #721c24;
        }

        .text-center {
            text-align: center;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--card-bg);
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-content h2 {
            background-color: var(--card-header);
            margin: 0;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            border-radius: 8px 8px 0 0;
            color: var(--text-color);
        }

        .close {
            color: var(--text-muted);
            float: right;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: var(--text-color);
        }

        .form-group {
            margin-bottom: 15px;
            padding: 0 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-color);
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            background-color: var(--card-bg);
            color: var(--text-color);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }

        .form-check input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }

        .form-check label {
            margin: 0;
            font-size: 14px;
            color: var(--text-color);
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            text-align: right;
            background-color: var(--card-bg);
            border-radius: 0 0 8px 8px;
        }

        /* 暗黑模式下的特殊樣式 */
        body.theme-dark {
            background-color: var(--dark-bg);
        }

        body.theme-dark .user-name {
            color: #ffffff !important;
        }

        body.theme-dark .user-position {
            color: #34a853 !important;
        }

        body.theme-dark .user-site {
            color: #aaaaaa !important;
        }

        body.theme-dark header {
            background-color: #1e1e1e !important;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .main-nav {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: var(--navbar-bg);
                flex-direction: column;
                padding: 10px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }

            .main-nav.show {
                display: flex;
            }

            .nav-item {
                width: 100%;
                justify-content: flex-start;
                padding: 12px 16px;
                margin-bottom: 5px;
            }

            .user-actions {
                gap: 10px;
            }

            .theme-text,
            .logout-text,
            .notification-text {
                display: none;
            }

            .user-info-details {
                display: none;
            }

            .main-content {
                padding: 15px;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .page-title {
                font-size: 20px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .document-table {
                font-size: 12px;
            }

            .document-table th,
            .document-table td {
                padding: 8px 10px;
            }
        }

        /* 自動上傳管理專用樣式 */
        /* 自動上傳管理專用樣式 */
        .auto-upload-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .scan-btn {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .scan-btn:hover {
            background-color: #138496;
            transform: translateY(-1px);
        }

        .scan-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .config-path {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: var(--text-muted);
            background: var(--light-bg);
            padding: 2px 6px;
            border-radius: 3px;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            margin: 2px;
        }

        /* 暗黑模式支援 */
        [data-theme="dark"] .config-path {
            background: var(--card-bg);
            color: var(--text-muted);
        }

        [data-theme="dark"] .status-active {
            background-color: rgba(40, 167, 69, 0.2);
            color: #4caf50;
        }

        [data-theme="dark"] .status-inactive {
            background-color: rgba(220, 53, 69, 0.2);
            color: #f44336;
        }
    </style>
</head>
<body>
<header>
    <div class="logo-area">
        <a href="dashboard.php" class="logo-link">
            <img src="images/logo.png" alt="良有營造標誌">
            <span>良有營造電子簽核系統</span>
        </a>
    </div>

    <!-- 添加漢堡選單按鈕，在移動設備上顯示 -->
    <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <nav class="main-nav" id="mainNav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-tachometer-alt"></i> 簽核總覽
        </a>

        <a href="document_manager.php" class="nav-item">
            <i class="fas fa-file-alt"></i> 公文整理系統
        </a>

        <?php if ($isAdmin): ?>
        <a href="auto_upload_manager.php" class="nav-item active">
            <i class="fas fa-robot"></i> 公文自動上傳管理
        </a>
        <?php endif; ?>
    </nav>

    <div class="user-actions" id="userActions">
        <button onclick="toggleTheme()" class="theme-toggle" title="切換主題">
            <i id="theme-icon" class="fas fa-sun"></i>
            <span id="theme-text">切換淺色模式</span>
        </button>
        <a href="logout.php" class="nav-item logout">
            <i class="fas fa-sign-out-alt"></i> <span class="logout-text">登出</span>
        </a>

        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <div class="user-info-details">
                <span class="user-name"><?php echo htmlspecialchars($_SESSION['name'] ?? $username); ?></span>
                <span class="user-position">
                    <?php if (!empty($_SESSION['position_name'])): ?>
                        <i class="fas fa-id-badge" style="margin-right: 4px; font-size: 12px;"></i>
                        <?php echo htmlspecialchars($_SESSION['position_name']); ?>
                    <?php else: ?>
                        <i class="fas fa-user-tag" style="margin-right: 4px; font-size: 12px;"></i>
                        <?php echo $role_mapping[$role] ?? '未知'; ?>
                    <?php endif; ?>
                </span>
                <span class="user-site"><?php echo htmlspecialchars($site); ?></span>
            </div>
        </div>

        <div class="notification-icon" onclick="toggleNotifications(event)">
            <i class="fas fa-bell"></i>
            <span class="notification-text">通知</span>
            <span class="notification-badge" id="notification-badge" <?php echo $notification_count > 0 ? '' : 'style="display:none;"'; ?>>
                <?php echo $notification_count; ?>
            </span>
            <div class="notification-dropdown" id="notificationDropdown">
                <div class="notification-header" style="padding: 10px 15px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; font-size: 16px;">通知訊息</h3>
                    <?php if (count($all_notifications) > 0): ?>
                    <button id="mark-read-btn" class="btn btn-sm" style="padding: 2px 8px; font-size: 12px; background-color: var(--primary-color); color: var(--navbar-text); border: none; border-radius: 4px; cursor: pointer;" onclick="markAllAsRead(event)">
                        <i class="fas fa-check"></i> 全部已讀
                    </button>
                    <?php endif; ?>
                </div>
                <div id="notifications-list">
                    <?php if (count($all_notifications) > 0): ?>
                        <?php foreach ($all_notifications as $notification): ?>
                        <div class="notification-item <?php echo $notification['read'] ? 'read' : 'unread'; ?>">
                            <div><?php echo htmlspecialchars($notification['message']); ?></div>
                            <div class="notification-time"><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="notification-item">
                            <div>目前沒有新通知</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</header>

<div class="main-content">
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-robot"></i> 公文自動上傳管理
        </h1>
        <div>
            <button type="button" class="btn btn-primary" onclick="showAddModal()">
                <i class="fas fa-plus"></i> 新增設定
            </button>
            <a href="auto_upload_status.php" class="btn btn-secondary">
                <i class="fas fa-chart-line"></i> 系統狀態
            </a>
        </div>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : ($message_type === 'warning' ? 'warning' : 'danger'); ?>">
        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'); ?>"></i>
        <div><?php echo htmlspecialchars($message); ?></div>
    </div>
    <?php endif; ?>

    <div class="dashboard-card">
        <div class="card-header">
            <h2><i class="fas fa-list"></i> 自動上傳設定列表</h2>
        </div>
        <div class="card-body">
            <?php if (empty($configs)): ?>
            <div class="text-center" style="padding: 40px;">
                <i class="fas fa-robot" style="font-size: 48px; color: var(--text-muted); margin-bottom: 15px;"></i>
                <p style="color: var(--text-muted); margin-bottom: 20px;">尚未建立任何自動上傳設定</p>
                <button type="button" class="btn btn-primary" onclick="showAddModal()">
                    <i class="fas fa-plus"></i> 建立第一個設定
                </button>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="document-table">
                    <thead>
                        <tr>
                            <th>設定名稱</th>
                            <th>SMB路徑</th>
                            <th>對應工地</th>
                            <th>上傳時間</th>
                            <th>狀態</th>
                            <th>最後掃描</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($configs as $config): ?>
                        <tr>
                            <td data-label="設定名稱">
                                <strong><?php echo htmlspecialchars($config['name']); ?></strong>
                            </td>
                            <td data-label="SMB路徑">
                                <div class="config-path" title="<?php echo htmlspecialchars($config['smb_path']); ?>">
                                    <?php echo htmlspecialchars($config['smb_path']); ?>
                                </div>
                            </td>
                            <td data-label="對應工地">
                                <span class="tag tag-site"><?php echo htmlspecialchars($config['site_name']); ?></span>
                            </td>
                            <td data-label="上傳時間"><?php echo $config['upload_time']; ?></td>
                            <td data-label="狀態">
                                <span class="status-badge <?php echo $config['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $config['is_active'] ? '啟用' : '停用'; ?>
                                </span>
                            </td>
                            <td data-label="最後掃描">
                                <?php echo $config['last_scan_time'] ? date('Y-m-d H:i', strtotime($config['last_scan_time'])) : '未掃描'; ?>
                            </td>
                            <td data-label="操作" style="text-align: center;">
                                <div class="auto-upload-actions">
                                    <?php if ($config['is_active']): ?>
                                    <button type="button" class="scan-btn" onclick="manualScan(<?php echo $config['id']; ?>)" title="手動掃描">
                                        <i class="fas fa-search"></i> 掃描
                                    </button>
                                    <?php endif; ?>

                                    <button type="button" class="btn btn-view btn-sm" onclick="editConfig(<?php echo $config['id']; ?>)" title="編輯設定">
                                        <i class="fas fa-edit"></i>
                                    </button>

                                    <button type="button" class="btn btn-<?php echo $config['is_active'] ? 'warning' : 'success'; ?> btn-sm" onclick="toggleConfig(<?php echo $config['id']; ?>)" title="<?php echo $config['is_active'] ? '停用' : '啟用'; ?>">
                                        <i class="fas fa-<?php echo $config['is_active'] ? 'pause' : 'play'; ?>"></i>
                                    </button>

                                    <button type="button" class="btn btn-delete btn-sm" onclick="deleteConfig(<?php echo $config['id']; ?>)" title="刪除設定">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

    <!-- 新增/編輯設定模態視窗 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">新增自動上傳設定</h2>

            <form id="configForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="config_id" id="configId">

                <div class="form-group">
                    <label class="form-label" for="configName">設定名稱 <span style="color: red;">*</span></label>
                    <input type="text" class="form-control" id="configName" name="name" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="smbPath">SMB路徑 <span style="color: red;">*</span></label>
                    <input type="text" class="form-control" id="smbPath" name="smb_path" required
                           placeholder="例如：\\192.168.55.251\公文\04中壢前寮段\收文">
                    <small class="form-text text-muted">請輸入完整的SMB網路路徑</small>
                </div>

                <div class="form-group">
                    <label class="form-label" for="siteId">對應工地 <span style="color: red;">*</span></label>
                    <select class="form-control" id="siteId" name="site_id" required>
                        <option value="">-- 選擇工地 --</option>
                        <?php foreach ($sites as $site): ?>
                        <option value="<?php echo $site['id']; ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="categoryId">預設文件類別</label>
                    <select class="form-control" id="categoryId" name="category_id">
                        <option value="">-- 自動判斷 --</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="uploadTime">每日上傳時間</label>
                    <input type="time" class="form-control" id="uploadTime" name="upload_time" value="09:00">
                </div>

                <div class="form-group">
                    <label class="form-label" for="fileExtensions">允許的檔案副檔名</label>
                    <input type="text" class="form-control" id="fileExtensions" name="file_extensions"
                           value="pdf,doc,docx,jpg,jpeg,png" placeholder="用逗號分隔，例如：pdf,doc,docx">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="scanSubdirs" name="scan_subdirs" checked>
                        掃描子目錄
                    </label>
                </div>

                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="button" class="btn btn-warning" onclick="testConnection()">測試連線</button>
                    <button type="submit" class="btn btn-success">儲存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 顯示新增模態視窗
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增自動上傳設定';
            document.getElementById('formAction').value = 'add';
            document.getElementById('configId').value = '';
            document.getElementById('configForm').reset();
            document.getElementById('scanSubdirs').checked = true;
            document.getElementById('uploadTime').value = '09:00';
            document.getElementById('fileExtensions').value = 'pdf,doc,docx,jpg,jpeg,png';
            document.getElementById('configModal').style.display = 'block';
        }

        // 編輯設定
        function editConfig(configId) {
            // 這裡需要通過AJAX獲取設定詳情
            fetch('get_config.php?id=' + configId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        document.getElementById('modalTitle').textContent = '編輯自動上傳設定';
                        document.getElementById('formAction').value = 'edit';
                        document.getElementById('configId').value = config.id;
                        document.getElementById('configName').value = config.name;
                        document.getElementById('smbPath').value = config.smb_path;
                        document.getElementById('siteId').value = config.site_id;
                        document.getElementById('categoryId').value = config.category_id || '';
                        document.getElementById('uploadTime').value = config.upload_time;
                        document.getElementById('fileExtensions').value = config.file_extensions;
                        document.getElementById('scanSubdirs').checked = config.scan_subdirs == 1;
                        document.getElementById('configModal').style.display = 'block';
                    } else {
                        alert('獲取設定失敗：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('獲取設定時發生錯誤');
                });
        }

        // 手動掃描功能
        function manualScan(configId) {
            const button = event.target.closest('.scan-btn');
            const originalText = button.innerHTML;

            // 禁用按鈕並顯示載入狀態
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 掃描中...';

            const formData = new FormData();
            formData.append('action', 'manual_scan');
            formData.append('config_id', configId);

            fetch('auto_upload_manager.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // 重新載入頁面以顯示結果
                window.location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('手動掃描時發生錯誤');

                // 恢復按鈕狀態
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // 切換設定狀態
        function toggleConfig(configId) {
            if (confirm('確定要切換此設定的狀態嗎？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle">
                    <input type="hidden" name="config_id" value="${configId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 刪除設定
        function deleteConfig(configId) {
            if (confirm('確定要刪除此設定嗎？此操作無法復原。')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="config_id" value="${configId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // 測試SMB連線
        function testConnection() {
            const smbPath = document.getElementById('smbPath').value;
            if (!smbPath) {
                alert('請先輸入SMB路徑');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'test_connection');
            formData.append('smb_path', smbPath);

            fetch('auto_upload_manager.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('連線測試成功！');
                } else {
                    alert('連線測試失敗：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('測試連線時發生錯誤');
            });
        }

        // 關閉模態視窗
        function closeModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // 點擊模態視窗外部關閉
        window.onclick = function(event) {
            const modal = document.getElementById('configModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // 主題切換功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // 更新主題切換按鈕
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (newTheme === 'dark') {
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切換淺色模式';
            } else {
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切換深色模式';
            }
        }

        // 通知功能
        function toggleNotifications(event) {
            event.stopPropagation();
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        function markAllAsRead(event) {
            event.stopPropagation();

            fetch('mark_notifications_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({action: 'mark_all_read'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新通知顯示
                    document.getElementById('notification-badge').style.display = 'none';
                    const notificationItems = document.querySelectorAll('.notification-item');
                    notificationItems.forEach(item => {
                        item.classList.remove('unread');
                        item.classList.add('read');
                    });
                    document.getElementById('mark-read-btn').style.display = 'none';
                }
            })
            .catch(error => console.error('Error:', error));
        }

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 載入儲存的主題
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (savedTheme === 'dark') {
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切換淺色模式';
            } else {
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切換深色模式';
            }

            // 點擊其他地方關閉通知下拉選單
            document.addEventListener('click', function() {
                document.getElementById('notificationDropdown').style.display = 'none';
            });

            // 移動選單切換
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mainNav = document.getElementById('mainNav');

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    mainNav.classList.toggle('show');
                });
            }
        });
    </script>
</body>
</html>
