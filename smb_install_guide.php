<?php
session_start();
require_once 'db.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

// 初始化用戶資訊
$site = $_SESSION['site'] ?? '總公司';
$username = $_SESSION['username'] ?? '';
$role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;
$role_mapping = [
    'admin' => '系統管理員',
    'user' => '一般用戶',
    'manager' => '主管'
];

// 檢查權限
$isAdmin = ($role === 'admin');
$isHqSupervisor = ($role === 'hq_supervisor');
$isHqStaff = ($site === '總公司');
$is_hq_role = ($isAdmin || $isHqSupervisor || $isHqStaff);

// 獲取通知
$notifications = [];
$notification_count = 0;
$all_notifications = [];
try {
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$user_id]);
    $all_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND `read` = 0");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $notification_count = $result ? $result['count'] : 0;
} catch (PDOException $e) {
    error_log("Error fetching notifications: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMB客戶端安裝指南 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #9c27b0;
            --info-hover: #7b1fa2;
            --dark-bg: #f5f5f5;
            --card-bg: #ffffff;
            --card-header: #f8f9fa;
            --table-header: #f8f9fa;
            --table-row-hover: #f5f5f5;
            --border-color: #e0e0e0;
            --text-color: #333333;
            --text-muted: #6c757d;
            --text-secondary: #6c757d;
            --light-bg: #f8f9fa;
            --navbar-bg: #ffffff;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        /* 暗黑模式變數 */
        body.theme-dark {
            --dark-bg: #121212;
            --card-bg: #1e1e1e;
            --card-header: #2d2d2d;
            --table-header: #2d2d2d;
            --table-row-hover: #2d2d2d;
            --border-color: #404040;
            --text-color: #ffffff;
            --text-muted: #b0b0b0;
            --text-secondary: #b0b0b0;
            --light-bg: #2d2d2d;
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft JhengHei', sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        /* 頂部導航樣式 */
        header {
            display: flex;
            align-items: center;
            background-color: var(--navbar-bg);
            padding: 0 20px;
            height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            margin-bottom: 0;
            padding-bottom: 0;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .logo-area {
            display: flex;
            align-items: center;
        }

        .logo-link {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-link img {
            height: 36px;
            margin-right: 10px;
        }

        .logo-link span {
            color: var(--navbar-text);
            font-size: 16px;
            font-weight: 600;
        }

        .main-nav {
            display: flex;
            align-items: center;
            margin-left: 20px;
            gap: 5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            color: var(--navbar-text);
            text-decoration: none;
            padding: 0 12px;
            height: 60px;
            transition: background-color 0.2s;
            font-size: 14px;
            white-space: nowrap;
        }

        .nav-item:hover {
            background-color: var(--navbar-hover);
        }

        .nav-item.active {
            background-color: var(--navbar-hover);
        }

        .nav-item i {
            margin-right: 7px;
            font-size: 14px;
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--navbar-text);
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .theme-toggle:hover {
            background-color: var(--navbar-hover);
        }

        .logout {
            color: #ea4335 !important;
            font-weight: 500;
        }

        .logout:hover {
            background-color: rgba(234, 67, 53, 0.1) !important;
        }

        .main-content {
            margin-top: 60px;
            padding: 30px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 0;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }

        .page-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color);
        }

        .card-body {
            padding: 30px;
        }

        .install-step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--light-bg);
        }

        .install-step h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 18px;
        }

        .install-step p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .warning-box {
            background-color: rgba(251, 188, 5, 0.1);
            border-left: 4px solid var(--warning-color);
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .warning-box i {
            color: var(--warning-color);
            margin-right: 8px;
        }

        .success-box {
            background-color: rgba(52, 168, 83, 0.1);
            border-left: 4px solid var(--secondary-color);
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .success-box i {
            color: var(--secondary-color);
            margin-right: 8px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-secondary {
            background-color: var(--text-muted);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }
    </style>
    <link rel="stylesheet" href="light-theme.css">
    <script src="theme-switcher.js" defer></script>
</head>
<body>
<header>
    <div class="logo-area">
        <a href="dashboard.php" class="logo-link">
            <img src="images/logo.png" alt="良有營造標誌">
            <span>良有營造電子簽核系統</span>
        </a>
    </div>

    <nav class="main-nav" id="mainNav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-tachometer-alt"></i> 簽核總覽
        </a>

        <a href="document_manager.php" class="nav-item">
            <i class="fas fa-file-alt"></i> 公文整理系統
        </a>

        <?php if ($isAdmin): ?>
        <a href="auto_upload_manager.php" class="nav-item">
            <i class="fas fa-robot"></i> 公文自動上傳管理
        </a>
        <?php endif; ?>
    </nav>

    <div class="user-actions" id="userActions">
        <button onclick="toggleTheme()" class="theme-toggle" title="切換主題">
            <i id="theme-icon" class="fas fa-sun"></i>
            <span id="theme-text">切換淺色模式</span>
        </button>
        <a href="logout.php" class="nav-item logout">
            <i class="fas fa-sign-out-alt"></i> <span class="logout-text">登出</span>
        </a>
    </div>
</header>

<div class="main-content">
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-download"></i> SMB客戶端安裝指南
        </h1>
        <div>
            <a href="auto_upload_manager.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回自動上傳管理
            </a>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="card-header">
            <h2><i class="fas fa-info-circle"></i> 系統需求</h2>
        </div>
        <div class="card-body">
            <p>為了使用公文自動上傳功能，系統需要安裝SMB客戶端來存取網路共享資料夾。以下是不同作業系統的安裝指南：</p>
            
            <div class="warning-box">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>重要提醒：</strong>安裝SMB客戶端需要系統管理員權限，請確保您有足夠的權限執行以下操作。
            </div>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="card-header">
            <h2><i class="fab fa-ubuntu"></i> Ubuntu/Debian 系統</h2>
        </div>
        <div class="card-body">
            <div class="install-step">
                <h3>步驟 1：更新套件列表</h3>
                <p>首先更新系統的套件列表：</p>
                <div class="code-block">sudo apt update</div>
            </div>

            <div class="install-step">
                <h3>步驟 2：安裝 SMB 客戶端</h3>
                <p>安裝 samba-client 套件：</p>
                <div class="code-block">sudo apt install samba-client cifs-utils -y</div>
            </div>

            <div class="install-step">
                <h3>步驟 3：驗證安裝</h3>
                <p>檢查 smbclient 是否正確安裝：</p>
                <div class="code-block">smbclient --version</div>
                <p>如果顯示版本資訊，表示安裝成功。</p>
            </div>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="card-header">
            <h2><i class="fab fa-redhat"></i> CentOS/RHEL/Rocky Linux 系統</h2>
        </div>
        <div class="card-body">
            <div class="install-step">
                <h3>步驟 1：安裝 SMB 客戶端</h3>
                <p>使用 yum 或 dnf 安裝：</p>
                <div class="code-block">
# CentOS 7/RHEL 7<br>
sudo yum install samba-client cifs-utils -y<br><br>
# CentOS 8+/RHEL 8+/Rocky Linux<br>
sudo dnf install samba-client cifs-utils -y
                </div>
            </div>

            <div class="install-step">
                <h3>步驟 2：驗證安裝</h3>
                <p>檢查 smbclient 是否正確安裝：</p>
                <div class="code-block">smbclient --version</div>
            </div>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="card-header">
            <h2><i class="fas fa-cog"></i> 設定與測試</h2>
        </div>
        <div class="card-body">
            <div class="install-step">
                <h3>步驟 1：測試 SMB 連線</h3>
                <p>使用以下指令測試是否能連接到 NAS：</p>
                <div class="code-block">smbclient //**************/公文 -U paper%liang55778010 -c "ls"</div>
                <p>如果能列出檔案，表示連線成功。</p>
            </div>

            <div class="install-step">
                <h3>步驟 2：設定檔案權限</h3>
                <p>確保 web 伺服器有權限執行 smbclient：</p>
                <div class="code-block">
# 檢查 smbclient 位置<br>
which smbclient<br><br>
# 確保權限正確<br>
sudo chmod +x /usr/bin/smbclient
                </div>
            </div>

            <div class="install-step">
                <h3>步驟 3：設定 SELinux（如果啟用）</h3>
                <p>如果系統啟用了 SELinux，可能需要額外設定：</p>
                <div class="code-block">
# 檢查 SELinux 狀態<br>
sestatus<br><br>
# 如果啟用，允許 httpd 執行網路連線<br>
sudo setsebool -P httpd_can_network_connect 1
                </div>
            </div>

            <div class="success-box">
                <i class="fas fa-check-circle"></i>
                <strong>安裝完成！</strong>現在您可以返回自動上傳管理頁面，測試 SMB 連線功能。
            </div>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="card-header">
            <h2><i class="fas fa-question-circle"></i> 常見問題</h2>
        </div>
        <div class="card-body">
            <div class="install-step">
                <h3>問題 1：權限被拒絕</h3>
                <p>如果出現權限錯誤，請檢查：</p>
                <ul style="margin-left: 20px;">
                    <li>確保使用正確的使用者名稱和密碼</li>
                    <li>檢查 NAS 上的共享資料夾權限設定</li>
                    <li>確認網路連線正常</li>
                </ul>
            </div>

            <div class="install-step">
                <h3>問題 2：找不到指令</h3>
                <p>如果系統提示找不到 smbclient 指令：</p>
                <ul style="margin-left: 20px;">
                    <li>重新執行安裝指令</li>
                    <li>檢查套件管理員是否有錯誤訊息</li>
                    <li>嘗試重新啟動系統</li>
                </ul>
            </div>

            <div class="install-step">
                <h3>問題 3：連線逾時</h3>
                <p>如果連線逾時：</p>
                <ul style="margin-left: 20px;">
                    <li>檢查防火牆設定</li>
                    <li>確認 NAS IP 位址正確</li>
                    <li>測試網路連線：<code>ping **************</code></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// 主題切換功能
function toggleTheme() {
    const body = document.body;
    const isDark = body.classList.contains('theme-dark');
    
    if (isDark) {
        body.classList.remove('theme-dark');
        body.classList.add('theme-light');
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.remove('theme-light');
        body.classList.add('theme-dark');
        localStorage.setItem('theme', 'dark');
    }

    // 更新主題切換按鈕
    const themeIcon = document.getElementById('theme-icon');
    const themeText = document.getElementById('theme-text');

    if (!isDark) {
        themeIcon.className = 'fas fa-moon';
        themeText.textContent = '切換淺色模式';
    } else {
        themeIcon.className = 'fas fa-sun';
        themeText.textContent = '切換深色模式';
    }
}

// 頁面載入時初始化
document.addEventListener('DOMContentLoaded', function() {
    // 載入儲存的主題
    const savedTheme = localStorage.getItem('theme') || 'light';
    const body = document.body;
    
    if (savedTheme === 'dark') {
        body.classList.add('theme-dark');
        body.classList.remove('theme-light');
    } else {
        body.classList.add('theme-light');
        body.classList.remove('theme-dark');
    }

    const themeIcon = document.getElementById('theme-icon');
    const themeText = document.getElementById('theme-text');

    if (savedTheme === 'dark') {
        themeIcon.className = 'fas fa-moon';
        themeText.textContent = '切換淺色模式';
    } else {
        themeIcon.className = 'fas fa-sun';
        themeText.textContent = '切換深色模式';
    }
});
</script>
</body>
</html>
