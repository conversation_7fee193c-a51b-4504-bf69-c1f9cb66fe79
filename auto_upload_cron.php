#!/usr/bin/env php
<?php
/**
 * 自動上傳排程腳本
 * 用於cron job定期執行自動上傳任務
 */

// 設置執行環境
set_time_limit(0);
ini_set('memory_limit', '512M');

// 記錄開始時間
$start_time = microtime(true);
$log_file = __DIR__ . '/logs/auto_upload_' . date('Y-m-d') . '.log';

// 確保日誌目錄存在
$log_dir = dirname($log_file);
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

// 日誌函數
function writeLog($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message\n";
}

writeLog("=== 自動上傳排程開始 ===");

try {
    // 載入必要檔案
    require_once __DIR__ . '/db.php';
    require_once __DIR__ . '/auto_upload_processor.php';
    
    writeLog("資料庫連接成功");
    
    // 檢查是否有需要執行的設定
    $current_time = date('H:i:s');
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM auto_upload_configs 
        WHERE is_active = 1 
        AND TIME(upload_time) <= TIME(?)
        AND (last_scan_time IS NULL OR DATE(last_scan_time) < CURDATE())
    ");
    $stmt->execute([$current_time]);
    $pending_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($pending_count == 0) {
        writeLog("沒有需要執行的自動上傳設定");
        writeLog("=== 自動上傳排程結束 ===");
        exit(0);
    }
    
    writeLog("發現 $pending_count 個需要執行的設定");
    
    // 建立處理器並執行
    $processor = new AutoUploadProcessor($pdo);
    
    // 獲取需要執行的設定
    $stmt = $pdo->prepare("
        SELECT * FROM auto_upload_configs 
        WHERE is_active = 1 
        AND TIME(upload_time) <= TIME(?)
        AND (last_scan_time IS NULL OR DATE(last_scan_time) < CURDATE())
        ORDER BY upload_time
    ");
    $stmt->execute([$current_time]);
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_uploaded = 0;
    $total_failed = 0;
    $total_errors = [];
    
    foreach ($configs as $config) {
        writeLog("開始處理設定: {$config['name']} ({$config['smb_path']})");
        
        $result = $processor->processConfig($config);
        
        writeLog("設定 {$config['name']} 處理完成:");
        writeLog("  - 掃描檔案: {$result['scanned_files']} 個");
        writeLog("  - 新檔案: {$result['new_files']} 個");
        writeLog("  - 成功上傳: {$result['uploaded_files']} 個");
        writeLog("  - 失敗: {$result['failed_files']} 個");
        
        $total_uploaded += $result['uploaded_files'];
        $total_failed += $result['failed_files'];
        
        if (!empty($result['errors'])) {
            writeLog("  錯誤訊息:");
            foreach ($result['errors'] as $error) {
                writeLog("    - $error");
                $total_errors[] = "{$config['name']}: $error";
            }
        }
        
        // 更新最後掃描時間
        $stmt = $pdo->prepare("UPDATE auto_upload_configs SET last_scan_time = NOW() WHERE id = ?");
        $stmt->execute([$config['id']]);
        
        writeLog("設定 {$config['name']} 處理完成\n");
    }
    
    // 總結報告
    $end_time = microtime(true);
    $execution_time = round($end_time - $start_time, 2);
    
    writeLog("=== 執行總結 ===");
    writeLog("處理設定數量: " . count($configs));
    writeLog("總上傳成功: $total_uploaded 個檔案");
    writeLog("總失敗: $total_failed 個檔案");
    writeLog("執行時間: {$execution_time} 秒");
    
    if (!empty($total_errors)) {
        writeLog("錯誤總覽:");
        foreach ($total_errors as $error) {
            writeLog("  - $error");
        }
    }
    
    // 如果有重要錯誤，發送通知（可選）
    if ($total_failed > 0) {
        writeLog("檢測到上傳失敗，建議檢查系統狀態");
        // 這裡可以添加郵件通知功能
    }
    
    writeLog("=== 自動上傳排程結束 ===");
    
} catch (Exception $e) {
    writeLog("嚴重錯誤: " . $e->getMessage());
    writeLog("堆疊追蹤: " . $e->getTraceAsString());
    writeLog("=== 自動上傳排程異常結束 ===");
    exit(1);
}
?>
