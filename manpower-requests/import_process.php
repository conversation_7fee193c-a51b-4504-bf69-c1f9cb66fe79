<?php
// 啟用輸出緩衝，避免 header() 函數錯誤
ob_start();

// 添加錯誤處理與日誌記錄
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/../error_log.txt');
set_time_limit(120); // 增加腳本執行時間限制

// 確保用戶已登入
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// 引入數據庫連接
require_once '../db_service.php';

// 初始化服務
$db = DBService::getInstance();

// CSV格式檢測函數
function detectCSVFormat($header1, $header2, $header3) {
    // 記錄調試信息
    error_log("格式檢測 - 標題行1: " . json_encode($header1, JSON_UNESCAPED_UNICODE));

    // 檢測403中繼屋格式
    if ($header1 && count($header1) > 2 && strpos($header1[2], '403中繼屋') !== false) {
        error_log("格式檢測 - 檢測到403中繼屋格式");
        return '403中繼屋';
    }

    // 檢測慈濟五期格式 - 更寬鬆的檢測條件
    if ($header1 && count($header1) > 7) {
        // 檢查是否包含「五期」或「慈濟」相關關鍵字
        $header_string = implode(',', $header1);
        if (strpos($header_string, '五期') !== false ||
            strpos($header_string, '慈濟') !== false ||
            (strpos($header_string, '中道') !== false && strpos($header_string, '大亞') !== false)) {
            error_log("格式檢測 - 檢測到慈濟五期格式");
            return '慈濟五期';
        }
    }

    // 檢測美崙格式（預設格式）
    error_log("格式檢測 - 使用美崙格式（預設）");
    return '美崙';
}

// 獲取格式配置函數
function getFormatConfig($format, $header1, $header2, $header3) {
    switch ($format) {
        case '403中繼屋':
            return get403Config($header1, $header2, $header3);
        case '慈濟五期':
            return getCijiConfig($header1, $header2, $header3);
        case '美崙':
        default:
            return getMeilunConfig($header1, $header2, $header3);
    }
}

// 403中繼屋格式配置
function get403Config($header1, $header2, $header3) {
    $companies = [];

    // 從第一行找廠商名稱（通全、隆承、翔琰、豐科、清煌等）
    if ($header1 && count($header1) > 11) {
        $company_start_index = 11; // 從第12列開始是廠商
        for ($i = $company_start_index; $i < count($header1); $i++) {
            $company_name = trim($header1[$i]);
            if (!empty($company_name) && $company_name !== '備註' && $company_name !== '') {
                $companies[$i] = $company_name;
            }
        }
    }

    return [
        'companies' => $companies,
        'detected_site_name' => '主商段大愛屋（慈濟403）', // 僅供參考，不使用
        'date_column' => 0, // 日期在第1列
        'work_description_column' => 3, // 工作內容在第4列
        'total_workers_column' => 1 // 今日工數在第2列
    ];
}

// 慈濟五期格式配置
function getCijiConfig($header1, $header2, $header3) {
    $companies = [];

    // 從第一行找廠商名稱，記錄調試信息
    error_log("慈濟五期配置 - 標題行長度: " . (is_array($header1) ? count($header1) : 0));

    if ($header1 && count($header1) > 15) {
        // 從第16列開始搜尋廠商名稱，因為前面是其他欄位
        for ($i = 15; $i < count($header1); $i++) {
            $company_name = trim($header1[$i]);
            error_log("慈濟五期配置 - 欄位 $i: '$company_name'");

            // 跳過空白或無效的廠商名稱
            if (!empty($company_name) &&
                $company_name !== '備註' &&
                $company_name !== '' &&
                $company_name !== '小計' &&
                $company_name !== '總計') {
                $companies[$i] = $company_name;
                error_log("慈濟五期配置 - 添加廠商: $company_name (欄位 $i)");
            }
        }
    }

    error_log("慈濟五期配置 - 找到的廠商列表: " . json_encode($companies, JSON_UNESCAPED_UNICODE));

    return [
        'companies' => $companies,
        'detected_site_name' => '花蓮慈濟醫院（慈濟五期）', // 僅供參考，不使用
        'date_column' => 0, // 日期在第1列
        'work_description_column' => 8, // 工作內容在第9列
        'total_workers_column' => 1, // 今日工數在第2列
        'skip_header_rows' => 1 // 慈濟五期只需要跳過1行標題，第2、3行是數據
    ];
}

// 美崙格式配置
function getMeilunConfig($header1, $header2, $header3) {
    $companies = [];

    // 從標題行中動態獲取廠商名稱（從第4列開始，跳過日期、總人數、工作描述）
    if ($header3 && count($header3) > 3) {
        for ($i = 3; $i < count($header3); $i++) {
            $company_name = trim($header3[$i]);
            // 跳過空白或無效的廠商名稱，以及備註欄位和小計
            if (!empty($company_name) &&
                $company_name !== '備註' &&
                $company_name !== 'remarks' &&
                $company_name !== '說明' &&
                $company_name !== '小計') {
                $companies[$i] = $company_name;
            }
        }
    }

    // 如果沒有找到廠商，使用預設的廠商列表作為備用
    if (empty($companies)) {
        $companies = [
            3 => '良有營造',
            4 => '冠凌',
            5 => '國富',
            6 => '祈勝',
            7 => '亞東',
            8 => '鳳勝'
        ];
    }

    return [
        'companies' => $companies,
        'detected_site_name' => '美崙', // 僅供參考，不使用
        'date_column' => 0, // 日期在第1列
        'work_description_column' => 2, // 工作描述在第3列
        'total_workers_column' => 1 // 總人數在第2列
    ];
}

// 解析行數據函數
function parseLineData($line, $format, $config) {
    if ($format === '403中繼屋') {
        return parse403LineData($line, $config);
    } else if ($format === '慈濟五期') {
        return parseCijiLineData($line, $config);
    } else {
        return parseMeilunLineData($line, $config);
    }
}

// 解析403中繼屋格式的行數據
function parse403LineData($line, $config) {
    // 檢查是否為有效數據行
    if (count($line) < 4 || empty($line[0])) {
        return false;
    }

    // 解析日期（格式：2月1日）
    $date_str = trim($line[0]);
    if (!preg_match('/(\d+)月(\d+)日/', $date_str, $matches)) {
        return false;
    }

    $day = intval($matches[2]);
    $total_workers = isset($line[1]) ? intval(trim($line[1])) : 0;
    $work_description = isset($line[3]) ? trim($line[3]) : '';

    // 如果沒有工人數量，跳過
    if ($total_workers <= 0) {
        return false;
    }

    return [
        'day' => $day,
        'total_workers' => $total_workers,
        'work_description' => $work_description
    ];
}

// 解析慈濟五期格式的行數據
function parseCijiLineData($line, $config) {
    // 檢查是否為有效數據行
    if (count($line) < 9 || empty($line[0])) {
        return false;
    }

    // 解析日期（格式：2月1日）
    $date_str = trim($line[0]);
    if (!preg_match('/(\d+)月(\d+)日/', $date_str, $matches)) {
        return false;
    }

    $day = intval($matches[2]);

    // 更仔細地解析工人數量
    $workers_raw = isset($line[1]) ? trim($line[1]) : '';
    $total_workers = 0;

    // 嘗試不同的解析方式
    if (is_numeric($workers_raw)) {
        $total_workers = intval($workers_raw);
    } else {
        // 如果包含非數字字符，嘗試提取數字
        if (preg_match('/(\d+)/', $workers_raw, $worker_matches)) {
            $total_workers = intval($worker_matches[1]);
        }
    }

    // 記錄調試信息
    error_log("慈濟五期解析 - 第{$day}天: 原始工人數='{$workers_raw}', 解析後={$total_workers}");

    // 工作內容可能在第8列或第10列
    $work_description = '';
    if (isset($line[8]) && !empty(trim($line[8]))) {
        $work_description = trim($line[8]);
    } else if (isset($line[10]) && !empty(trim($line[10]))) {
        $work_description = trim($line[10]);
    } else {
        $work_description = '一般工作'; // 預設描述
    }

    error_log("慈濟五期解析 - 第{$day}天: 工作描述='{$work_description}'");

    // 如果沒有工人數量，跳過
    if ($total_workers <= 0) {
        error_log("慈濟五期解析 - 第{$day}天: 跳過，工人數量={$total_workers}");
        return false;
    }

    return [
        'day' => $day,
        'total_workers' => $total_workers,
        'work_description' => $work_description
    ];
}

// 解析美崙格式的行數據
function parseMeilunLineData($line, $config) {
    // 檢查是否為空行或結束行
    if (count($line) < 3 || empty($line[0]) || !is_numeric(trim($line[0]))) {
        return false;
    }

    $day = intval(trim($line[0]));
    $total_workers = intval(trim($line[1]));
    $work_description = trim($line[2]);

    // 如果沒有工人數量，跳過
    if ($total_workers <= 0) {
        return false;
    }

    return [
        'day' => $day,
        'total_workers' => $total_workers,
        'work_description' => $work_description
    ];
}

// 用戶資訊
$user_id = $_SESSION['user_id'];
$role = $_SESSION['role'];
$username = $_SESSION['username'] ?? '';
$site = $_SESSION['site'] ?? '';

// 檢查權限（只允許管理員或工地主管）
if ($role !== 'admin' && $role !== 'site_supervisor') {
    $_SESSION['error'] = '您沒有權限執行此操作';
    header('Location: export_form.php');
    exit;
}

// 初始化消息
$error_message = '';
$success_message = '';

// 處理表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 檢查是否有文件上傳
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('請選擇一個CSV文件');
        }

        // 檢查文件類型
        $file_info = pathinfo($_FILES['csv_file']['name']);
        if (strtolower($file_info['extension']) !== 'csv') {
            throw new Exception('請上傳CSV格式的文件');
        }

        // 獲取工地ID
        $site_id = isset($_POST['site_id']) ? intval($_POST['site_id']) : 0;
        if ($site_id <= 0) {
            throw new Exception('請選擇工地');
        }

        // 獲取工地信息
        $site = $db->fetchOne("SELECT * FROM sites WHERE id = ?", [$site_id]);
        if (!$site) {
            throw new Exception('找不到指定的工地');
        }

        // 獲取月份
        $month = isset($_POST['month']) ? $_POST['month'] : '';
        if (empty($month) || !preg_match('/^\d{4}-\d{2}$/', $month)) {
            throw new Exception('請選擇有效的月份（格式：YYYY-MM）');
        }

        // 解析月份
        list($year, $month_num) = explode('-', $month);
        $year = intval($year);
        $month_num = intval($month_num);

        // 開始事務
        $db->beginTransaction();

        // 讀取CSV文件
        $file = fopen($_FILES['csv_file']['tmp_name'], 'r');
        if (!$file) {
            if ($db->getPdo()->inTransaction()) {
                $db->rollback(); // 回滾事務
            }
            throw new Exception('無法打開CSV文件');
        }

        // 讀取前三行用於格式檢測
        $header1 = fgetcsv($file);
        $header2 = fgetcsv($file);
        $header3 = fgetcsv($file);

        // 記錄標題行，幫助調試
        error_log("標題行1：" . json_encode($header1, JSON_UNESCAPED_UNICODE));
        error_log("標題行2：" . json_encode($header2, JSON_UNESCAPED_UNICODE));
        error_log("標題行3：" . json_encode($header3, JSON_UNESCAPED_UNICODE));

        // 檢測CSV格式類型
        $csv_format = detectCSVFormat($header1, $header2, $header3);
        error_log("檢測到的CSV格式：" . $csv_format);

        // 根據格式獲取廠商列表和相關配置
        $format_config = getFormatConfig($csv_format, $header1, $header2, $header3);

        // 根據格式決定需要跳過的標題行數
        $skip_header_rows = isset($format_config['skip_header_rows']) ? $format_config['skip_header_rows'] : 3;

        // 重新定位文件指針
        rewind($file);
        for ($i = 0; $i < $skip_header_rows; $i++) {
            fgetcsv($file);
        }
        $companies = $format_config['companies'];
        $site_name = $site['name']; // 使用用戶選擇的工地名稱，不是自動檢測的
        $date_column = $format_config['date_column'];
        $work_description_column = $format_config['work_description_column'];

        // 記錄找到的廠商列表和配置
        error_log("找到的廠商列表：" . json_encode($companies, JSON_UNESCAPED_UNICODE));
        error_log("用戶選擇的工地名稱：" . $site_name);
        error_log("自動檢測的工地名稱：" . $format_config['detected_site_name']);
        error_log("日期欄位：" . $date_column);
        error_log("工作描述欄位：" . $work_description_column);

        $imported_count = 0;
        $skipped_count = 0;
        $error_lines = [];

        // 逐行處理CSV數據
        while (($line = fgetcsv($file)) !== false) {
            // 記錄原始行數據，幫助調試
            error_log("原始行數據：" . json_encode($line, JSON_UNESCAPED_UNICODE));

            // 根據格式解析數據
            $parsed_data = parseLineData($line, $csv_format, $format_config);
            if (!$parsed_data) {
                error_log("跳過行：不是有效的數據行");
                continue;
            }

            $day = $parsed_data['day'];
            $total_workers = $parsed_data['total_workers'];
            $work_description = $parsed_data['work_description'];

            // 記錄處理後的數據，幫助調試
            error_log("處理後的數據：日期=$day，工人數量=$total_workers，工作描述=$work_description");

            // 如果沒有工人，跳過
            if ($total_workers <= 0) {
                $skipped_count++;
                continue;
            }

            // 構建工作日期
            $work_date = sprintf('%04d-%02d-%02d', $year, $month_num, $day);

            // 檢查日期是否有效
            if (!checkdate($month_num, $day, $year)) {
                $error_lines[] = "第 $day 天：日期無效";
                continue;
            }

            // 檢查是否已存在該日期的記錄
            $existing = $db->fetchOne(
                "SELECT id FROM manpower_requests WHERE site = ? AND work_date = ?",
                [$site_name, $work_date]
            );

            if ($existing) {
                $error_lines[] = "第 $day 天：該日期已存在人力需求記錄";
                continue;
            }

            // 創建人力需求記錄
            $request_data = [
                'site' => $site_name, // 使用檢測到的工地名稱
                'requester_id' => $user_id,
                'request_date' => date('Y-m-d'),
                'work_date' => $work_date,
                'status' => 'approved', // 直接設為已批准
                'approver_id' => $user_id,
                'approval_date' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $request_id = $db->insert('manpower_requests', $request_data);

            if (!$request_id) {
                $error_lines[] = "第 $day 天：創建人力需求記錄失敗";
                continue;
            }

            // 創建工作項目
            $item_data = [
                'request_id' => $request_id,
                'work_description' => $work_description,
                'workers_required' => $total_workers,
                'company_preference' => '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $item_id = $db->insert('manpower_request_items', $item_data);

            if (!$item_id) {
                $error_lines[] = "第 $day 天：創建工作項目失敗";
                continue;
            }

            // 記錄日誌，幫助調試
            error_log("處理第 $day 天的數據：工人數量 $total_workers，工作描述：$work_description");

            foreach ($companies as $index => $company_name) {
                // 檢查該欄位是否存在於當前行中
                if (isset($line[$index])) {
                    $cell_value = trim($line[$index]);

                    // 記錄每個廠商的原始數據
                    error_log("第 $day 天：$company_name (欄位 $index) 原始值: '$cell_value'");

                    // 如果有工人數量（大於0且不是'-'）
                    if (!empty($cell_value) && $cell_value !== '-' && floatval($cell_value) > 0) {
                        $worker_count = floatval($cell_value);

                        // 記錄日誌，幫助調試
                        error_log("第 $day 天：$company_name 的工人數量為 $worker_count");

                        // 創建實際用工記錄
                        $record_data = [
                            'request_id' => $request_id,
                            'record_date' => $work_date, // 添加記錄日期
                            'site' => $site_name, // 使用檢測到的工地名稱
                            'recorder_id' => $user_id, // 添加記錄者ID
                            'company_name' => $company_name,
                            'worker_count' => $worker_count,
                            'work_description' => $work_description,
                            'work_location' => '',
                            'remarks' => '', // 備註欄位可能在不同位置，暫時留空
                            'is_overtime' => 0, // 普通工作
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];

                        $record_id = $db->insert('manpower_records', $record_data);

                        if (!$record_id) {
                            $error_lines[] = "第 $day 天：創建 $company_name 的實際用工記錄失敗";
                            error_log("第 $day 天：創建 $company_name 的實際用工記錄失敗");
                        } else {
                            error_log("第 $day 天：成功創建 $company_name 的實際用工記錄，ID: $record_id");
                        }
                    } else {
                        error_log("第 $day 天：$company_name 沒有工人或數量為零，跳過");
                    }
                } else {
                    error_log("第 $day 天：$company_name 對應的欄位 $index 不存在於當前行中");
                }
            }

            $imported_count++;
        }

        // 關閉文件
        fclose($file);

        // 提交事務
        $db->commit();

        // 設置成功消息
        $_SESSION['success'] = "成功匯入 $imported_count 天的人力需求和實際用工記錄";
        if ($skipped_count > 0) {
            $_SESSION['success'] .= "，跳過 $skipped_count 天（無工人）";
        }
        if (!empty($error_lines)) {
            $_SESSION['success'] .= "。但有以下錯誤：<br>" . implode("<br>", $error_lines);
        }

        // 重定向回表單頁面
        header('Location: export_form.php');
        exit;

    } catch (Exception $e) {
        // 回滾事務
        if ($db->getPdo()->inTransaction()) {
            $db->rollback();
        }
        $_SESSION['error'] = '匯入失敗：' . $e->getMessage();
        error_log('匯入CSV失敗：' . $e->getMessage());

        // 重定向回表單頁面
        header('Location: export_form.php');
        exit;
    }
} else {
    // 如果不是POST請求，重定向回表單頁面
    header('Location: export_form.php');
    exit;
}
