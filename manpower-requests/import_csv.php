<?php
// 啟用輸出緩衝，避免 header() 函數錯誤
ob_start();

// 添加錯誤處理與日誌記錄
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/../error_log.txt');
set_time_limit(120); // 增加腳本執行時間限制

// 確保用戶已登入
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// 引入數據庫連接
require_once '../db_service.php';

// 初始化服務
$db = DBService::getInstance();

// 用戶資訊
$user_id = $_SESSION['user_id'];
$role = $_SESSION['role'];
$username = $_SESSION['username'] ?? '';
$site = $_SESSION['site'] ?? '';

// 獲取用戶資訊
function getUserData($db, $user_id, &$session) {
    if (!isset($session['name']) || empty($session['name']) || !isset($session['position_name'])) {
        $userData = $db->fetchOne(
            "SELECT u.name, u.site, u.signature_path, u.position_id, p.name as position_name
             FROM users u
             LEFT JOIN positions p ON u.position_id = p.id
             WHERE u.id = ?",
            [$user_id]
        );

        if ($userData) {
            $session['name'] = $userData['name'];
            $session['site'] = $userData['site'];
            $session['signature_path'] = $userData['signature_path'];
            $session['position_id'] = $userData['position_id'];
            $session['position_name'] = $userData['position_name'];
            return $userData['name'];
        }
        return '未知姓名';
    }
    return $session['name'];
}

// 獲取使用者資訊
$fullname = getUserData($db, $user_id, $_SESSION);
$site = $_SESSION['site'];

// 檢查權限（只允許管理員或工地主管）
if ($role !== 'admin' && $role !== 'site_supervisor') {
    header('Location: index.php?error=1&message=' . urlencode('您沒有權限執行此操作'));
    exit;
}

// 初始化數據庫連接
$db = DBService::getInstance();

// 初始化消息
$error_message = '';
$success_message = '';

// 處理表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 檢查是否有文件上傳
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('請選擇一個CSV文件');
        }

        // 檢查文件類型
        $file_info = pathinfo($_FILES['csv_file']['name']);
        if (strtolower($file_info['extension']) !== 'csv') {
            throw new Exception('請上傳CSV格式的文件');
        }

        // 獲取工地ID
        $site_id = isset($_POST['site_id']) ? intval($_POST['site_id']) : 0;
        if ($site_id <= 0) {
            throw new Exception('請選擇工地');
        }

        // 獲取工地信息
        $site = $db->fetchOne("SELECT * FROM sites WHERE id = ?", [$site_id]);
        if (!$site) {
            throw new Exception('找不到指定的工地');
        }

        // 獲取月份
        $month = isset($_POST['month']) ? $_POST['month'] : '';
        if (empty($month) || !preg_match('/^\d{4}-\d{2}$/', $month)) {
            throw new Exception('請選擇有效的月份（格式：YYYY-MM）');
        }

        // 解析月份
        list($year, $month_num) = explode('-', $month);
        $year = intval($year);
        $month_num = intval($month_num);

        // 開始事務
        $db->beginTransaction();

        // 讀取CSV文件
        $file = fopen($_FILES['csv_file']['tmp_name'], 'r');
        if (!$file) {
            if ($db->getPdo()->inTransaction()) {
                $db->rollback(); // 回滾事務
            }
            throw new Exception('無法打開CSV文件');
        }

        // 跳過前三行（標題行）
        $header1 = fgetcsv($file);
        $header2 = fgetcsv($file);
        $header3 = fgetcsv($file);

        // 記錄標題行，幫助調試
        error_log("標題行1：" . json_encode($header1, JSON_UNESCAPED_UNICODE));
        error_log("標題行2：" . json_encode($header2, JSON_UNESCAPED_UNICODE));
        error_log("標題行3：" . json_encode($header3, JSON_UNESCAPED_UNICODE));

        $imported_count = 0;
        $skipped_count = 0;
        $error_lines = [];

        // 逐行處理CSV數據
        while (($line = fgetcsv($file)) !== false) {
            // 記錄原始行數據，幫助調試
            error_log("原始行數據：" . json_encode($line, JSON_UNESCAPED_UNICODE));

            // 檢查是否為空行或結束行
            if (count($line) < 3 || empty($line[0]) || !is_numeric(trim($line[0]))) {
                error_log("跳過行：不是有效的數據行");
                continue;
            }

            $day = intval(trim($line[0]));
            $total_workers = intval(trim($line[1]));
            $work_description = trim($line[2]);

            // 記錄處理後的數據，幫助調試
            error_log("處理後的數據：日期=$day，工人數量=$total_workers，工作描述=$work_description");

            // 如果沒有工人，跳過
            if ($total_workers <= 0) {
                $skipped_count++;
                continue;
            }

            // 構建工作日期
            $work_date = sprintf('%04d-%02d-%02d', $year, $month_num, $day);

            // 檢查日期是否有效
            if (!checkdate($month_num, $day, $year)) {
                $error_lines[] = "第 $day 天：日期無效";
                continue;
            }

            // 檢查是否已存在該日期的記錄
            $existing = $db->fetchOne(
                "SELECT id FROM manpower_requests WHERE site = ? AND work_date = ?",
                [$site['name'], $work_date]
            );

            if ($existing) {
                $error_lines[] = "第 $day 天：該日期已存在人力需求記錄";
                continue;
            }

            // 創建人力需求記錄
            $request_data = [
                'site' => $site['name'],
                'requester_id' => $user_id,
                'request_date' => date('Y-m-d'),
                'work_date' => $work_date,
                'status' => 'approved', // 直接設為已批准
                'approver_id' => $user_id,
                'approval_date' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $request_id = $db->insert('manpower_requests', $request_data);

            if (!$request_id) {
                $error_lines[] = "第 $day 天：創建人力需求記錄失敗";
                continue;
            }

            // 創建工作項目
            $item_data = [
                'request_id' => $request_id,
                'work_description' => $work_description,
                'workers_required' => $total_workers,
                'company_preference' => '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $item_id = $db->insert('manpower_request_items', $item_data);

            if (!$item_id) {
                $error_lines[] = "第 $day 天：創建工作項目失敗";
                continue;
            }

            // 處理各公司的實際用工記錄（排除小計）
            $companies = [
                3 => '良有營造', // 索引3對應CSV中的第4列
                4 => '冠凌',
                5 => '國富',
                6 => '祈勝',
                7 => '亞東',
                8 => '鳳勝'
                // 注意：不包含「小計」
            ];

            // 記錄日誌，幫助調試
            error_log("處理第 $day 天的數據：工人數量 $total_workers，工作描述：$work_description");

            foreach ($companies as $index => $company_name) {
                if (isset($line[$index]) && !empty($line[$index]) && trim($line[$index]) !== '-' && floatval(trim($line[$index])) > 0) {
                    $worker_count = floatval(trim($line[$index]));

                    // 記錄日誌，幫助調試
                    error_log("第 $day 天：$company_name 的工人數量為 $worker_count");

                    // 創建實際用工記錄
                    $record_data = [
                        'request_id' => $request_id,
                        'company_name' => $company_name,
                        'worker_count' => $worker_count,
                        'work_description' => $work_description,
                        'work_location' => '',
                        'remarks' => isset($line[10]) && !empty($line[10]) ? $line[10] : '',
                        'is_overtime' => 0, // 普通工作
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $record_id = $db->insert('manpower_records', $record_data);

                    if (!$record_id) {
                        $error_lines[] = "第 $day 天：創建 $company_name 的實際用工記錄失敗";
                    } else {
                        error_log("第 $day 天：成功創建 $company_name 的實際用工記錄，ID: $record_id");
                    }
                } else {
                    error_log("第 $day 天：$company_name 沒有工人或數量為零，跳過");
                }
            }

            $imported_count++;
        }

        // 關閉文件
        fclose($file);

        // 提交事務
        $db->commit();

        $success_message = "成功匯入 $imported_count 天的人力需求和實際用工記錄";
        if ($skipped_count > 0) {
            $success_message .= "，跳過 $skipped_count 天（無工人）";
        }
        if (!empty($error_lines)) {
            $success_message .= "。但有以下錯誤：<br>" . implode("<br>", $error_lines);
        }

    } catch (Exception $e) {
        // 回滾事務
        if ($db->getPdo()->inTransaction()) {
            $db->rollback();
        }
        $error_message = '匯入失敗：' . $e->getMessage();
        error_log('匯入CSV失敗：' . $e->getMessage());
    }
}

// 獲取所有工地
$sites = [];
if ($role === 'admin') {
    // 管理員可以看到所有工地
    $sites = $db->fetchAll("SELECT * FROM sites ORDER BY name");
} else {
    // 工地主管只能看到自己的工地
    // 先獲取用戶的工地ID
    $site_info = $db->fetchOne("SELECT s.id FROM sites s WHERE s.name = ?", [$site]);
    $site_id = $site_info ? $site_info['id'] : 0;

    if ($site_id > 0) {
        $sites = $db->fetchAll("SELECT * FROM sites WHERE id = ? ORDER BY name", [$site_id]);
    } else {
        // 如果找不到工地ID，顯示錯誤消息
        $error_message = '無法確定您的工地，請聯繫系統管理員';
    }
}

// 映射表
$role_mapping = [
    'admin' => '系統管理者',
    'site_supervisor' => '工地主管',
    'hq_supervisor' => '總公司主管',
    'employee' => '員工'
];
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匯入CSV數據 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../dark-theme.css">
    <link rel="stylesheet" href="../light-theme.css">
    <link rel="stylesheet" href="../responsive.css">
    <link rel="stylesheet" href="../navbar.css">
    <link rel="stylesheet" href="user-info-style.css">
    <link rel="stylesheet" href="user-info-color.css">
    <link rel="stylesheet" href="force-animation.css">
    <link rel="stylesheet" href="compact-user-info.css">
    <!-- 使用簡化版主題切換器 -->
    <script src="theme-switcher-custom.js" defer></script>
    <!-- 強制設置用戶信息顏色 -->
    <script src="force-user-info-color.js" defer></script>
    <!-- 強制設置用戶信息字體大小 -->
    <style>
        /* 使用最高優先級的選擇器強制設置字體大小 */
        .user-info i.fas.fa-user-circle,
        .user-info i.fas.fa-user-circle[style],
        .user-info > i.fas.fa-user-circle,
        .user-info > i.fas.fa-user-circle[style] {
            font-size: 14px !important;
        }

        .user-name,
        .user-name[style],
        .user-info .user-name,
        .user-info .user-name[style],
        .user-info > .user-info-details > .user-name,
        .user-info > .user-info-details > .user-name[style] {
            font-size: 14px !important;
        }

        .user-position,
        .user-position[style],
        .user-info .user-position,
        .user-info .user-position[style],
        .user-info > .user-info-details > .user-position,
        .user-info > .user-info-details > .user-position[style] {
            font-size: 11px !important;
        }

        .user-position i,
        .user-position i[style],
        .user-info .user-position i,
        .user-info .user-position i[style],
        .user-info > .user-info-details > .user-position i,
        .user-info > .user-info-details > .user-position i[style] {
            font-size: 11px !important;
        }

        .user-site,
        .user-site[style],
        .user-info .user-site,
        .user-info .user-site[style],
        .user-info > .user-info-details > .user-site,
        .user-info > .user-info-details > .user-site[style] {
            font-size: 14px !important;
        }

        .notification-icon i.fas.fa-bell,
        .notification-icon i.fas.fa-bell[style] {
            font-size: 14px !important;
        }

        .import-form {
            max-width: 800px;
            margin: 0 auto;
        }
        .import-instructions {
            background-color: var(--bg-tertiary);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .import-instructions h3 {
            margin-top: 0;
            color: var(--text-primary);
        }
        .import-instructions ol {
            margin-bottom: 0;
            color: var(--text-secondary);
        }

        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-header h2 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-input);
            color: var(--text-primary);
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .alert {
            padding: 12px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .alert i {
            margin-right: 10px;
            font-size: 16px;
        }

        .alert-danger {
            background-color: rgba(234, 67, 53, 0.1);
            border: 1px solid rgba(234, 67, 53, 0.2);
            color: #ea4335;
        }

        .alert-success {
            background-color: rgba(52, 168, 83, 0.1);
            border: 1px solid rgba(52, 168, 83, 0.2);
            color: #34a853;
        }

        .main-content {
            margin-top: 55px;
            padding: 30px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .page-header {
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 24px;
            margin: 0;
            color: var(--text-primary);
        }
    </style>
    <script>
    // 確保在DOM載入後立即執行
    document.addEventListener('DOMContentLoaded', function() {
        // 立即強制設置導覽條顏色
        function forceNavbarColor() {
            const navbar = document.querySelector('.navbar');
            if (!navbar) return;

            // 檢查當前主題
            const currentTheme = localStorage.getItem('theme') || 'dark';

            // 強制設置內聯樣式（優先級最高）
            if (currentTheme === 'light') {
                navbar.style.setProperty('background-color', '#4361EE', 'important');
                console.log('設置淺色模式導覽條顏色: #4361EE');
            } else {
                navbar.style.setProperty('background-color', '#1e1e1e', 'important');
                console.log('設置深色模式導覽條顏色: #1e1e1e');
            }
        }

        // 初始設置顏色
        forceNavbarColor();

        // 監聽主題變化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class' && mutation.target === document.body) {
                    forceNavbarColor();
                }
            });
        });

        // 監視body的class變化
        observer.observe(document.body, { attributes: true });
    });
    </script>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-brand">
            <a href="../dashboard.php">
                <img src="../images/logo.png" alt="良有營造" class="logo">
                <span>良有營造電子簽核系統</span>
            </a>
        </div>

        <ul class="navbar-nav" id="navbarNav" style="display: flex !important; flex-direction: row !important; justify-content: center !important; align-items: center !important; margin: 0; padding: 0; list-style: none; flex: 1;">
            <li class="nav-item">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> 簽核總覽
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-users-cog"></i> 人力需求派工
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="history.php">
                    <i class="fas fa-history"></i> 歷史記錄
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar"></i> 統計報表
                </a>
            </li>
            <li class="nav-item">
                <a href="export_form.php" class="nav-link">
                    <i class="fas fa-file-export"></i> 匯出報表
                </a>
            </li>
            <?php if ($role == 'admin' || $role == 'site_supervisor'): ?>
            <li class="nav-item">
                <a href="import_csv.php" class="nav-link active">
                    <i class="fas fa-file-import"></i> 匯入CSV
                </a>
            </li>
            <?php endif; ?>

            <li class="nav-item">
                <a class="nav-link" href="#" id="theme-toggle">
                    <i class="fas fa-sun"></i>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link logout" href="../logout.php" style="color: #ea4335 !important; font-weight: bold; background-color: transparent !important;">
                    <i class="fas fa-sign-out-alt" style="color: #ea4335 !important;"></i> 登出
                </a>
            </li>
        </ul>

        <div class="navbar-user">
            <div class="user-info">
                <i class="fas fa-user-circle" style="color: #4285f4 !important; font-size: 14px;"></i>
                <span class="user-name" style="color: #ffffff !important; font-size: 14px;"><?php echo htmlspecialchars($fullname); ?></span>
                <span class="user-role" style="color: #34a853 !important; font-size: 11px;">
                    <i class="fas fa-id-badge" style="font-size: 11px;"></i>
                    <?php
                    $role_mapping = [
                        'admin' => '系統管理者',
                        'site_supervisor' => '工地主管',
                        'hq_supervisor' => '總公司主管',
                        'employee' => '員工'
                    ];
                    echo htmlspecialchars($role_mapping[$role] ?? '未知角色');
                    ?>
                </span>
                <span class="user-site" style="color: #aaaaaa !important; font-size: 14px;"><?php echo htmlspecialchars($site); ?></span>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title"><i class="fas fa-file-import"></i> 匯入CSV數據</h1>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回人力需求派工
            </a>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-file-import"></i> 匯入CSV數據</h2>
            </div>
            <div class="card-body">
                <div class="import-instructions">
                    <h3>匯入說明</h3>
                    <ol>
                        <li>CSV文件應包含日期、人數、工作內容和各公司的工人數量</li>
                        <li>系統會自動跳過前三行（標題行）</li>
                        <li>系統會自動跳過沒有工人的日期</li>
                        <li>如果某一天已經有人力需求記錄，該天將被跳過</li>
                        <li>匯入後，所有記錄將直接設為已批准狀態</li>
                    </ol>
                </div>

                <form action="" method="post" enctype="multipart/form-data" class="import-form">
                    <div class="form-group">
                        <label for="site_id">選擇工地 <span class="required" style="color: #ea4335;">*</span></label>
                        <select id="site_id" name="site_id" class="form-control" required>
                            <option value="">-- 請選擇工地 --</option>
                            <?php foreach ($sites as $site): ?>
                            <option value="<?php echo $site['id']; ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="month">選擇月份 <span class="required" style="color: #ea4335;">*</span></label>
                        <input type="month" id="month" name="month" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="csv_file">選擇CSV文件 <span class="required" style="color: #ea4335;">*</span></label>
                        <input type="file" id="csv_file" name="csv_file" class="form-control" accept=".csv" required>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-import"></i> 匯入數據
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 設置當前日期為默認值
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            $('#month').val(`${year}-${month}`);

            // 主題切換器
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    const currentTheme = document.body.classList.contains('theme-light') ? 'dark' : 'light';
                    document.body.classList.toggle('theme-light');
                    localStorage.setItem('theme', currentTheme);

                    // 更新圖標
                    if (currentTheme === 'light') {
                        this.innerHTML = '<i class="fas fa-moon"></i>';
                    } else {
                        this.innerHTML = '<i class="fas fa-sun"></i>';
                    }
                });

                // 初始化主題
                const savedTheme = localStorage.getItem('theme') || 'dark';
                if (savedTheme === 'light') {
                    document.body.classList.add('theme-light');
                    themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
                } else {
                    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
                }
            }
        });
    </script>
</body>
</html>
