<?php
/**
 * 測試日期提取功能
 */

function extractFileDate($file) {
    $path = $file['path'];

    echo "測試路徑: $path\n";

    // 檢查路徑中是否包含年月資訊 (例如: 114年度, 11405)
    if (preg_match('/11[4-9](\d{2})/', $path, $matches)) {
        echo "匹配到民國年月: {$matches[0]}\n";
        $year_month = $matches[0];
        $roc_year = intval(substr($year_month, 0, 3)); // 114
        $year = $roc_year + 1911; // 114 + 1911 = 2025年
        $month = str_pad(substr($year_month, 3, 2), 2, '0', STR_PAD_LEFT);
        $result = "$year-$month-01";
        echo "民國年: $roc_year, 西元年: $year, 月份: $month\n";
        echo "轉換結果: $result\n";
        return $result;
    }

    // 檢查路徑中是否包含西元年月 (例如: 2024/05, 202405)
    if (preg_match('/20(\d{2})[\/\-]?(\d{1,2})/', $path, $matches)) {
        echo "匹配到西元年月: {$matches[0]}\n";
        $year = '20' . $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $result = "$year-$month-01";
        echo "轉換結果: $result\n";
        return $result;
    }

    // 檢查檔案名稱中的日期格式
    $filename = $file['name'];
    echo "檢查檔案名稱: $filename\n";
    if (preg_match('/(\d{4})[\/\-]?(\d{1,2})[\/\-]?(\d{1,2})/', $filename, $matches)) {
        echo "匹配到檔案名稱日期: {$matches[0]}\n";
        $year = $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
        $result = "$year-$month-$day";
        echo "轉換結果: $result\n";
        return $result;
    }

    // 如果無法提取日期，使用當前日期
    $result = date('Y-m-d');
    echo "無法提取日期，使用當前日期: $result\n";
    return $result;
}

// 測試鳳林台電的檔案
$file_info = [
    'name' => '漢科字第1140002438號.pdf',
    'path' => '//192.168.55.251/公文/14鳳林台電/收文/11405/漢科字第1140002438號.pdf',
    'size' => 1233268,
    'extension' => 'pdf'
];

echo "=== 測試日期提取 ===\n";
$extracted_date = extractFileDate($file_info);
echo "最終提取的日期: $extracted_date\n";

$cutoff_date = '2025-05-01';
echo "截止日期: $cutoff_date\n";

if ($extracted_date >= $cutoff_date) {
    echo "✅ 符合日期條件！\n";
} else {
    echo "❌ 不符合日期條件\n";
}

echo "\n=== 測試完成 ===\n";
?>
