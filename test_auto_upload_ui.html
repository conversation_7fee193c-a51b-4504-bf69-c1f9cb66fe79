<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自動上傳系統測試 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid #28a745;
            color: #155724;
        }
        
        .test-error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            color: #721c24;
        }
        
        .feature-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .demo-item {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
        }
        
        .demo-item h4 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
        }
        
        .scan-btn-demo {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .scan-btn-demo:hover {
            background-color: #138496;
            transform: translateY(-1px);
        }
        
        .status-badge-demo {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 5px;
        }
        
        .status-active-demo {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive-demo {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        [data-theme="dark"] .status-active-demo {
            background-color: rgba(40, 167, 69, 0.2);
            color: #4caf50;
        }
        
        [data-theme="dark"] .status-inactive-demo {
            background-color: rgba(220, 53, 69, 0.2);
            color: #f44336;
        }
        
        .config-path-demo {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: var(--text-muted);
            background: var(--light-bg);
            padding: 2px 6px;
            border-radius: 3px;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        [data-theme="dark"] .config-path-demo {
            background: var(--card-bg);
            color: var(--text-muted);
        }
    </style>
</head>
<body>
<header>
    <div class="logo-area">
        <a href="dashboard.php" class="logo-link">
            <img src="images/logo.png" alt="良有營造標誌">
            <span>良有營造電子簽核系統</span>
        </a>
    </div>

    <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <nav class="main-nav" id="mainNav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-tachometer-alt"></i> 簽核總覽
        </a>
        <a href="document_manager.php" class="nav-item">
            <i class="fas fa-file-alt"></i> 公文整理系統
        </a>
        <a href="auto_upload_manager.php" class="nav-item active">
            <i class="fas fa-robot"></i> 公文自動上傳管理
        </a>
    </nav>

    <div class="user-actions" id="userActions">
        <button onclick="toggleTheme()" class="theme-toggle" title="切換主題">
            <i id="theme-icon" class="fas fa-sun"></i>
            <span id="theme-text">切換深色模式</span>
        </button>
        <a href="logout.php" class="nav-item logout">
            <i class="fas fa-sign-out-alt"></i> <span class="logout-text">登出</span>
        </a>

        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <div class="user-info-details">
                <span class="user-name">測試管理員</span>
                <span class="user-position">
                    <i class="fas fa-user-tag" style="margin-right: 4px; font-size: 12px;"></i>
                    系統管理員
                </span>
                <span class="user-site">總公司</span>
            </div>
        </div>

        <div class="notification-icon">
            <i class="fas fa-bell"></i>
            <span class="notification-text">通知</span>
        </div>
    </div>
</header>

<div class="main-content">
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-robot"></i> 自動上傳系統 UI 測試
        </h1>
        <div>
            <a href="auto_upload_manager.php" class="btn btn-primary">
                <i class="fas fa-cog"></i> 進入實際系統
            </a>
        </div>
    </div>

    <div class="test-card">
        <h2><i class="fas fa-check-circle"></i> 功能特色展示</h2>
        
        <div class="test-result test-success">
            <i class="fas fa-check"></i> 
            <strong>暗黑模式支援</strong> - 點擊右上角的主題切換按鈕測試
        </div>
        
        <div class="test-result test-success">
            <i class="fas fa-check"></i> 
            <strong>手動掃描功能</strong> - 每個啟用的設定都有掃描按鈕
        </div>
        
        <div class="test-result test-success">
            <i class="fas fa-check"></i> 
            <strong>統一介面設計</strong> - 與原系統完全一致的導覽列和樣式
        </div>
        
        <div class="test-result test-success">
            <i class="fas fa-check"></i> 
            <strong>智能檔案過濾</strong> - 自動跳過4月份之前的檔案，避免重複上傳
        </div>
    </div>

    <div class="test-card">
        <h2><i class="fas fa-palette"></i> UI 元件展示</h2>
        
        <div class="feature-demo">
            <div class="demo-item">
                <h4>手動掃描按鈕</h4>
                <button class="scan-btn-demo">
                    <i class="fas fa-search"></i> 掃描
                </button>
                <p style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                    點擊後會立即掃描對應的SMB路徑並上傳新檔案
                </p>
            </div>
            
            <div class="demo-item">
                <h4>狀態標籤</h4>
                <span class="status-badge-demo status-active-demo">啟用</span>
                <span class="status-badge-demo status-inactive-demo">停用</span>
                <p style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                    清楚顯示每個設定的啟用狀態
                </p>
            </div>
            
            <div class="demo-item">
                <h4>SMB路徑顯示</h4>
                <div class="config-path-demo">\\192.168.55.251\公文\04中壢前寮段\收文</div>
                <p style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                    使用等寬字體顯示路徑，支援長路徑省略
                </p>
            </div>
            
            <div class="demo-item">
                <h4>操作按鈕組</h4>
                <button class="btn btn-view btn-sm"><i class="fas fa-edit"></i></button>
                <button class="btn btn-success btn-sm"><i class="fas fa-play"></i></button>
                <button class="btn btn-delete btn-sm"><i class="fas fa-trash"></i></button>
                <p style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                    編輯、啟用/停用、刪除功能
                </p>
            </div>
        </div>
    </div>

    <div class="test-card">
        <h2><i class="fas fa-info-circle"></i> 系統說明</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <div>
                <h4><i class="fas fa-shield-alt"></i> 智能過濾</h4>
                <p>系統會自動跳過2024年4月份之前的檔案，確保不會重複上傳已處理的公文。</p>
            </div>
            
            <div>
                <h4><i class="fas fa-clock"></i> 即時掃描</h4>
                <p>除了定時自動掃描外，您可以隨時點擊「掃描」按鈕進行即時上傳。</p>
            </div>
            
            <div>
                <h4><i class="fas fa-moon"></i> 暗黑模式</h4>
                <p>完整支援暗黑模式，所有UI元件都會自動適應主題變化。</p>
            </div>
            
            <div>
                <h4><i class="fas fa-mobile-alt"></i> 響應式設計</h4>
                <p>支援各種螢幕尺寸，在手機和平板上也能正常使用。</p>
            </div>
        </div>
    </div>

    <div class="test-card">
        <h2><i class="fas fa-rocket"></i> 快速開始</h2>
        
        <ol style="line-height: 1.8;">
            <li>點擊右上角的「進入實際系統」按鈕</li>
            <li>檢查預設的自動上傳設定</li>
            <li>測試SMB連線是否正常</li>
            <li>點擊「掃描」按鈕進行手動測試</li>
            <li>設定cron job啟用自動排程</li>
        </ol>
        
        <div style="margin-top: 20px;">
            <a href="auto_upload_manager.php" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i> 開始使用自動上傳系統
            </a>
            <a href="auto_upload_status.php" class="btn btn-secondary">
                <i class="fas fa-chart-line"></i> 查看系統狀態
            </a>
        </div>
    </div>
</div>

<script>
    // 主題切換功能
    function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        const themeIcon = document.getElementById('theme-icon');
        const themeText = document.getElementById('theme-text');
        
        if (newTheme === 'dark') {
            themeIcon.className = 'fas fa-moon';
            themeText.textContent = '切換淺色模式';
        } else {
            themeIcon.className = 'fas fa-sun';
            themeText.textContent = '切換深色模式';
        }
    }

    // 頁面載入時初始化
    document.addEventListener('DOMContentLoaded', function() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        const themeIcon = document.getElementById('theme-icon');
        const themeText = document.getElementById('theme-text');
        
        if (savedTheme === 'dark') {
            themeIcon.className = 'fas fa-moon';
            themeText.textContent = '切換淺色模式';
        } else {
            themeIcon.className = 'fas fa-sun';
            themeText.textContent = '切換深色模式';
        }

        // 移動選單切換
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mainNav = document.getElementById('mainNav');
        
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function() {
                mainNav.classList.toggle('show');
            });
        }
    });
</script>
</body>
</html>
