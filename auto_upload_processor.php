<?php
/**
 * 自動上傳處理器
 * 定期掃描SMB路徑並自動上傳新檔案
 */

require_once 'db.php';
require_once 'smb_scanner.php';

class AutoUploadProcessor {
    private $pdo;
    private $scanner;
    private $upload_dir = 'uploads/auto_upload/';
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->scanner = new SMBScanner($pdo);
        
        // 確保上傳目錄存在
        if (!is_dir($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
    }
    
    /**
     * 處理所有啟用的自動上傳設定
     */
    public function processAllConfigs() {
        $results = [];
        
        try {
            // 獲取所有啟用的設定
            $stmt = $this->pdo->query("
                SELECT * FROM auto_upload_configs 
                WHERE is_active = 1 
                ORDER BY upload_time
            ");
            $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($configs as $config) {
                $result = $this->processConfig($config);
                $results[] = [
                    'config_id' => $config['id'],
                    'config_name' => $config['name'],
                    'result' => $result
                ];
                
                // 更新最後掃描時間
                $this->updateLastScanTime($config['id']);
            }
            
        } catch (Exception $e) {
            error_log("自動上傳處理失敗: " . $e->getMessage());
            $results[] = [
                'error' => '處理失敗：' . $e->getMessage()
            ];
        }
        
        return $results;
    }
    
    /**
     * 處理單一設定
     */
    public function processConfig($config) {
        $result = [
            'scanned_files' => 0,
            'new_files' => 0,
            'uploaded_files' => 0,
            'failed_files' => 0,
            'errors' => []
        ];
        
        try {
            // 掃描檔案
            $scan_result = $this->scanner->scanFiles($config);
            
            if (!$scan_result['success']) {
                $result['errors'][] = $scan_result['message'];
                return $result;
            }
            
            $result['scanned_files'] = $scan_result['total_found'];
            $result['new_files'] = $scan_result['new_files'];
            
            // 處理每個新檔案
            foreach ($scan_result['files'] as $file) {
                $upload_result = $this->uploadFile($file, $config);
                
                if ($upload_result['success']) {
                    $result['uploaded_files']++;
                } else {
                    $result['failed_files']++;
                    $result['errors'][] = $file['name'] . ': ' . $upload_result['message'];
                }
                
                // 記錄上傳日誌
                $this->logUpload($config['id'], $file, $upload_result);
            }
            
        } catch (Exception $e) {
            $result['errors'][] = '處理設定失敗：' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * 上傳單一檔案
     */
    private function uploadFile($file, $config) {
        try {
            // 生成本地檔案路徑
            $local_filename = uniqid('auto_') . '_' . $file['name'];
            $local_path = $this->upload_dir . $local_filename;
            
            // 下載檔案到本地
            $download_result = $this->scanner->downloadFile($file['path'], $local_path);
            
            if (!$download_result['success']) {
                return $download_result;
            }
            
            // 模擬檔案上傳陣列
            $uploaded_file = [
                'name' => $file['name'],
                'tmp_name' => $local_path,
                'size' => $file['size'],
                'type' => $this->getMimeType($file['extension']),
                'error' => 0
            ];
            
            // 使用現有的文件處理函數
            $upload_result = $this->processDocumentUpload(
                $uploaded_file,
                $config['site_id'],
                $config['category_id']
            );
            
            if ($upload_result['success']) {
                // 記錄檔案雜湊值
                $this->recordFileHash($file, $upload_result['document_id']);
            }
            
            // 清理臨時檔案
            if (file_exists($local_path)) {
                unlink($local_path);
            }
            
            return $upload_result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '上傳失敗：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 處理文件上傳（整合現有邏輯）
     */
    private function processDocumentUpload($file, $site_id, $category_id) {
        try {
            // 生成唯一文件名
            $fileName = uniqid('doc_') . '.pdf';
            $uploadDir = 'uploads/';
            $filePath = $uploadDir . $fileName;
            
            // 移動文件到目標目錄
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                return [
                    'success' => false,
                    'message' => '檔案移動失敗'
                ];
            }
            
            // 初始化文件信息（這裡可以整合OCR分析）
            $docInfo = [
                'document_number' => null,
                'title' => basename($file['name'], '.' . pathinfo($file['name'], PATHINFO_EXTENSION)),
                'sender' => '自動上傳',
                'receiver' => '良有營造股份有限公司',
                'issue_date' => date('Y-m-d'),
                'content_summary' => '自動上傳的公文',
                'notes' => '由自動上傳系統處理'
            ];
            
            // 插入資料庫
            $this->pdo->beginTransaction();
            
            $sql = "
                INSERT INTO official_documents (
                    file_name, file_path, document_number,
                    title, sender, receiver,
                    issue_date, content_summary, notes,
                    site_id, category_id,
                    uploaded_by, uploaded_at, is_processed,
                    upload_status
                ) VALUES (
                    ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?,
                    ?, ?,
                    1, NOW(), 0,
                    'auto_uploaded'
                )
            ";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $file['name'],
                $filePath,
                $docInfo['document_number'],
                $docInfo['title'],
                $docInfo['sender'],
                $docInfo['receiver'],
                $docInfo['issue_date'],
                $docInfo['content_summary'],
                $docInfo['notes'],
                $site_id,
                $category_id
            ]);
            
            $document_id = $this->pdo->lastInsertId();
            $this->pdo->commit();
            
            return [
                'success' => true,
                'document_id' => $document_id,
                'message' => '檔案上傳成功'
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            return [
                'success' => false,
                'message' => '資料庫錯誤：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 記錄檔案雜湊值
     */
    private function recordFileHash($file, $document_id) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO uploaded_file_hashes (file_hash, file_name, file_path, document_id)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $file['hash'],
                $file['name'],
                $file['path'],
                $document_id
            ]);
        } catch (Exception $e) {
            error_log("記錄檔案雜湊值失敗: " . $e->getMessage());
        }
    }
    
    /**
     * 記錄上傳日誌
     */
    private function logUpload($config_id, $file, $result) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO auto_upload_logs (
                    config_id, file_path, file_name, file_size,
                    status, error_message, document_id,
                    scan_time, upload_time, file_hash
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?)
            ");
            
            $stmt->execute([
                $config_id,
                $file['path'],
                $file['name'],
                $file['size'],
                $result['success'] ? 'success' : 'failed',
                $result['success'] ? null : $result['message'],
                $result['success'] ? $result['document_id'] : null,
                $result['success'] ? date('Y-m-d H:i:s') : null,
                $file['hash']
            ]);
        } catch (Exception $e) {
            error_log("記錄上傳日誌失敗: " . $e->getMessage());
        }
    }
    
    /**
     * 更新最後掃描時間
     */
    private function updateLastScanTime($config_id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE auto_upload_configs 
                SET last_scan_time = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$config_id]);
        } catch (Exception $e) {
            error_log("更新掃描時間失敗: " . $e->getMessage());
        }
    }
    
    /**
     * 根據副檔名獲取MIME類型
     */
    private function getMimeType($extension) {
        $mime_types = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png'
        ];
        
        return $mime_types[strtolower($extension)] ?? 'application/octet-stream';
    }
}

// 如果直接執行此腳本，則處理所有設定
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $processor = new AutoUploadProcessor($pdo);
    $results = $processor->processAllConfigs();
    
    echo "自動上傳處理完成:\n";
    foreach ($results as $result) {
        if (isset($result['error'])) {
            echo "錯誤: " . $result['error'] . "\n";
        } else {
            echo "設定 {$result['config_name']}: ";
            echo "掃描 {$result['result']['scanned_files']} 個檔案, ";
            echo "發現 {$result['result']['new_files']} 個新檔案, ";
            echo "成功上傳 {$result['result']['uploaded_files']} 個檔案, ";
            echo "失敗 {$result['result']['failed_files']} 個檔案\n";
            
            if (!empty($result['result']['errors'])) {
                echo "錯誤訊息:\n";
                foreach ($result['result']['errors'] as $error) {
                    echo "  - $error\n";
                }
            }
        }
    }
}
?>
