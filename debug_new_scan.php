<?php
/**
 * 調試新的掃描邏輯
 */

// 測試路徑轉換
function convertToSmbPath($windows_path) {
    $path = str_replace('\\', '/', $windows_path);
    if (substr($path, 0, 2) !== '//') {
        $path = '//' . ltrim($path, '/');
    }
    
    // 分解路徑
    $parts = explode('/', ltrim($path, '/'));
    if (count($parts) >= 2) {
        $server = $parts[0]; // 192.168.55.251
        $share = $parts[1];  // 公文
        $subpath = implode('/', array_slice($parts, 2)); // 14鳳林台電/收文
        
        return [
            'base_path' => "//$server/$share",
            'sub_path' => $subpath
        ];
    }
    
    return [
        'base_path' => $path,
        'sub_path' => ''
    ];
}

$windows_path = '\\192.168.55.251\公文\14鳳林台電\收文';
echo "=== 測試路徑轉換 ===\n";
echo "原始路徑: $windows_path\n";

$path_info = convertToSmbPath($windows_path);
echo "基礎路徑: {$path_info['base_path']}\n";
echo "子路徑: {$path_info['sub_path']}\n\n";

// 測試SMB連線
$smb_username = 'paper';
$smb_password = 'liang55778010';

echo "=== 測試SMB連線 ===\n";

// 測試基礎路徑連線
echo "1. 測試基礎路徑連線:\n";
$command = sprintf(
    'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
    $path_info['base_path'],
    $smb_username,
    $smb_password
);
echo "命令: $command\n";
$output = shell_exec($command);
echo "輸出:\n$output\n";

// 測試子路徑掃描
echo "2. 測試子路徑掃描:\n";
$ls_path = $path_info['sub_path'];
$command = sprintf(
    'smbclient "%s" -U "%s%%%s" -c "ls %s" 2>/dev/null',
    $path_info['base_path'],
    $smb_username,
    $smb_password,
    $ls_path
);
echo "命令: $command\n";
$output = shell_exec($command);
echo "輸出:\n$output\n";

// 解析輸出
if ($output) {
    echo "3. 解析輸出:\n";
    $lines = explode("\n", $output);
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '.') === 0) continue;
        
        echo "  原始行: '$line'\n";
        
        if (preg_match('/^\s*(.+?)\s+([AD])\s+(\d+)\s+(.+)$/', $line, $matches)) {
            $name = trim($matches[1]);
            $type = $matches[2];
            $size = $matches[3];
            
            if ($type === 'D') {
                echo "  -> 目錄: '$name'\n";
                
                // 測試子目錄
                $subdir_path = $path_info['sub_path'] ? $path_info['sub_path'] . '/' . $name : $name;
                echo "  測試子目錄: $subdir_path\n";
                
                $sub_command = sprintf(
                    'smbclient "%s" -U "%s%%%s" -c "ls %s" 2>/dev/null',
                    $path_info['base_path'],
                    $smb_username,
                    $smb_password,
                    $subdir_path
                );
                
                $sub_output = shell_exec($sub_command);
                echo "  子目錄內容:\n";
                if ($sub_output) {
                    $sub_lines = explode("\n", $sub_output);
                    foreach ($sub_lines as $sub_line) {
                        $sub_line = trim($sub_line);
                        if (empty($sub_line) || strpos($sub_line, '.') === 0) continue;
                        echo "    $sub_line\n";
                        
                        if (preg_match('/^\s*(.+?)\s+A\s+(\d+)\s+(.+)$/', $sub_line, $file_matches)) {
                            $filename = trim($file_matches[1]);
                            $file_size = $file_matches[2];
                            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                            
                            if ($extension === 'pdf') {
                                echo "    -> PDF檔案: '$filename' (大小: $file_size bytes)\n";
                                
                                $full_path = $path_info['base_path'] . '/' . $subdir_path . '/' . $filename;
                                echo "    -> 完整路徑: $full_path\n";
                                
                                // 測試日期提取
                                $file_info = [
                                    'name' => $filename,
                                    'path' => $full_path,
                                    'size' => $file_size,
                                    'extension' => $extension
                                ];
                                
                                $file_date = extractFileDate($file_info);
                                echo "    -> 提取的日期: $file_date\n";
                                
                                $cutoff_date = '2025-05-01';
                                if ($file_date >= $cutoff_date) {
                                    echo "    -> ✅ 符合日期條件！\n";
                                } else {
                                    echo "    -> ❌ 不符合日期條件 (需要 >= $cutoff_date)\n";
                                }
                            }
                        }
                    }
                } else {
                    echo "    (空目錄或無法存取)\n";
                }
                echo "\n";
            } else if ($type === 'A') {
                echo "  -> 檔案: '$name' (大小: $size bytes)\n";
                $extension = strtolower(pathinfo($name, PATHINFO_EXTENSION));
                if ($extension === 'pdf') {
                    echo "  -> 這是PDF檔案！\n";
                }
            }
        }
    }
}

// 日期提取函數
function extractFileDate($file) {
    $path = $file['path'];

    // 檢查路徑中是否包含年月資訊 (例如: 114年度, 11405)
    if (preg_match('/11[4-9](\d{2})/', $path, $matches)) {
        $year_month = $matches[0];
        $year = 2000 + intval(substr($year_month, 0, 3)) - 11; // 114年 = 2025年
        $month = str_pad(substr($year_month, 3, 2), 2, '0', STR_PAD_LEFT);
        return "$year-$month-01";
    }

    // 檢查路徑中是否包含西元年月 (例如: 2024/05, 202405)
    if (preg_match('/20(\d{2})[\/\-]?(\d{1,2})/', $path, $matches)) {
        $year = '20' . $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        return "$year-$month-01";
    }

    // 檢查檔案名稱中的日期格式
    $filename = $file['name'];
    if (preg_match('/(\d{4})[\/\-]?(\d{1,2})[\/\-]?(\d{1,2})/', $filename, $matches)) {
        $year = $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
        return "$year-$month-$day";
    }

    // 如果無法提取日期，使用當前日期
    return date('Y-m-d');
}

echo "\n=== 調試完成 ===\n";
?>
