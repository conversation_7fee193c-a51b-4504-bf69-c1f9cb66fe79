<?php
/**
 * SMB檔案掃描器
 * 用於掃描NAS上的SMB共享資料夾並找出新檔案
 */

class SMBScanner {
    private $smb_username = 'paper';
    private $smb_password = 'liang55778010';
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * 測試SMB連線
     */
    public function testConnection($smb_path) {
        try {
            // 將Windows路徑轉換為Linux smbclient格式
            $server_path = $this->convertToSmbPath($smb_path);
            
            // 使用smbclient測試連線
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "ls" 2>&1',
                $server_path,
                $this->smb_username,
                $this->smb_password
            );
            
            $output = shell_exec($command);
            
            if (strpos($output, 'NT_STATUS_') !== false) {
                return [
                    'success' => false,
                    'message' => '連線失敗：' . $output,
                    'path' => $smb_path
                ];
            }
            
            return [
                'success' => true,
                'message' => '連線測試成功',
                'path' => $smb_path
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '連線測試失敗：' . $e->getMessage(),
                'path' => $smb_path
            ];
        }
    }
    
    /**
     * 掃描SMB路徑中的檔案
     */
    public function scanFiles($config) {
        $files = [];
        $smb_path = $config['smb_path'];
        $allowed_extensions = explode(',', strtolower($config['file_extensions']));

        try {
            // 將Windows路徑轉換為Linux smbclient格式
            $path_info = $this->convertToSmbPath($smb_path);

            // 掃描檔案
            if ($config['scan_subdirs']) {
                $files = $this->scanDirectoryRecursive($path_info, $allowed_extensions);
            } else {
                $files = $this->scanDirectory($path_info, $allowed_extensions);
            }

            // 過濾已上傳的檔案
            $new_files = $this->filterNewFiles($files, $config['id']);

            return [
                'success' => true,
                'files' => $new_files,
                'total_found' => count($files),
                'new_files' => count($new_files)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '掃描失敗：' . $e->getMessage(),
                'files' => []
            ];
        }
    }
    
    /**
     * 將Windows SMB路徑轉換為smbclient格式
     */
    private function convertToSmbPath($windows_path) {
        // \\192.168.55.251\公文\04中壢前寮段\收文
        // 分解為: 基礎路徑 //192.168.55.251/公文 和 子路徑 04中壢前寮段/收文
        $path = str_replace('\\', '/', $windows_path);
        if (substr($path, 0, 2) !== '//') {
            $path = '//' . ltrim($path, '/');
        }

        // 分解路徑
        $parts = explode('/', ltrim($path, '/'));
        if (count($parts) >= 2) {
            $server = $parts[0]; // 192.168.55.251
            $share = $parts[1];  // 公文
            $subpath = implode('/', array_slice($parts, 2)); // 04中壢前寮段/收文

            return [
                'base_path' => "//$server/$share",
                'sub_path' => $subpath
            ];
        }

        return [
            'base_path' => $path,
            'sub_path' => ''
        ];
    }
    
    /**
     * 掃描單一目錄
     */
    private function scanDirectory($path_info, $allowed_extensions) {
        $files = [];

        if (empty($path_info['sub_path'])) {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password
            );
        } else {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "cd %s; ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password,
                $path_info['sub_path']
            );
        }

        $output = shell_exec($command);

        if ($output) {
            $lines = explode("\n", $output);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '.') === 0) continue;

                // 解析smbclient輸出格式
                if (preg_match('/^\s*(.+?)\s+([AD])\s+(\d+)\s+(.+)$/', $line, $matches)) {
                    $filename = trim($matches[1]);
                    $type = $matches[2]; // A=Archive(file), D=Directory
                    $size = $matches[3];

                    if ($type === 'A') { // 只處理檔案
                        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                        if (in_array($extension, $allowed_extensions)) {
                            $full_path = $path_info['base_path'] . '/' .
                                        ($path_info['sub_path'] ? $path_info['sub_path'] . '/' : '') .
                                        $filename;
                            $files[] = [
                                'name' => $filename,
                                'path' => $full_path,
                                'size' => $size,
                                'extension' => $extension
                            ];
                        }
                    }
                }
            }
        }

        return $files;
    }
    
    /**
     * 遞歸掃描目錄
     */
    private function scanDirectoryRecursive($path_info, $allowed_extensions, $depth = 0, $max_depth = 5) {
        $files = [];

        if ($depth > $max_depth) {
            return $files;
        }

        // 先掃描當前目錄的檔案
        $files = array_merge($files, $this->scanDirectory($path_info, $allowed_extensions));

        // 然後掃描子目錄
        if (empty($path_info['sub_path'])) {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password
            );
        } else {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "cd %s; ls" 2>/dev/null',
                $path_info['base_path'],
                $this->smb_username,
                $this->smb_password,
                $path_info['sub_path']
            );
        }

        $output = shell_exec($command);

        if ($output) {
            $lines = explode("\n", $output);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '.') === 0) continue;

                if (preg_match('/^\s*(.+?)\s+D\s+\d+\s+(.+)$/', $line, $matches)) {
                    $dirname = trim($matches[1]);
                    if ($dirname !== '.' && $dirname !== '..') {
                        // 建立子目錄的路徑資訊
                        $subdir_path_info = [
                            'base_path' => $path_info['base_path'],
                            'sub_path' => $path_info['sub_path'] ? $path_info['sub_path'] . '/' . $dirname : $dirname
                        ];
                        $subdir_files = $this->scanDirectoryRecursive($subdir_path_info, $allowed_extensions, $depth + 1, $max_depth);
                        $files = array_merge($files, $subdir_files);
                    }
                }
            }
        }

        return $files;
    }
    
    /**
     * 過濾已上傳的檔案
     */
    private function filterNewFiles($files, $config_id) {
        $new_files = [];
        $cutoff_date = '2025-05-01'; // 只處理2025年5月之後的檔案

        foreach ($files as $file) {
            $file_hash = $this->calculateFileHash($file);

            // 檢查檔案是否已經上傳過
            $stmt = $this->pdo->prepare("
                SELECT id FROM uploaded_file_hashes
                WHERE file_hash = ? OR (file_name = ? AND file_path = ?)
            ");
            $stmt->execute([$file_hash, $file['name'], $file['path']]);

            if (!$stmt->fetch()) {
                // 檢查檔案修改時間，處理2020年之後的檔案
                $file_date = $this->extractFileDate($file);
                if ($file_date >= $cutoff_date) {
                    $file['hash'] = $file_hash;
                    $new_files[] = $file;
                }
            }
        }

        return $new_files;
    }

    /**
     * 從檔案路徑或名稱中提取日期
     */
    private function extractFileDate($file) {
        // 嘗試從檔案路徑中提取日期
        $path = $file['path'];

        // 檢查路徑中是否包含年月資訊 (例如: 114年度, 11405)
        if (preg_match('/11[4-9](\d{2})/', $path, $matches)) {
            $year_month = $matches[0];
            $roc_year = intval(substr($year_month, 0, 3)); // 114
            $year = $roc_year + 1911; // 114 + 1911 = 2025年
            $month = str_pad(substr($year_month, 3, 2), 2, '0', STR_PAD_LEFT);
            return "$year-$month-01";
        }

        // 檢查路徑中是否包含西元年月 (例如: 2024/05, 202405)
        if (preg_match('/20(\d{2})[\/\-]?(\d{1,2})/', $path, $matches)) {
            $year = '20' . $matches[1];
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            return "$year-$month-01";
        }

        // 檢查檔案名稱中的日期格式
        $filename = $file['name'];
        if (preg_match('/(\d{4})[\/\-]?(\d{1,2})[\/\-]?(\d{1,2})/', $filename, $matches)) {
            $year = $matches[1];
            $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
            $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
            return "$year-$month-$day";
        }

        // 如果無法提取日期，使用當前日期（這樣會被包含在處理範圍內）
        return date('Y-m-d');
    }
    
    /**
     * 計算檔案雜湊值
     */
    private function calculateFileHash($file) {
        // 使用檔案路徑、名稱和大小來計算雜湊值
        $data = $file['path'] . '|' . $file['name'] . '|' . $file['size'];
        return hash('sha256', $data);
    }
    
    /**
     * 下載SMB檔案到本地
     */
    public function downloadFile($smb_file_path, $local_path) {
        try {
            $command = sprintf(
                'smbclient "%s" -U "%s%%%s" -c "get \"%s\" \"%s\"" 2>&1',
                dirname($smb_file_path),
                $this->smb_username,
                $this->smb_password,
                basename($smb_file_path),
                $local_path
            );
            
            $output = shell_exec($command);
            
            if (file_exists($local_path) && filesize($local_path) > 0) {
                return [
                    'success' => true,
                    'local_path' => $local_path,
                    'message' => '檔案下載成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '檔案下載失敗：' . $output
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '下載失敗：' . $e->getMessage()
            ];
        }
    }
}
?>
