<?php
/**
 * 調試掃描功能
 */

// 手動測試SMB掃描
$smb_username = 'paper';
$smb_password = 'liang55778010';
$server_path = '//192.168.55.251/公文/14鳳林台電/收文';

echo "=== 調試SMB掃描 ===\n";
echo "測試路徑: $server_path\n\n";

// 測試頂層目錄
echo "1. 掃描頂層目錄:\n";
$command = sprintf(
    'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
    $server_path,
    $smb_username,
    $smb_password
);

echo "執行命令: $command\n";
$output = shell_exec($command);
echo "輸出:\n$output\n";

// 解析輸出
if ($output) {
    $lines = explode("\n", $output);
    echo "解析結果:\n";
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '.') === 0) continue;
        
        echo "  原始行: '$line'\n";
        
        // 檢查是否為目錄
        if (preg_match('/^\s*(.+?)\s+D\s+\d+\s+(.+)$/', $line, $matches)) {
            $dirname = trim($matches[1]);
            echo "  -> 找到目錄: '$dirname'\n";
            
            if ($dirname !== '.' && $dirname !== '..') {
                // 測試子目錄
                $subdir_path = $server_path . '/' . $dirname;
                echo "  測試子目錄: $subdir_path\n";
                
                $sub_command = sprintf(
                    'smbclient "%s" -U "%s%%%s" -c "ls" 2>/dev/null',
                    $subdir_path,
                    $smb_username,
                    $smb_password
                );
                
                $sub_output = shell_exec($sub_command);
                echo "  子目錄內容:\n";
                if ($sub_output) {
                    $sub_lines = explode("\n", $sub_output);
                    foreach ($sub_lines as $sub_line) {
                        $sub_line = trim($sub_line);
                        if (empty($sub_line) || strpos($sub_line, '.') === 0) continue;
                        echo "    $sub_line\n";
                        
                        // 檢查是否為PDF檔案
                        if (preg_match('/^\s*(.+?)\s+A\s+(\d+)\s+(.+)$/', $sub_line, $file_matches)) {
                            $filename = trim($file_matches[1]);
                            $size = $file_matches[2];
                            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                            
                            if ($extension === 'pdf') {
                                echo "    -> 找到PDF檔案: '$filename' (大小: $size bytes)\n";
                                
                                // 測試日期提取
                                $file_info = [
                                    'name' => $filename,
                                    'path' => $subdir_path . '/' . $filename,
                                    'size' => $size,
                                    'extension' => $extension
                                ];
                                
                                $file_date = extractFileDate($file_info);
                                echo "    -> 提取的日期: $file_date\n";
                                
                                $cutoff_date = '2025-05-01';
                                if ($file_date >= $cutoff_date) {
                                    echo "    -> 符合日期條件！\n";
                                } else {
                                    echo "    -> 不符合日期條件 (需要 >= $cutoff_date)\n";
                                }
                            }
                        }
                    }
                } else {
                    echo "    (空目錄或無法存取)\n";
                }
                echo "\n";
            }
        }
        // 檢查是否為檔案
        else if (preg_match('/^\s*(.+?)\s+A\s+(\d+)\s+(.+)$/', $line, $matches)) {
            $filename = trim($matches[1]);
            $size = $matches[2];
            echo "  -> 找到檔案: '$filename' (大小: $size bytes)\n";
        }
    }
}

// 日期提取函數（複製自SMBScanner）
function extractFileDate($file) {
    $path = $file['path'];

    // 檢查路徑中是否包含年月資訊 (例如: 114年度, 11405)
    if (preg_match('/11[4-9](\d{2})/', $path, $matches)) {
        $year_month = $matches[0];
        $year = 2000 + intval(substr($year_month, 0, 3)) - 11; // 114年 = 2025年
        $month = str_pad(substr($year_month, 3, 2), 2, '0', STR_PAD_LEFT);
        return "$year-$month-01";
    }

    // 檢查路徑中是否包含西元年月 (例如: 2024/05, 202405)
    if (preg_match('/20(\d{2})[\/\-]?(\d{1,2})/', $path, $matches)) {
        $year = '20' . $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        return "$year-$month-01";
    }

    // 檢查檔案名稱中的日期格式
    $filename = $file['name'];
    if (preg_match('/(\d{4})[\/\-]?(\d{1,2})[\/\-]?(\d{1,2})/', $filename, $matches)) {
        $year = $matches[1];
        $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
        $day = str_pad($matches[3], 2, '0', STR_PAD_LEFT);
        return "$year-$month-$day";
    }

    // 如果無法提取日期，使用當前日期
    return date('Y-m-d');
}

echo "\n=== 調試完成 ===\n";
?>
