#!/bin/bash

# 自動上傳系統 Cron Job 設定腳本
# 此腳本用於設定定期執行自動上傳任務

echo "=== 良有營造公文自動上傳系統 Cron Job 設定 ==="
echo ""

# 獲取當前腳本所在目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PHP_SCRIPT="$SCRIPT_DIR/auto_upload_cron.php"
LOG_DIR="$SCRIPT_DIR/logs"

echo "腳本目錄: $SCRIPT_DIR"
echo "PHP腳本路徑: $PHP_SCRIPT"
echo "日誌目錄: $LOG_DIR"
echo ""

# 檢查PHP腳本是否存在
if [ ! -f "$PHP_SCRIPT" ]; then
    echo "錯誤: 找不到 auto_upload_cron.php 檔案"
    echo "請確保此腳本在正確的目錄中執行"
    exit 1
fi

# 建立日誌目錄
if [ ! -d "$LOG_DIR" ]; then
    echo "建立日誌目錄: $LOG_DIR"
    mkdir -p "$LOG_DIR"
    chmod 755 "$LOG_DIR"
fi

# 設定PHP腳本執行權限
echo "設定執行權限..."
chmod +x "$PHP_SCRIPT"

# 找到PHP執行檔路徑
PHP_PATH=$(which php)
if [ -z "$PHP_PATH" ]; then
    echo "錯誤: 找不到PHP執行檔"
    echo "請確保PHP已正確安裝並在PATH中"
    exit 1
fi

echo "PHP路徑: $PHP_PATH"
echo ""

# 顯示現有的cron jobs
echo "=== 當前的 Cron Jobs ==="
crontab -l 2>/dev/null || echo "沒有現有的 cron jobs"
echo ""

# 準備新的cron job條目
CRON_JOB="*/30 * * * * $PHP_PATH $PHP_SCRIPT >> $LOG_DIR/cron.log 2>&1"

echo "=== 建議的 Cron Job 設定 ==="
echo "每30分鐘執行一次自動上傳檢查："
echo "$CRON_JOB"
echo ""

# 詢問用戶是否要安裝cron job
read -p "是否要安裝此 cron job？(y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 備份現有的crontab
    echo "備份現有的 crontab..."
    crontab -l > "$SCRIPT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || true
    
    # 檢查是否已存在相同的cron job
    if crontab -l 2>/dev/null | grep -q "$PHP_SCRIPT"; then
        echo "警告: 已存在相關的 cron job"
        echo "現有的條目:"
        crontab -l 2>/dev/null | grep "$PHP_SCRIPT"
        echo ""
        read -p "是否要替換現有的條目？(y/n): " -n 1 -r
        echo ""
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 移除現有的條目並添加新的
            (crontab -l 2>/dev/null | grep -v "$PHP_SCRIPT"; echo "$CRON_JOB") | crontab -
            echo "已替換 cron job"
        else
            echo "取消安裝"
            exit 0
        fi
    else
        # 添加新的cron job
        (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
        echo "已安裝 cron job"
    fi
    
    echo ""
    echo "=== 安裝完成 ==="
    echo "新的 crontab 內容:"
    crontab -l
    echo ""
    echo "自動上傳將每30分鐘執行一次檢查"
    echo "日誌檔案位置: $LOG_DIR/"
    echo ""
    
else
    echo "取消安裝 cron job"
    echo ""
    echo "=== 手動設定說明 ==="
    echo "如果您想要手動設定 cron job，請執行以下命令："
    echo "crontab -e"
    echo ""
    echo "然後添加以下行："
    echo "$CRON_JOB"
    echo ""
fi

echo "=== 其他設定選項 ==="
echo ""
echo "1. 每小時執行一次 (在每小時的第0分鐘):"
echo "0 * * * * $PHP_PATH $PHP_SCRIPT >> $LOG_DIR/cron.log 2>&1"
echo ""
echo "2. 每天早上9點執行一次:"
echo "0 9 * * * $PHP_PATH $PHP_SCRIPT >> $LOG_DIR/cron.log 2>&1"
echo ""
echo "3. 每15分鐘執行一次:"
echo "*/15 * * * * $PHP_PATH $PHP_SCRIPT >> $LOG_DIR/cron.log 2>&1"
echo ""

echo "=== 測試說明 ==="
echo "您可以手動執行以下命令來測試自動上傳功能："
echo "$PHP_PATH $PHP_SCRIPT"
echo ""
echo "或者使用網頁測試工具："
echo "http://your-domain/manual_upload_test.php"
echo ""

echo "=== 監控說明 ==="
echo "日誌檔案位置:"
echo "- 每日日誌: $LOG_DIR/auto_upload_YYYY-MM-DD.log"
echo "- Cron 日誌: $LOG_DIR/cron.log"
echo ""
echo "您可以使用以下命令查看日誌:"
echo "tail -f $LOG_DIR/auto_upload_$(date +%Y-%m-%d).log"
echo "tail -f $LOG_DIR/cron.log"
echo ""

echo "=== 設定完成 ==="
echo "如有問題，請檢查:"
echo "1. SMB連線設定是否正確"
echo "2. PHP腳本執行權限"
echo "3. 日誌檔案權限"
echo "4. 資料庫連線設定"
echo ""
echo "感謝使用良有營造公文自動上傳系統！"
