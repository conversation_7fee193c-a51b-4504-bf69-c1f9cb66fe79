PHP Warning:  PHP Startup: Unable to load dynamic library '/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so' (tried: /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so (libnnz21.so: cannot open shared object file: No such file or directory), /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so.so (/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so.so: cannot open shared object file: No such file or directory)) in Unknown on line 0
PHP Warning:  PHP Startup: Unable to load dynamic library '/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so' (tried: /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so (libnnz21.so: cannot open shared object file: No such file or directory), /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so.so (/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so.so: cannot open shared object file: No such file or directory)) in Unknown on line 0
PHP Warning:  Module "pdo_pgsql" is already loaded in Unknown on line 0
PHP Warning:  Please note that using Swow with Xdebug may cause unknown problems when PHP version < 8.1 in Unknown on line 0
PHP Warning:  JIT is incompatible with third party extensions that override zend_execute_ex(). JIT disabled. in Unknown on line 0

Warning: JIT is incompatible with third party extensions that override zend_execute_ex(). JIT disabled. in Unknown on line 0
[2025-05-28 19:00:01] === 自動上傳排程開始 ===
[2025-05-28 19:00:01] 資料庫連接成功
[2025-05-28 19:00:01] 發現 1 個需要執行的設定
[2025-05-28 19:00:01] 開始處理設定: 鳳林台電收文 (\\**************\公文\14鳳林台電)
自動上傳：開始OCR處理檔案 uploads/doc_6836ecb2c13a3.pdf
Gemini API 未啟用或API金鑰未設置，使用傳統方法
extract: unknown option -t
usage: mutool extract [options] file.pdf [object numbers]
	-p	password
	-r	convert images to rgb
	-N	do not use ICC color conversions
warning: ICC support is not available
page uploads/doc_6836ecb2c13a3.pdf 1
page uploads/doc_6836ecb2c13a3.pdf 2
PdfParser 庫不可用
Error: Unable to access jarfile /usr/local/bin/pdfbox-app.jar
嘗試使用Tesseract OCR處理PDF: uploads/doc_6836ecb2c13a3.pdf
嘗試PDF轉圖像方法 #1
PDF轉圖像成功，使用方法 #1，產生了 2 個圖像文件
嘗試OCR選項 #1: -l chi_tra+eng --oem 1 --psm 3 -c preserve_interword_spaces=1
Tesseract Open Source OCR Engine v4.1.1 with Leptonica
Warning: Invalid resolution 0 dpi. Using 70 instead.
Estimating resolution as 670
Detected 401 diacritics
Tesseract Open Source OCR Engine v4.1.1 with Leptonica
Warning: Invalid resolution 0 dpi. Using 70 instead.
Estimating resolution as 560
使用OCR選項 #1 成功提取內容，長度: 1742
提取公文字號: 翔科字第1140002438 號
智能提取特定類型主旨: 關於「鳳林EE/S 161kYV 靜態同步補償器 oe FEE)? anid 29 8 台電公司已通知由本公司 用標 APRA MARA TMA A RATS 及施作,請查照惠復。
開始提取發文單位，內容長度: 1742
發文單位提取內容前1000字符: ZAMELERPARAA Bw

地址:40760 台中市西屯區漢翔路1號
FRM AS B4E $M e-mail : <EMAIL>. tw
£z : (04)2702-0001#2169 #& :

受文者: 良有營造股份有限公司

發文日期: 中華民國 114 年05 月 02 日
發文字號:翔科字第1140002438 號
速別: 最速件

密等及解密條件或保密期限: 普通
附件 : 鳳林案決標通知單

主旨: 關於「鳳林EE/S 161kYV 靜態同步補償器 oe
FEE)? anid 29 8 台電公司已通知由本公司  用標
APRA MARA TMA A RATS
及施作,請查照惠復。

一、台電公司已於114 年4月29日通知「鳳林E/S 161kY 則
態同步補償器(STATCOM)新建工程案 」 由本公司得標 ,
見附件一。

一  oe 我雙方尚須商定正式夫約各項條款,又工程時程緊

, 敬請吐公司於接獲本先期動工函後即刻依標前協議書
Senet. 工作定義書及全案時程表先行展開相
關準備與執行作業,以確保工程進度如期推動。

=> KARA a REA BBR Hoe ©

d

正本: 蔡增仲建築師事務所(台北市大同區延平北路一段 92 號 2 樓之 2) 、泰興工程顧
問股份有限公司(臺北市大安區敦化南路 333 號 14 樓)、良有營造股份有限公司
(新北市泰山區工專路 59 巷3 號 2 樓)、南亞塑膠工業股份有限公司新港分公司
配電盤組(台北市內湖區南京東路六段 390 號5 樓)

副本: 科技服務處、物料處


|                        > TREE HH BE LM WEE BE Gol Be dB OT RY BE (OH DE EE
EIBACH ELS OS Ie Se B/E E/E EW EE) eo OE ee

a,

[eo 4S Se Te UE

 

eH

SGOBOLMPSIECOSE ~ SEQ8OLAPS9ECOZE
< PIOBOLAVS9ECOSE + ELTOSOLMPS9EZNSE

 

= Se He Wee wea eb oe S : a Ht StL    |       OOS CLA6CHPEPIL| : Howe
ey oy
直接匹配到建築師事務所: 蔡增仲建築師事務所
開始提取受文者，內容長度: 1742
受文者提取內容前1000字符: ZAMELERPARAA Bw

地址:40760 台中市西屯區漢翔路1號
FRM AS B4E $M e-mail : <EMAIL>. tw
£z : (04)2702-0001#2169 #& :

受文者: 良有營造股份有限公司

發文日期: 中華民國 114 年05 月 02 日
發文字號:翔科字第1140002438 號
速別: 最速件

密等及解密條件或保密期限: 普通
附件 : 鳳林案決標通知單

主旨: 關於「鳳林EE/S 161kYV 靜態同步補償器 oe
FEE)? anid 29 8 台電公司已通知由本公司  用標
APRA MARA TMA A RATS
及施作,請查照惠復。

一、台電公司已於114 年4月29日通知「鳳林E/S 161kY 則
態同步補償器(STATCOM)新建工程案 」 由本公司得標 ,
見附件一。

一  oe 我雙方尚須商定正式夫約各項條款,又工程時程緊

, 敬請吐公司於接獲本先期動工函後即刻依標前協議書
Senet. 工作定義書及全案時程表先行展開相
關準備與執行作業,以確保工程進度如期推動。

=> KARA a REA BBR Hoe ©

d

正本: 蔡增仲建築師事務所(台北市大同區延平北路一段 92 號 2 樓之 2) 、泰興工程顧
問股份有限公司(臺北市大安區敦化南路 333 號 14 樓)、良有營造股份有限公司
(新北市泰山區工專路 59 巷3 號 2 樓)、南亞塑膠工業股份有限公司新港分公司
配電盤組(台北市內湖區南京東路六段 390 號5 樓)

副本: 科技服務處、物料處


|                        > TREE HH BE LM WEE BE Gol Be dB OT RY BE (OH DE EE
EIBACH ELS OS Ie Se B/E E/E EW EE) eo OE ee

a,

[eo 4S Se Te UE

 

eH

SGOBOLMPSIECOSE ~ SEQ8OLAPS9ECOZE
< PIOBOLAVS9ECOSE + ELTOSOLMPS9EZNSE

 

= Se He Wee wea eb oe S : a Ht StL    |       OOS CLA6CHPEPIL| : Howe
ey oy
從標準格式提取受文者(高優先級): 良有營造股份有限公司
開始提取發文日期，內容長度: 1742
發文日期提取內容前1000字符: ZAMELERPARAA Bw

地址:40760 台中市西屯區漢翔路1號
FRM AS B4E $M e-mail : <EMAIL>. tw
£z : (04)2702-0001#2169 #& :

受文者: 良有營造股份有限公司

發文日期: 中華民國 114 年05 月 02 日
發文字號:翔科字第1140002438 號
速別: 最速件

密等及解密條件或保密期限: 普通
附件 : 鳳林案決標通知單

主旨: 關於「鳳林EE/S 161kYV 靜態同步補償器 oe
FEE)? anid 29 8 台電公司已通知由本公司  用標
APRA MARA TMA A RATS
及施作,請查照惠復。

一、台電公司已於114 年4月29日通知「鳳林E/S 161kY 則
態同步補償器(STATCOM)新建工程案 」 由本公司得標 ,
見附件一。

一  oe 我雙方尚須商定正式夫約各項條款,又工程時程緊

, 敬請吐公司於接獲本先期動工函後即刻依標前協議書
Senet. 工作定義書及全案時程表先行展開相
關準備與執行作業,以確保工程進度如期推動。

=> KARA a REA BBR Hoe ©

d

正本: 蔡增仲建築師事務所(台北市大同區延平北路一段 92 號 2 樓之 2) 、泰興工程顧
問股份有限公司(臺北市大安區敦化南路 333 號 14 樓)、良有營造股份有限公司
(新北市泰山區工專路 59 巷3 號 2 樓)、南亞塑膠工業股份有限公司新港分公司
配電盤組(台北市內湖區南京東路六段 390 號5 樓)

副本: 科技服務處、物料處


|                        > TREE HH BE LM WEE BE Gol Be dB OT RY BE (OH DE EE
EIBACH ELS OS Ie Se B/E E/E EW EE) eo OE ee

a,

[eo 4S Se Te UE

 

eH

SGOBOLMPSIECOSE ~ SEQ8OLAPS9ECOZE
< PIOBOLAVS9ECOSE + ELTOSOLMPS9EZNSE

 

= Se He Wee wea eb oe S : a Ht StL    |       OOS CLA6CHPEPIL| : Howe
ey oy
從標記模式提取發文日期: 2025-05-02
extract: unknown option -t
usage: mutool extract [options] file.pdf [object numbers]
	-p	password
	-r	convert images to rgb
	-N	do not use ICC color conversions
warning: ICC support is not available
page uploads/doc_6836ecb2c13a3.pdf 1
page uploads/doc_6836ecb2c13a3.pdf 2
PdfParser 庫不可用
Error: Unable to access jarfile /usr/local/bin/pdfbox-app.jar
嘗試使用Tesseract OCR處理PDF: uploads/doc_6836ecb2c13a3.pdf
嘗試PDF轉圖像方法 #1
PDF轉圖像成功，使用方法 #1，產生了 2 個圖像文件
嘗試OCR選項 #1: -l chi_tra+eng --oem 1 --psm 3 -c preserve_interword_spaces=1
Tesseract Open Source OCR Engine v4.1.1 with Leptonica
Warning: Invalid resolution 0 dpi. Using 70 instead.
Estimating resolution as 670
Detected 401 diacritics
Tesseract Open Source OCR Engine v4.1.1 with Leptonica
Warning: Invalid resolution 0 dpi. Using 70 instead.
Estimating resolution as 560
使用OCR選項 #1 成功提取內容，長度: 1742
自動上傳：OCR處理成功，提取到文件信息
[2025-05-28 19:03:37] 設定 鳳林台電收文 處理完成:
[2025-05-28 19:03:37]   - 掃描檔案: 1 個
[2025-05-28 19:03:37]   - 新檔案: 1 個
[2025-05-28 19:03:37]   - 成功上傳: 0 個
[2025-05-28 19:03:37]   - 失敗: 1 個
[2025-05-28 19:03:37]   錯誤訊息:
[2025-05-28 19:03:37]     - 漢科字第1140002438號.pdf: 資料庫錯誤：SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ocr_content' in 'field list'
[2025-05-28 19:03:37] 設定 鳳林台電收文 處理完成

[2025-05-28 19:03:37] === 執行總結 ===
[2025-05-28 19:03:37] 處理設定數量: 1
[2025-05-28 19:03:37] 總上傳成功: 0 個檔案
[2025-05-28 19:03:37] 總失敗: 1 個檔案
[2025-05-28 19:03:37] 執行時間: 215.58 秒
[2025-05-28 19:03:37] 錯誤總覽:
[2025-05-28 19:03:37]   - 鳳林台電收文: 漢科字第1140002438號.pdf: 資料庫錯誤：SQLSTATE[42S22]: Column not found: 1054 Unknown column 'ocr_content' in 'field list'
[2025-05-28 19:03:37] 檢測到上傳失敗，建議檢查系統狀態
[2025-05-28 19:03:37] === 自動上傳排程結束 ===
PHP Warning:  PHP Startup: Unable to load dynamic library '/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so' (tried: /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so (libnnz21.so: cannot open shared object file: No such file or directory), /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so.so (/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/oci8.so.so: cannot open shared object file: No such file or directory)) in Unknown on line 0
PHP Warning:  PHP Startup: Unable to load dynamic library '/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so' (tried: /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so (libnnz21.so: cannot open shared object file: No such file or directory), /www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so.so (/www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930//www/server/php/80/lib/php/extensions/no-debug-non-zts-20200930/pdo_oci.so.so: cannot open shared object file: No such file or directory)) in Unknown on line 0
PHP Warning:  Module "pdo_pgsql" is already loaded in Unknown on line 0
PHP Warning:  Please note that using Swow with Xdebug may cause unknown problems when PHP version < 8.1 in Unknown on line 0
PHP Warning:  JIT is incompatible with third party extensions that override zend_execute_ex(). JIT disabled. in Unknown on line 0

Warning: JIT is incompatible with third party extensions that override zend_execute_ex(). JIT disabled. in Unknown on line 0
[2025-05-28 20:00:02] === 自動上傳排程開始 ===
Gemini API 已啟用，使用金鑰索引 4，共 5 個金鑰
自動上傳：Gemini API 配置已載入
[2025-05-28 20:00:02] 資料庫連接成功
[2025-05-28 20:00:02] 發現 5 個需要執行的設定
[2025-05-28 20:00:02] 開始處理設定: 花蓮美崙安居收文 (\\**************\公文\06花蓮美崙安居\收文)
自動上傳：開始PDF解析檔案 uploads/doc_6836fac3c81ae.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fac3c81ae.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fac3c81ae.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 340000 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 180780 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 1246
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月8日
自動上傳：民國年轉換成功：中華民國114年5月8日 -> 2025-05-08
自動上傳：文件資料已保存到數據庫，ID: 5321
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1365edfa9a932cfe2b48aaab8642715324182bd87d3ddcec57aee340ac78c6bc' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836faca42a00.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836faca42a00.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836faca42a00.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 212324 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 145852 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 618
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月8日
自動上傳：民國年轉換成功：中華民國114年5月8日 -> 2025-05-08
自動上傳：文件資料已保存到數據庫，ID: 5323
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '3a608408cc6c9ad60dca667973bd1543e59a8c1964c143fee54e1a16e9e15241' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836face945db.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836face945db.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836face945db.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 216672 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 150120 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 634
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月8日
自動上傳：民國年轉換成功：中華民國114年5月8日 -> 2025-05-08
自動上傳：文件資料已保存到數據庫，ID: 5325
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '24e60160d54bbf7550269301c17baeab8c7b9040ea0ce3134ae839e10647af77' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fad38a62f.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fad38a62f.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fad38a62f.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 209756 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 143220 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 621
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月8日
自動上傳：民國年轉換成功：中華民國114年5月8日 -> 2025-05-08
自動上傳：文件資料已保存到數據庫，ID: 5327
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'c72f021ce14c886b830b421b7e1d83b5f20629f184d7d3d737a5cab5fec271a8' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fad889d04.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fad889d04.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fad889d04.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 1889892 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 132580 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 438
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月8日
自動上傳：民國年轉換成功：中華民國114年5月8日 -> 2025-05-08
自動上傳：文件資料已保存到數據庫，ID: 5329
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '07df59622b1aecbdd9d9b55e2c4476bfbe681fc9039f969146f9bb1ed79cada2' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836faddb040f.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836faddb040f.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836faddb040f.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 805508 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 133640 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 528
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月9日
自動上傳：民國年轉換成功：中華民國114年5月9日 -> 2025-05-09
自動上傳：文件資料已保存到數據庫，ID: 5331
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '450afe4bd10fb4bc0e2476dd72299e8f0d90b5c1334d81979fb2d5ea39dedbae' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fae268fd9.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fae268fd9.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fae268fd9.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 200476 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 133856 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 497
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月9日
自動上傳：民國年轉換成功：中華民國114年5月9日 -> 2025-05-09
自動上傳：文件資料已保存到數據庫，ID: 5333
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'cee373837ea36389f753a64beb24d28aefa75d52c637fcbe85b41fe0db0ca104' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fae77713d.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fae77713d.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fae77713d.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 316160 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 163600 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 1066
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月9日
自動上傳：民國年轉換成功：中華民國114年5月9日 -> 2025-05-09
自動上傳：文件資料已保存到數據庫，ID: 5335
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '3601723ab19433d51aa81f6d3f0035d5b0ed64f459020ae0e44942e8bae66614' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836faed6d9ed.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836faed6d9ed.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836faed6d9ed.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 10885212 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 148108 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 757
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月9日
自動上傳：民國年轉換成功：中華民國114年5月9日 -> 2025-05-09
自動上傳：文件資料已保存到數據庫，ID: 5337
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '840b4a0d93a5e4f9c04e8ce52e0c3920326564da824fc078cc74462e136411d3' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836faf32ab88.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836faf32ab88.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836faf32ab88.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 12807052 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 148060 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 738
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月9日
自動上傳：民國年轉換成功：中華民國114年5月9日 -> 2025-05-09
自動上傳：文件資料已保存到數據庫，ID: 5339
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '5304f14ab0e52e8fa7988ba66e433d3e156ef88921dba53c13b042a145450b20' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836faf84ab94.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836faf84ab94.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836faf84ab94.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 223188 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 156800 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 768
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月9日
自動上傳：民國年轉換成功：中華民國114年5月9日 -> 2025-05-09
自動上傳：文件資料已保存到數據庫，ID: 5341
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1997f01e7fad426fcdd2905f0dcf976a21062fb736effb94fa1294bd12c3f11d' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fafdaad02.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fafdaad02.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fafdaad02.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 233820 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 167300 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 913
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月12日
自動上傳：民國年轉換成功：中華民國114年5月12日 -> 2025-05-12
自動上傳：文件資料已保存到數據庫，ID: 5343
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '22b8ba180a778d4eb7f17c12f9ecd676c5c710794f4f5603039296e474e89027' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb03009b6.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb03009b6.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb03009b6.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 2120756 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 141492 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 551
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5345
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1d638d0a8ab54515f56d46f0ea1ca5b68b2435bb3683787e9ad434eda86422d3' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb073e4bb.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb073e4bb.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb073e4bb.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 197676 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 131088 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 589
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5347
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '9e00747913b0e54f7c9dba20feb941961ecad66d5196c11409179d4fe228f870' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb0b72f58.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb0b72f58.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb0b72f58.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 344016 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 187804 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 1063
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5349
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'dda074c074869f3191d4f5acd97da969b7c2c0d6b30ac3ffcab708a3d8de292c' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb11a1a24.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb11a1a24.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb11a1a24.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 204140 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 137424 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 664
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5351
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '58cfbb02c7c6f9ac1ca15ef2d590603c00d45420db8b6db86030e1cb51c146e3' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb1656211.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb1656211.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb1656211.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 2161384 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 140064 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 526
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5353
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'bd40d44e14062b23c8ef71587cefa250c2ac5949b2d1ef67cd9094c87d449f5b' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb1aa8b45.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb1aa8b45.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb1aa8b45.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 195052 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 128452 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 583
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5355
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '1b35747300d97d36156d8bee2188b178acf6f3c6759d134adc8a3303953a7f4d' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb2089252.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb2089252.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb2089252.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 197440 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 130844 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 636
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5357
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '4517a33df7bc47842c7ec73ced0165e0baaced93bb66d5279c3aa0ae0619bf7c' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb254b8dc.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb254b8dc.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb254b8dc.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 366260 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 124588 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 480
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月13日
自動上傳：民國年轉換成功：中華民國114年5月13日 -> 2025-05-13
自動上傳：文件資料已保存到數據庫，ID: 5359
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'd8a665b75bcea37513a673775f6ed633e6406dc948ca46f34eb0c65cf3af28d1' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb29631c4.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb29631c4.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb29631c4.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 8472932 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 207404 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 1088
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月14日
自動上傳：民國年轉換成功：中華民國114年5月14日 -> 2025-05-14
自動上傳：文件資料已保存到數據庫，ID: 5361
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '341184496126708cac8e1fed20f661dc24179ce2a60fbc828023397ea8e60e7a' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb2f61cd1.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb2f61cd1.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb2f61cd1.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 203104 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 136524 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 586
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月14日
自動上傳：民國年轉換成功：中華民國114年5月14日 -> 2025-05-14
自動上傳：文件資料已保存到數據庫，ID: 5363
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'eb1514f828b4b561a9dbb93a0c2293daee527f952eccc900cefa674611326dd1' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb338cff6.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb338cff6.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb338cff6.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 229572 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 163140 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 861
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月14日
自動上傳：民國年轉換成功：中華民國114年5月14日 -> 2025-05-14
自動上傳：文件資料已保存到數據庫，ID: 5365
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '32b75145375c0a897b894a06a9812addd6bfdd3011d228943b1dc34f091e6c43' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb386bcb8.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb386bcb8.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb386bcb8.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 206856 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 140152 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 539
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月14日
自動上傳：民國年轉換成功：中華民國114年5月14日 -> 2025-05-14
自動上傳：文件資料已保存到數據庫，ID: 5367
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'a3786cd0a4ef92103b19f15c7a52c3566d9299602e13dafac140c79b364d83a8' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb3ceff9e.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb3ceff9e.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb3ceff9e.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 2117096 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 127272 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 469
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月15日
自動上傳：民國年轉換成功：中華民國114年5月15日 -> 2025-05-15
自動上傳：文件資料已保存到數據庫，ID: 5369
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '20c7773c6808aaa2a7a58d6e2ada4097c0aa0330b94c25fde6899f2877e6bfdc' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb41e415d.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb41e415d.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb41e415d.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 220124 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 153692 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 697
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月15日
自動上傳：民國年轉換成功：中華民國114年5月15日 -> 2025-05-15
自動上傳：文件資料已保存到數據庫，ID: 5371
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '4e7824e871fb7b970d8c99ae1776e87811a66a0fe1f449f404a0c52a5843d741' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb46d70cb.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb46d70cb.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb46d70cb.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 206384 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 139708 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 646
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月15日
自動上傳：民國年轉換成功：中華民國114年5月15日 -> 2025-05-15
自動上傳：文件資料已保存到數據庫，ID: 5372
自動上傳：開始PDF解析檔案 uploads/doc_6836fb4ba4ba5.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb4ba4ba5.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb4ba4ba5.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 340128 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 185696 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 1242
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月15日
自動上傳：民國年轉換成功：中華民國114年5月15日 -> 2025-05-15
自動上傳：文件資料已保存到數據庫，ID: 5374
自動上傳：開始PDF解析檔案 uploads/doc_6836fb5340aa4.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb5340aa4.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb5340aa4.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 720368 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 722132 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 622
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月16日
自動上傳：民國年轉換成功：中華民國114年5月16日 -> 2025-05-16
自動上傳：文件資料已保存到數據庫，ID: 5376
自動上傳：開始PDF解析檔案 uploads/doc_6836fb58f407f.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb58f407f.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb58f407f.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 210756 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 144224 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 623
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月19日
自動上傳：民國年轉換成功：中華民國114年5月19日 -> 2025-05-19
自動上傳：文件資料已保存到數據庫，ID: 5378
自動上傳：開始PDF解析檔案 uploads/doc_6836fb5d93e25.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb5d93e25.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb5d93e25.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 217048 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 150484 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 645
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月19日
自動上傳：民國年轉換成功：中華民國114年5月19日 -> 2025-05-19
自動上傳：文件資料已保存到數據庫，ID: 5380
自動上傳：開始PDF解析檔案 uploads/doc_6836fb62253c2.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb62253c2.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb62253c2.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 221612 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 155372 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 736
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月19日
自動上傳：民國年轉換成功：中華民國114年5月19日 -> 2025-05-19
自動上傳：文件資料已保存到數據庫，ID: 5382
自動上傳：開始PDF解析檔案 uploads/doc_6836fb6724d9c.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb6724d9c.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb6724d9c.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 220940 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 154544 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 813
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月20日
自動上傳：民國年轉換成功：中華民國114年5月20日 -> 2025-05-20
自動上傳：文件資料已保存到數據庫，ID: 5384
自動上傳：開始PDF解析檔案 uploads/doc_6836fb6c58f59.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb6c58f59.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb6c58f59.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 2188120 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 141428 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 551
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月20日
自動上傳：民國年轉換成功：中華民國114年5月20日 -> 2025-05-20
自動上傳：文件資料已保存到數據庫，ID: 5386
自動上傳：開始PDF解析檔案 uploads/doc_6836fb70b4e66.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb70b4e66.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb70b4e66.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 315776 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 166264 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 962
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月20日
自動上傳：民國年轉換成功：中華民國114年5月20日 -> 2025-05-20
自動上傳：文件資料已保存到數據庫，ID: 5388
自動上傳：開始PDF解析檔案 uploads/doc_6836fb768ab22.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb768ab22.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb768ab22.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 228452 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 162084 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 767
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月20日
自動上傳：民國年轉換成功：中華民國114年5月20日 -> 2025-05-20
自動上傳：文件資料已保存到數據庫，ID: 5390
自動上傳：開始PDF解析檔案 uploads/doc_6836fb7bc8494.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb7bc8494.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb7bc8494.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 536360 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 150248 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 763
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月20日
自動上傳：民國年轉換成功：中華民國114年5月20日 -> 2025-05-20
自動上傳：文件資料已保存到數據庫，ID: 5392
自動上傳：開始PDF解析檔案 uploads/doc_6836fb813de9d.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb813de9d.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb813de9d.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 655112 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 182568 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 1025
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月20日
自動上傳：民國年轉換成功：中華民國114年5月20日 -> 2025-05-20
自動上傳：文件資料已保存到數據庫，ID: 5395
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'cca8f627b27350db5c6b139f67aee62644b6aeefdb532281fa8de4a8cb311cfd' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb87061fa.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb87061fa.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb87061fa.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 207428 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 140788 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 709
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月21日
自動上傳：民國年轉換成功：中華民國114年5月21日 -> 2025-05-21
自動上傳：文件資料已保存到數據庫，ID: 5397
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '4855cdb04a5da51cd7519acd92f0df93f031634399337010040d714594de8e4b' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb8bd6968.pdf
Syntax Warning: Bad appearance for annotation
Syntax Warning: Bad appearance for annotation
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb8bd6968.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb8bd6968.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 8920928 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 151488 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 763
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月21日
自動上傳：民國年轉換成功：中華民國114年5月21日 -> 2025-05-21
自動上傳：文件資料已保存到數據庫，ID: 5399
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'a4f474cf602541c68da8cb865e689a39d69e6efabfdb66ed2307a6ed66b61889' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb9124f2d.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb9124f2d.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb9124f2d.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 220548 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 154380 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 741
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月21日
自動上傳：民國年轉換成功：中華民國114年5月21日 -> 2025-05-21
自動上傳：文件資料已保存到數據庫，ID: 5401
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '5ed37abeb7abe876f94f0c28d06e6ac1c19cb5e32c4a1f0413b621f851a41019' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb9669e8d.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb9669e8d.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb9669e8d.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 226936 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 160552 bytes
Gemini API: 發送請求到 https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent
Gemini API: 回應狀態碼: 200
Gemini API: 成功取得回應，內容長度: 949
Gemini API: 從代碼塊中提取JSON
Gemini API: 成功解析JSON回應
Gemini API 解析成功
自動上傳：Gemini API 解析成功，跳過額外的OCR處理
自動上傳：PDF解析結果: 成功
自動上傳：開始轉換日期格式：中華民國114年5月21日
自動上傳：民國年轉換成功：中華民國114年5月21日 -> 2025-05-21
自動上傳：文件資料已保存到數據庫，ID: 5403
記錄檔案雜湊值失敗: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry '91ffbd3ffe20a72c499f75072cd6fa78bf86121cc935743cc7b778be415f584b' for key 'idx_file_hash'
自動上傳：開始PDF解析檔案 uploads/doc_6836fb9c7847d.pdf
優先使用 Gemini API 解析 PDF: uploads/doc_6836fb9c7847d.pdf
Gemini API: 開始處理PDF文件: uploads/doc_6836fb9c7847d.pdf
Gemini API: 使用API金鑰: AIzaSyBffZ...
Gemini API: PDF文件大小: 3415980 bytes (base64編碼後)
Gemini API: 對所有PDF文件都只處理第一頁
sh: pdftk: command not found
Gemini API: pdftk提取第一頁失敗，嘗試使用ghostscript
Gemini API: 只處理第一頁後的大小: 1241124 bytes
Gemini API: 第一頁仍然太大，嘗試降低圖像質量
使用 MuPDF 成功提取內容，長度: 90
使用原有方法提取文本失敗，內容長度: 90，嘗試使用 PaddleOCR: uploads/doc_6836fb9c7847d.pdf
PDF 轉圖像成功，產生了 3 個圖像文件
使用 PaddleOCR 處理圖像: /www/wwwroot/paper.lybuild.com.tw/debug_pdfs/images_doc_6836fb9c7847d/page-000.png, 語言模式: ch
sh: python: command not found
PaddleOCR 處理圖像失敗: /www/wwwroot/paper.lybuild.com.tw/debug_pdfs/images_doc_6836fb9c7847d/page-000.png, 錯誤: 未知錯誤
使用 PaddleOCR 處理圖像: /www/wwwroot/paper.lybuild.com.tw/debug_pdfs/images_doc_6836fb9c7847d/page-001.png, 語言模式: ch
sh: python: command not found
PaddleOCR 處理圖像失敗: /www/wwwroot/paper.lybuild.com.tw/debug_pdfs/images_doc_6836fb9c7847d/page-001.png, 錯誤: 未知錯誤
使用 PaddleOCR 處理圖像: /www/wwwroot/paper.lybuild.com.tw/debug_pdfs/images_doc_6836fb9c7847d/page-002.png, 語言模式: ch
sh: python: command not found
PaddleOCR 處理圖像失敗: /www/wwwroot/paper.lybuild.com.tw/debug_pdfs/images_doc_6836fb9c7847d/page-002.png, 錯誤: 未知錯誤
PaddleOCR 提取內容為空或太短，嘗試使用 Tesseract OCR
嘗試PDF轉圖像方法 #1
