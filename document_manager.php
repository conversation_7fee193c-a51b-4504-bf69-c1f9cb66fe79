<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
session_start();
require_once 'db.php';

// 初始化用戶資訊
$site = $_SESSION['site'] ?? '總公司';
$username = $_SESSION['username'] ?? '';
$role = $_SESSION['role'] ?? '';
$role_mapping = [
    'admin' => '系統管理員',
    'user' => '一般用戶',
    'manager' => '主管'
];

// 獲取通知資訊
$notification_count = 0;
$all_notifications = [];
$notifications = []; // 未讀通知
try {
    // 獲取未讀通知
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = ? AND `read` = 0 ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$_SESSION['user_id']]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $notification_count = count($notifications);

    // 獲取所有通知（包括已讀和未讀）
    $stmt = $pdo->prepare("SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$_SESSION['user_id']]);
    $all_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching notifications: " . $e->getMessage());
}

// 用戶資訊
$user_id = $_SESSION['user_id'];
$role = $_SESSION['role'];
$username = $_SESSION['username'] ?? '';

// 工地資訊
try {
    $stmt = $pdo->query("SELECT id, name FROM sites ORDER BY name");
    $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching sites: " . $e->getMessage());
    $sites = [];
}

// 公文類別
try {
    $stmt = $pdo->query("SELECT id, code, name FROM document_categories WHERE is_active = 1 ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

// 處理公文上傳
$upload_success = false;
$upload_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("收到 POST 請求，處理上傳");

    if (isset($_POST['action']) && $_POST['action'] === 'upload') {
        $upload_type = $_POST['upload_type'] ?? 'unknown';
        error_log("偵測到上傳動作: action=upload, type={$upload_type}");

        // 處理單文件上傳
        if (isset($_FILES['document'])) {
            error_log("處理單一文件上傳 (type={$upload_type}): " . json_encode($_FILES['document']));

            if ($_FILES['document']['error'] === 0) {
                error_log("文件上傳狀態正常，開始處理");
                // 獲取要通知的工地參數
                $notifySites = isset($_POST['notify_sites']) ? $_POST['notify_sites'] : [];
                // 獲取選擇的工地和分類
                $selected_site_id = isset($_POST['site_id']) ? intval($_POST['site_id']) : null;
                $selected_category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : null;
                error_log("獲取到通知工地列表: " . json_encode($notifySites));
                error_log("選擇的工地ID: " . ($selected_site_id ?? 'null'));
                error_log("選擇的分類ID: " . ($selected_category_id ?? 'null'));

                // 檢查是否為慈濟文件
                $isTzuChiDoc = false;
                if (strpos($_FILES['document']['name'], '慈濟') !== false ||
                    strpos($_FILES['document']['name'], '佛教') !== false ||
                    strpos($_FILES['document']['name'], 'TZU-CHI') !== false) {
                    $isTzuChiDoc = true;
                    error_log("單一上傳: 檢測到慈濟文件: " . $_FILES['document']['name']);
                }

                $result = processDocumentUpload(
                    $_FILES['document'],
                    $pdo,
                    $user_id,
                    $upload_type,
                    $notifySites,
                    $selected_site_id,
                    $selected_category_id,
                    $isTzuChiDoc
                );

                // 修改：檢查是否是重複文件的情況
                if (isset($result['duplicate']) && $result['duplicate']) {
                    $upload_success = false;
                    $upload_message = $result['message'];
                    // 保存跳過的文件信息，以便在界面上顯示
                    $skipped_file = [
                        'name' => $_FILES['document']['name'],
                        'reason' => '發文字號重複',
                        'document_number' => $result['existing_document']['document_number'] ?? '',
                        'existing_id' => $result['existing_document']['id'] ?? null
                    ];
                    $_SESSION['skipped_file'] = $skipped_file;
                } else {
                    $upload_success = $result['success'];
                    $upload_message = $result['message'];
                    // 清除之前可能存在的跳過文件信息
                    if (isset($_SESSION['skipped_file'])) {
                        unset($_SESSION['skipped_file']);
                    }
                }

                error_log("單一文件處理結果: " . ($upload_success ? '成功' : '失敗') . ", 訊息: " . $upload_message);
            } else {
                error_log("文件上傳錯誤，代碼: " . $_FILES['document']['error']);
                $upload_success = false;
                $upload_message = '文件上傳失敗，錯誤代碼: ' . $_FILES['document']['error'];
            }
        } else {
            error_log("未找到文件上傳資料 (document)");
        }

        // 處理批量上傳
        if (isset($_FILES['documents']) && is_array($_FILES['documents']['name'])) {
            error_log("處理批量上傳，文件數: " . count($_FILES['documents']['name']));
            $totalFiles = count($_FILES['documents']['name']);
            $successCount = 0;

            // 獲取批量上傳的通知工地
            $notifySites = isset($_POST['notify_sites']) ? $_POST['notify_sites'] : [];
            error_log("批量上傳通知工地列表: " . json_encode($notifySites));

            for ($i = 0; $i < $totalFiles; $i++) {
                if ($_FILES['documents']['error'][$i] === 0) {
                    $file = [
                        'name' => $_FILES['documents']['name'][$i],
                        'type' => $_FILES['documents']['type'][$i],
                        'tmp_name' => $_FILES['documents']['tmp_name'][$i],
                        'error' => $_FILES['documents']['error'][$i],
                        'size' => $_FILES['documents']['size'][$i]
                    ];

                    error_log("處理批量文件 #" . ($i+1) . ": " . $file['name']);

                    // 確保每個批量文件都重新載入Gemini API配置，以確保金鑰輪換機制正常工作
                    if (file_exists('gemini_config.php')) {
                        // 強制重新載入配置文件，確保每個文件都使用新的API金鑰
                        if (function_exists('opcache_invalidate')) {
                            opcache_invalidate('gemini_config.php', true);
                        }
                        require_once 'gemini_config.php';
                        error_log("批量上傳: Gemini API 配置已重新載入，使用金鑰輪換機制");
                    } else if (class_exists('PDFProcessor') && method_exists('PDFProcessor', 'setGeminiOptions')) {
                        // 如果配置文件不存在，使用預設金鑰
                        PDFProcessor::setGeminiOptions([
                            'enabled' => true,
                            'api_key' => 'AIzaSyDgSD54jWdWw2v0nIbU425RCaLtDx_uy2k',
                            'fallback_to_traditional' => true
                        ]);
                        error_log("批量上傳: Gemini API 選項已手動設置");
                    }

                    // 確保PDFProcessor類已正確載入
                    if (!class_exists('PDFProcessor')) {
                        require_once 'pdf_processor.php';
                        error_log("批量上傳: PDFProcessor類已重新載入");
                    }

                    // 處理文件上傳 - 對於批量上傳，使用與單一上傳完全相同的處理邏輯
                    $result = processDocumentUpload($file, $pdo, $user_id, 'batch', $notifySites);
                if ($result['success']) {
                        $successCount++;
                        error_log("批量文件 #" . ($i+1) . " 上傳成功");
                    } else {
                        error_log("批量文件 #" . ($i+1) . " 上傳失敗: " . $result['message']);
                    }
                } else {
                    error_log("批量文件 #" . ($i+1) . " 上傳錯誤，代碼: " . $_FILES['documents']['error'][$i]);
                }
            }

            $upload_success = $successCount > 0;
            $upload_message = "已成功上傳並處理 {$successCount}/{$totalFiles} 份公文";
            error_log("批量上傳完成: " . $upload_message);
        }
    } else {
        error_log("未找到上傳動作標識 (action=upload)");
    }
}

// 分頁參數設置
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 10; // 預設每頁10筆
$valid_per_page = [10, 20, 50, 100]; // 有效的每頁筆數選項
if (!in_array($per_page, $valid_per_page)) {
    $per_page = 10; // 如果不是有效值，預設為10
}

// 處理搜索
$search_term = $_GET['search'] ?? '';
$site_filter = isset($_GET['site']) ? (int)$_GET['site'] : 0;
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$sender_filter = isset($_GET['sender']) ? $_GET['sender'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// 獲取所有不同的發文單位
$senders = [];
try {
    $senderQuery = "SELECT DISTINCT sender FROM official_documents WHERE sender IS NOT NULL AND sender != '' ORDER BY sender";
    $senderStmt = $pdo->query($senderQuery);
    $senders = $senderStmt->fetchAll(PDO::FETCH_COLUMN);

    // 定義優先顯示的發文單位
    $prioritySenders = ['國家住宅', '內政部', '花蓮縣政府', '慈濟', '孫文郁', '恩典'];

    // 確保會話已啟動
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // 定義需要歸類的發文單位前綴
    $groupPrefixes = [
        '財團法人中華民國佛教慈濟慈善事業基金會' => '慈濟基金會',
        '中華民國' => '慈濟基金會',
        '佛教慈濟' => '慈濟基金會',
        '慈濟' => '慈濟基金會'
    ];

    // 調試信息
    error_log("原始發文單位數量: " . count($senders));
    error_log("原始發文單位列表: " . json_encode(array_slice($senders, 0, 10)) . "...");

    // 直接創建慈濟基金會分組
    $groupedSenders = [];
    $processedSenders = [];
    $cizuGroup = [];

    // 創建內政部相關分組
    $neizhengGroup = []; // 內政部國土管理署分組
    $yingjianGroup = []; // 內政部營建署分組

    // 先處理需要歸類的發文單位
    foreach ($senders as $sender) {
        // 檢查是否包含慈濟相關關鍵字
        if (strpos($sender, '慈濟') !== false ||
            strpos($sender, '中華民國') !== false ||
            strpos($sender, '財團法人') !== false) {
            // 添加到慈濟基金會分組
            $cizuGroup[] = $sender;
        }
        // 檢查是否包含內政部國土管理署相關關鍵字
        else if (strpos($sender, '內政部國土') !== false ||
                 strpos($sender, '國土管理署') !== false) {
            // 添加到內政部國土管理署分組
            $neizhengGroup[] = $sender;
        }
        // 檢查是否包含內政部營建署相關關鍵字
        else if (strpos($sender, '內政部營建署') !== false ||
                 strpos($sender, '營建署') !== false) {
            // 添加到內政部營建署分組
            $yingjianGroup[] = $sender;
        } else {
            // 如果不屬於任何分組，則保持原樣
            $processedSenders[] = $sender;
        }
    }

    // 如果有慈濟相關的發文單位，則添加到分組中
    if (!empty($cizuGroup)) {
        $groupedSenders['慈濟基金會'] = $cizuGroup;
    }

    // 如果有內政部國土管理署相關的發文單位，則添加到分組中
    if (!empty($neizhengGroup)) {
        $groupedSenders['內政部國土管理署'] = $neizhengGroup;
        error_log("內政部國土管理署分組數量: " . count($neizhengGroup));
        error_log("內政部國土管理署分組內容: " . json_encode($neizhengGroup));
    }

    // 如果有內政部營建署相關的發文單位，則添加到分組中
    if (!empty($yingjianGroup)) {
        $groupedSenders['內政部營建署'] = $yingjianGroup;
        error_log("內政部營建署分組數量: " . count($yingjianGroup));
        error_log("內政部營建署分組內容: " . json_encode($yingjianGroup));
    }

    // 調試信息
    error_log("慈濟基金會分組數量: " . count($cizuGroup));
    error_log("慈濟基金會分組內容: " . json_encode($cizuGroup));
    error_log("所有分組: " . json_encode(array_keys($groupedSenders)));

    // 將優先發文單位從未分組的列表中移除
    $otherSenders = array_filter($processedSenders, function($sender) use ($prioritySenders) {
        foreach ($prioritySenders as $priority) {
            if (strpos($sender, $priority) !== false) {
                return false;
            }
        }
        return true;
    });

    // 找出實際存在的優先發文單位
    $existingPrioritySenders = array_filter($processedSenders, function($sender) use ($prioritySenders) {
        foreach ($prioritySenders as $priority) {
            if (strpos($sender, $priority) !== false) {
                return true;
            }
        }
        return false;
    });

    // 創建最終排序的發文單位列表
    $finalSenders = [];

    // 1. 首先添加慈濟基金會（如果存在）
    if (isset($groupedSenders['慈濟基金會'])) {
        $finalSenders[] = '慈濟基金會';
    }

    // 2. 添加內政部國土管理署（如果存在）
    if (isset($groupedSenders['內政部國土管理署'])) {
        $finalSenders[] = '內政部國土管理署';
    }

    // 3. 添加內政部營建署（如果存在）
    if (isset($groupedSenders['內政部營建署'])) {
        $finalSenders[] = '內政部營建署';
    }

    // 4. 然後添加優先發文單位（除了已經包含在分組中的）
    foreach ($prioritySenders as $priority) {
        // 跳過已經包含在分組中的優先發文單位
        if ($priority !== '慈濟' && $priority !== '內政部' && $priority !== '營建署') {
            foreach ($processedSenders as $key => $sender) {
                if (strpos($sender, $priority) !== false) {
                    $finalSenders[] = $sender;
                    // 從未處理列表中移除
                    unset($processedSenders[$key]);
                }
            }
        }
    }

    // 3. 最後添加其他發文單位
    $finalSenders = array_merge($finalSenders, $processedSenders);

    // 更新發文單位列表
    $senders = $finalSenders;

    // 調試信息
    error_log("最終發文單位列表: " . json_encode(array_slice($senders, 0, 10)) . "...");

    // 保存原始發文單位與簡化名稱的映射關係，用於表單提交時還原
    $_SESSION['sender_groups'] = $groupedSenders;
} catch (PDOException $e) {
    error_log("Error fetching senders: " . $e->getMessage());
}

// 構建基本查詢
$baseQuery = "FROM official_documents od
              LEFT JOIN sites s ON od.site_id = s.id
              LEFT JOIN document_categories dc ON od.category_id = dc.id
              LEFT JOIN users u ON od.uploaded_by = u.id
              WHERE 1=1";

$params = [];

// 判斷是否為管理員或總公司主管
$isAdmin = ($role === 'admin');
$isHqSupervisor = ($role === 'hq_supervisor');

// 判斷是否為總公司人員，不限職位
$isHqStaff = ($site === '總公司');

// 合併權限判斷 - 允許總公司相關角色和所有總公司人員查看所有工地
$is_hq_role = ($isAdmin || $isHqSupervisor || $isHqStaff);

// 檢查批量上傳功能是否出現錯誤
$db_update_needed = false;
if ($isAdmin) {
    try {
        $checkColumnQuery = "SHOW COLUMNS FROM upload_tasks LIKE 'skipped_files'";
        $columnExists = $pdo->query($checkColumnQuery)->fetch();
        if (!$columnExists) {
            $db_update_needed = true;
        }
    } catch (PDOException $e) {
        $db_update_needed = true;
    }
}

// 檢查用戶角色 - 限制非總公司角色只能查看自己工地的文件
if (!$is_hq_role) {
    // 獲取用戶所屬工地
    try {
        $stmt = $pdo->prepare("SELECT s.id FROM users u JOIN sites s ON u.site = s.name WHERE u.id = ?");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $user_site_id = $result ? $result['id'] : 0;

        if ($user_site_id) {
            $baseQuery .= " AND od.site_id = ?";
            $params[] = $user_site_id;

            // 如果用戶手動選擇了其他工地，覆蓋為自己的工地
            $site_filter = $user_site_id;
        }
    } catch (PDOException $e) {
        error_log("Error fetching user site: " . $e->getMessage());
    }
}

if (!empty($search_term)) {
    // 將搜索詞分割為多個關鍵詞
    $keywords = preg_split('/\s+/', trim($search_term));

    // 調試信息
    error_log("搜索關鍵詞: " . json_encode($keywords));

    // 構建複雜的搜索條件
    $searchConditions = [];

    // 為每個關鍵詞創建一個條件組
    foreach ($keywords as $keyword) {
        if (strlen($keyword) > 0) {
            $searchPattern = "%{$keyword}%";

            // 添加條件：每個關鍵詞必須出現在標題、發文字號或內容摘要中的至少一個
            $searchConditions[] = "(od.title LIKE ? OR od.document_number LIKE ? OR od.content_summary LIKE ?)";
            $params[] = $searchPattern;
            $params[] = $searchPattern;
            $params[] = $searchPattern;
        }
    }

    // 如果有有效的搜索條件，則添加到查詢中
    if (!empty($searchConditions)) {
        // 使用 AND 連接所有關鍵詞條件，確保所有關鍵詞都必須匹配
        $baseQuery .= " AND (" . implode(" AND ", $searchConditions) . ")";

        // 調試信息
        error_log("搜索條件: " . implode(" AND ", $searchConditions));
        error_log("搜索參數數量: " . count($params));
    }
}

// 僅當用戶可以查看全部工地或已選擇了篩選工地時，才添加工地篩選條件
if ($is_hq_role && $site_filter > 0) {
    $baseQuery .= " AND od.site_id = ?";
    $params[] = $site_filter;
}

if ($category_filter > 0) {
    $baseQuery .= " AND od.category_id = ?";
    $params[] = $category_filter;
}

// 添加發文單位篩選條件
if (!empty($sender_filter)) {
    // 檢查是否是簡化的發文單位名稱
    if (isset($_SESSION['sender_groups'][$sender_filter]) && !empty($_SESSION['sender_groups'][$sender_filter])) {
        // 如果是簡化名稱，使用 IN 查詢包含該組的所有發文單位
        $placeholders = implode(',', array_fill(0, count($_SESSION['sender_groups'][$sender_filter]), '?'));
        $baseQuery .= " AND od.sender IN ($placeholders)";
        $params = array_merge($params, $_SESSION['sender_groups'][$sender_filter]);

        // 調試信息
        error_log("使用分組查詢: " . $sender_filter);
        error_log("查詢條件: IN (" . implode(', ', $_SESSION['sender_groups'][$sender_filter]) . ")");
    } else {
        // 如果是原始發文單位名稱，直接使用等於查詢
        $baseQuery .= " AND od.sender = ?";
        $params[] = $sender_filter;

        // 調試信息
        error_log("使用直接查詢: " . $sender_filter);
    }
}

// 添加日期範圍篩選條件
if (!empty($start_date)) {
    $baseQuery .= " AND od.issue_date >= ?";
    $params[] = $start_date;
    error_log("添加開始日期篩選: " . $start_date);
}

if (!empty($end_date)) {
    $baseQuery .= " AND od.issue_date <= ?";
    $params[] = $end_date;
    error_log("添加結束日期篩選: " . $end_date);
}

// 計算總記錄數
try {
    $countQuery = "SELECT COUNT(*) as total " . $baseQuery;
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_documents = $result ? $result['total'] : 0;

    // 確保錯誤訊息和成功訊息不會同時出現
    if (isset($_SESSION['success_message']) && isset($_SESSION['error'])) {
        // 保留最新添加的訊息
        $errorTime = $_SESSION['error_time'] ?? 0;
        $successTime = $_SESSION['success_time'] ?? 0;

        if ($successTime > $errorTime) {
            unset($_SESSION['error']);
        } else {
            unset($_SESSION['success_message']);
        }
    }
} catch (PDOException $e) {
    error_log("Error counting documents: " . $e->getMessage());
    $total_documents = 0;
}

// 計算總頁數
$total_pages = ceil($total_documents / $per_page);
if ($page > $total_pages && $total_pages > 0) {
    $page = $total_pages;
}

// 計算偏移量
$offset = ($page - 1) * $per_page;

// 構建分頁查詢
try {
    $query = "SELECT od.*, s.name as site_name, dc.name as category_name, dc.code as category_code,
              u.name as uploader_name " . $baseQuery . " ORDER BY od.issue_date DESC LIMIT ? OFFSET ?";

    $finalParams = array_merge($params, [$per_page, $offset]);
    $stmt = $pdo->prepare($query);

    // 手動綁定參數類型
    foreach ($finalParams as $i => $param) {
        $paramIndex = $i + 1; // PDO 參數索引從 1 開始
        if ($i === count($finalParams) - 2) { // LIMIT 參數
            $stmt->bindValue($paramIndex, (int)$param, PDO::PARAM_INT);
        } else if ($i === count($finalParams) - 1) { // OFFSET 參數
            $stmt->bindValue($paramIndex, (int)$param, PDO::PARAM_INT);
        } else {
            $stmt->bindValue($paramIndex, $param);
        }
    }

    $stmt->execute();
    $documents = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching documents: " . $e->getMessage());
    $documents = [];
}

// 如果沒有找到文檔，檢查表中是否有數據
if (count($documents) == 0 && $page == 1) {
    try {
        $checkQuery = "SELECT COUNT(*) as total FROM official_documents";
        $stmt = $pdo->query($checkQuery);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        error_log("資料表中總文檔數: " . ($result ? $result['total'] : '查詢失敗'));
    } catch (PDOException $e) {
        error_log("Error checking total documents: " . $e->getMessage());
    }
}

/**
 * 處理公文上傳與分析
 */
function processDocumentUpload($file, $pdo, $user_id, $upload_type = 'single', $notifySites = [], $selected_site_id = null, $selected_category_id = null) {
    try {
        // 添加調試日誌，追蹤函數執行
        error_log("開始處理文件上傳: " . $file['name'] . ", 大小: " . $file['size'] . ", 類型: " . $file['type'] . ", 上傳類型: " . $upload_type);
        error_log("選擇的工地ID: " . ($selected_site_id ?? 'null') . ", 分類ID: " . ($selected_category_id ?? 'null'));

        // 生成唯一文件名
        $fileName = uniqid('doc_') . '.pdf';
        $uploadDir = 'uploads/';

        // 確保上傳目錄存在
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                error_log("無法創建上傳目錄: " . $uploadDir);
            }
            chmod($uploadDir, 0777);
        }

        $filePath = $uploadDir . $fileName;

        // 移動文件到目標目錄
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            error_log("文件移動失敗，從 " . $file['tmp_name'] . " 到 " . $filePath);
            // 嘗試直接複製文件
            if (!copy($file['tmp_name'], $filePath)) {
                error_log("文件複製也失敗，使用原始文件路徑");
                $filePath = $file['tmp_name'];
                $fileName = basename($file['name']);
            }
        }

        // 初始化文件信息
        $docInfo = [
            'document_number' => null,
            'title' => basename($fileName),
            'sender' => '未提供發文單位',
            'receiver' => '未提供受文者',
            'issue_date' => date('Y-m-d'),
            'content_summary' => null,
            'notes' => null
        ];

        // 嘗試分析 PDF 內容
        try {
            // 使用標準分析函數處理所有文件
            $analyzedInfo = analyzePDFContent($filePath);
            if ($analyzedInfo && is_array($analyzedInfo)) {
                $docInfo = array_merge($docInfo, array_filter($analyzedInfo, function($value) {
                    return $value !== null && $value !== '';
                }));
            }
        } catch (Exception $e) {
            error_log("PDF分析失敗，使用基本信息: " . $e->getMessage());
            $docInfo['notes'] = "PDF分析失敗: " . $e->getMessage();
        }

        // 確定工地ID
        $site_id = $selected_site_id;
        if (!$site_id) {
            try {
                $site_id = determineSiteFromContent($docInfo, $pdo);
                error_log("從文件內容確定工地ID: " . $site_id);
            } catch (Exception $e) {
                error_log("工地判斷失敗，使用默認工地: " . $e->getMessage());
                $site_id = 1; // 使用默認工地
            }
        }

        // 處理日期格式
        try {
            if (!empty($docInfo['issue_date'])) {
                $date = new DateTime($docInfo['issue_date']);
                $issueDate = $date->format('Y-m-d');
            } else {
                $issueDate = date('Y-m-d');
            }
        } catch (Exception $e) {
            error_log("日期格式化失敗，使用當前日期: " . $e->getMessage());
            $issueDate = date('Y-m-d');
        }

        // 檢查發文字號是否已存在
        if (!empty($docInfo['document_number'])) {
            $checkDuplicateSql = "SELECT id, file_name, title FROM official_documents WHERE document_number = ? LIMIT 1";
            $checkStmt = $pdo->prepare($checkDuplicateSql);
            $checkStmt->execute([$docInfo['document_number']]);
            $existingDoc = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($existingDoc) {
                // 發文字號已存在，跳過上傳
                error_log("發現重複的發文字號: " . $docInfo['document_number'] . "，已存在文件ID: " . $existingDoc['id'] . ", 標題: " . $existingDoc['title']);

                // 刪除臨時上傳的文件
                if (file_exists($filePath) && strpos($filePath, 'uploads/') === 0) {
                    @unlink($filePath);
                }

                return [
                    'success' => false,
                    'skipped' => true,
                    'duplicate' => true,
                    'message' => '發文字號「' . $docInfo['document_number'] . '」已存在於系統中，已跳過上傳。',
                    'existing_document' => [
                        'id' => $existingDoc['id'],
                        'title' => $existingDoc['title'],
                        'document_number' => $docInfo['document_number']
                    ]
                ];
            }
        }

        // 準備數據庫插入
        try {
            $pdo->beginTransaction();

            $sql = "
                INSERT INTO official_documents (
                    file_name, file_path, document_number,
                    title, sender, receiver,
                    issue_date, content_summary, notes,
                    site_id, category_id,
                    uploaded_by, uploaded_at, is_processed,
                    upload_status
                ) VALUES (
                    ?, ?, ?,
                    ?, ?, ?,
                    ?, ?, ?,
                    ?, ?,
                    ?, NOW(), 0,
                    ?
                )
            ";

            $params = [
                $fileName,                               // file_name
                $filePath,                               // file_path
                $docInfo['document_number'] ?? null,     // document_number
                $docInfo['title'],                       // title
                $docInfo['sender'],                      // sender
                $docInfo['receiver'],                    // receiver
                $issueDate,                              // issue_date
                $docInfo['content_summary'] ?? null,     // content_summary
                $docInfo['notes'] ?? null,               // notes
                $site_id,                                // site_id
                $selected_category_id,                   // category_id
                $user_id,                                // uploaded_by
                'pending'                                // upload_status
            ];

            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $documentId = $pdo->lastInsertId();

            // 更新上傳狀態
            $updateSql = "UPDATE official_documents SET upload_status = ? WHERE id = ?";
            $updateStmt = $pdo->prepare($updateSql);
            $updateStmt->execute(['completed', $documentId]);

            $pdo->commit();

            // 發送通知
            try {
                $documentSite = '';
                if ($site_id) {
                    $siteQuery = $pdo->prepare("SELECT name FROM sites WHERE id = ?");
                    $siteQuery->execute([$site_id]);
                    $siteResult = $siteQuery->fetch(PDO::FETCH_ASSOC);
                    if ($siteResult) {
                        $documentSite = $siteResult['name'];
                    }
                }
                notifySiteAdminStaff($pdo, $documentSite, $fileName, $docInfo, $user_id, $notifySites);
            } catch (Exception $e) {
                error_log("通知發送失敗，但不影響文件上傳: " . $e->getMessage());
            }

            return ['success' => true, 'message' => '文件上傳成功', 'document_id' => $documentId];

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log("數據庫操作失敗: " . $e->getMessage());

            // 嘗試再次插入，使用最基本的信息
            try {
                $sql = "
                    INSERT INTO official_documents (
                        file_name, file_path,
                        title, sender, receiver,
                        issue_date, notes,
                        uploaded_by, uploaded_at, is_processed,
                        upload_status
                    ) VALUES (
                        ?, ?,
                        ?, ?, ?,
                        ?, ?,
                        ?, NOW(), 0,
                        ?
                    )
                ";

                $params = [
                    $fileName,                    // file_name
                    $filePath,                    // file_path
                    basename($fileName),          // title
                    '未提供發文單位',              // sender
                    '未提供受文者',                // receiver
                    date('Y-m-d'),               // issue_date
                    '文件處理過程中發生錯誤，需要手動更新資訊', // notes
                    $user_id,                     // uploaded_by
                    'error'                       // upload_status
                ];

                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $documentId = $pdo->lastInsertId();

                return ['success' => true, 'message' => '文件已上傳，但需要手動更新資訊', 'document_id' => $documentId];
            } catch (Exception $e2) {
                error_log("最終備用插入也失敗: " . $e2->getMessage());
                return ['success' => false, 'message' => '文件上傳失敗，請聯繫系統管理員'];
            }
        }
    } catch (Exception $e) {
        error_log("文件上傳處理過程中發生嚴重錯誤: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        return ['success' => false, 'message' => '處理過程中發生錯誤: ' . $e->getMessage()];
    }
}

/**
 * 向特定工地的行政人員發送公文上傳通知
 *
 * @param PDO $pdo 資料庫連接
 * @param string $site 工地名稱
 * @param string $fileName 檔案名稱
 * @param array $docInfo 文件資訊
 * @param int $uploaderId 上傳者ID
 * @param array $notifySites 要額外通知的工地列表
 */
function notifySiteAdminStaff($pdo, $site, $fileName, $docInfo, $uploaderId, $notifySites = []) {
    try {
        error_log("開始準備發送通知，原始工地: {$site}, 通知工地: " . json_encode($notifySites));

        // 獲取行政職位ID
        $positionQuery = $pdo->prepare("SELECT id FROM positions WHERE name = '行政'");
        $positionQuery->execute();
        $positionResult = $positionQuery->fetch(PDO::FETCH_ASSOC);

        if (!$positionResult) {
            error_log("找不到行政職位");
            return;
        }

        $adminPositionId = $positionResult['id'];

        // 查詢上傳者信息
        $uploaderQuery = $pdo->prepare("SELECT name, username, site FROM users WHERE id = ?");
        $uploaderQuery->execute([$uploaderId]);
        $uploaderInfo = $uploaderQuery->fetch(PDO::FETCH_ASSOC);
        $uploaderName = $uploaderInfo ? $uploaderInfo['name'] ?? $uploaderInfo['username'] : '未知用戶';
        $uploaderSite = $uploaderInfo ? $uploaderInfo['site'] : null;

        // 獲取文件標題或默認名稱
        $docTitle = $docInfo['title'] ?? $fileName;

        // 準備通知訊息
        $message = "新文件上傳通知：{$uploaderName} 上傳了一份新文件「{$docTitle}」";

        // 如果有發文單位和受文者，則添加到訊息中
        if (!empty($docInfo['sender'])) {
            $message .= "，發文單位：{$docInfo['sender']}";
        }

        if (!empty($docInfo['receiver'])) {
            $message .= "，受文者：{$docInfo['receiver']}";
        }

        if (!empty($docInfo['document_number'])) {
            $message .= "，文號：{$docInfo['document_number']}";
        }

        error_log("通知訊息: {$message}");

        // 處理的工地列表 - 首先添加默認工地
        $sitesToNotify = [];
        if (!empty($site)) {
            $sitesToNotify[] = $site;
        }

        // 添加用戶指定的額外工地
        if (!empty($notifySites) && is_array($notifySites)) {
            foreach ($notifySites as $notifySite) {
                $notifySite = trim($notifySite);
                if (!empty($notifySite) && !in_array($notifySite, $sitesToNotify)) {
                    $sitesToNotify[] = $notifySite;
                }
            }
        }

        error_log("將通知以下工地的行政人員: " . implode(', ', $sitesToNotify));

        // 為每個需要通知的工地發送通知
        foreach ($sitesToNotify as $currentSite) {
            // 查詢特定工地的行政人員
            $userQuery = $pdo->prepare("
                SELECT id, name, username FROM users
                WHERE position_id = ? AND site = ? AND id != ?
            ");
            $userQuery->execute([$adminPositionId, $currentSite, $uploaderId]);
            $adminUsers = $userQuery->fetchAll(PDO::FETCH_ASSOC);

            error_log("找到 " . count($adminUsers) . " 位工地 '{$currentSite}' 的行政人員");

            // 發送通知給工地行政人員
            if (count($adminUsers) > 0) {
                foreach ($adminUsers as $user) {
                    $userName = $user['name'] ?? $user['username'] ?? $user['id'];
                    $notifyStmt = $pdo->prepare("
                        INSERT INTO notifications (user_id, message, created_at)
                        VALUES (?, ?, NOW())
                    ");
                    $notifyStmt->execute([$user['id'], $message]);
                    error_log("已發送文件上傳通知給用戶: {$userName}（ID: {$user['id']}），工地: {$currentSite}");
                }
            } else {
                error_log("未找到工地 '{$currentSite}' 的行政人員，無法發送通知");
            }
        }

        // 不再自動通知總公司行政人員，只在用戶明確選擇時才通知
        // 在 notifySites 中明確包含總公司時，上面的循環已經處理了通知

        error_log("通知發送完成");
    } catch (Exception $e) {
        error_log("發送通知時發生錯誤: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    }
}

function analyzePDFContent($filePath) {
    // 調用 PDFProcessor 進行實際的PDF內容分析
    try {
        // 載入PDF處理類
        require_once 'pdf_processor.php';

        error_log("開始分析PDF: " . $filePath);

        // 載入 Gemini API 配置
        if (file_exists('gemini_config.php')) {
            // 強制重新載入配置文件，確保每次調用都使用新的API金鑰
            if (function_exists('opcache_invalidate')) {
                opcache_invalidate('gemini_config.php', true);
            }
            require_once 'gemini_config.php';
            error_log("Gemini API 配置已重新載入，使用金鑰輪換機制");
        } else {
            // 手動設置 Gemini API 選項
            PDFProcessor::setGeminiOptions([
                'enabled' => true,
                'api_key' => 'AIzaSyDgSD54jWdWw2v0nIbU425RCaLtDx_uy2k',
                'fallback_to_traditional' => true
            ]);
            error_log("Gemini API 選項已手動設置");
        }

        // 直接使用標準方法解析PDF
        $result = PDFProcessor::parsePDF($filePath);
        error_log("PDFProcessor::parsePDF結果: " . json_encode($result));

        // 初始化結果數組
        $docInfo = isset($result['data']) ? $result['data'] : [];

        // 如果解析失敗，嘗試直接提取文本並使用專門的提取函數
        if (empty($docInfo) || empty($docInfo['sender']) || empty($docInfo['receiver']) || empty($docInfo['issue_date'])) {
            error_log("PDF解析結果不完整，嘗試直接提取文本");

            // 提取文本內容
            $textContent = PDFProcessor::extractTextFromPDF($filePath);
            if (empty($textContent) || strlen(trim($textContent)) < 50) {
                error_log("直接提取文本失敗，嘗試OCR: " . $filePath);
                $textContent = PDFProcessor::performOCROnPDF($filePath);
            }

            error_log("PDF提取文本長度: " . strlen($textContent));

            // 使用專門的提取函數
            if (!empty($textContent)) {
                if (empty($docInfo['sender'])) {
                    $docInfo['sender'] = PDFProcessor::extractSender($textContent);
                    error_log("直接提取發文單位: " . $docInfo['sender']);
                }

                if (empty($docInfo['receiver'])) {
                    $docInfo['receiver'] = PDFProcessor::extractReceiver($textContent);
                    error_log("直接提取受文者: " . $docInfo['receiver']);
                }

                if (empty($docInfo['issue_date'])) {
                    $docInfo['issue_date'] = PDFProcessor::extractIssueDate($textContent);
                    error_log("直接提取發文日期: " . $docInfo['issue_date']);
                }

                if (empty($docInfo['document_number'])) {
                    $docInfo['document_number'] = PDFProcessor::extractDocumentNumber($textContent, $filePath);
                    error_log("直接提取文號: " . $docInfo['document_number']);
                }

                if (empty($docInfo['title'])) {
                    $docInfo['title'] = PDFProcessor::extractTitle($textContent);
                    error_log("直接提取標題: " . $docInfo['title']);
                }

                if (empty($docInfo['summary'])) {
                    $docInfo['content_summary'] = PDFProcessor::extractSummary($textContent);
                    error_log("直接提取內容摘要: " . $docInfo['content_summary']);
                }
            }
        }

        // 從檔名提取補充信息
        $fileName = basename($filePath);
        $fileNameInfo = PDFProcessor::extractInfoFromFilename($fileName);

        // 補充從文件名獲取的信息
        if (!empty($fileNameInfo)) {
            if (empty($docInfo['document_number']) && !empty($fileNameInfo['document_number'])) {
                $docInfo['document_number'] = $fileNameInfo['document_number'];
                error_log("從文件名提取文號: " . $docInfo['document_number']);
            }

            if (empty($docInfo['sender']) && !empty($fileNameInfo['sender'])) {
                $docInfo['sender'] = $fileNameInfo['sender'];
                error_log("從文件名提取發文單位: " . $docInfo['sender']);
            }

            if (empty($docInfo['issue_date']) && !empty($fileNameInfo['date'])) {
                $docInfo['issue_date'] = $fileNameInfo['date'];
                error_log("從文件名提取發文日期: " . $docInfo['issue_date']);
            }

            if (empty($docInfo['site']) && !empty($fileNameInfo['site'])) {
                $docInfo['site'] = $fileNameInfo['site'];
                error_log("從文件名提取工地: " . $docInfo['site']);
            }
        }

        // 特殊處理：如果有"復貴公司"的說明，但沒有提取到受文者，則再次嘗試提取
        if (empty($docInfo['receiver']) && isset($textContent) && !empty($textContent)) {
            if (mb_strpos($textContent, '復貴公司') !== false ||
                preg_match('/主旨.*?[「『].*?營造股份有限公司.*?[」』]/us', $textContent) ||
                mb_strpos($textContent, '良有營造股份有限公司') !== false) {

                // 再次嘗試提取受文者
                $docInfo['receiver'] = PDFProcessor::extractReceiver($textContent);
                error_log("特殊處理提取受文者: " . $docInfo['receiver']);

                // 如果仍然未提取到但確實包含"良有營造"，強制設置
                if (empty($docInfo['receiver']) && mb_strpos($textContent, '良有營造') !== false) {
                    $docInfo['receiver'] = '良有營造股份有限公司';
                    error_log("強制設置受文者為良有營造股份有限公司");
                }
            }
        }

        // 對提取的發文單位和受文者進行額外清理
        if (!empty($docInfo['sender'])) {
            // 清除前導的點符號和其他雜質
            $docInfo['sender'] = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $docInfo['sender']);
            $docInfo['sender'] = trim($docInfo['sender']);
            error_log("清理後的發文單位: " . $docInfo['sender']);
        }

        if (!empty($docInfo['receiver'])) {
            // 清除前導的點符號和其他雜質
            $docInfo['receiver'] = preg_replace('/^\s*[.．。、,，;；]\s*/', '', $docInfo['receiver']);
            $docInfo['receiver'] = trim($docInfo['receiver']);
            error_log("清理後的受文者: " . $docInfo['receiver']);
        }

        // 確保標題不為空
        if (empty($docInfo['title'])) {
            $docInfo['title'] = basename($filePath, '.pdf');
        }

        // 處理日期格式
        if (!empty($docInfo['issue_date'])) {
            try {
                $date = new DateTime($docInfo['issue_date']);
                $docInfo['issue_date'] = $date->format('Y-m-d');
            } catch (Exception $e) {
                error_log("日期格式錯誤: " . $docInfo['issue_date'] . ", 使用當前日期代替");
                $docInfo['issue_date'] = date('Y-m-d');
            }
        } else {
            $docInfo['issue_date'] = date('Y-m-d'); // 默認使用當前日期
        }

        // 確保內容摘要字段存在
        if (empty($docInfo['content_summary']) && !empty($docInfo['summary'])) {
            $docInfo['content_summary'] = $docInfo['summary'];
        }

        // 最後記錄結果
        error_log("最終PDF解析結果: " . json_encode($docInfo));

        return $docInfo;
    } catch (Exception $e) {
        error_log("PDF內容分析錯誤: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    return [
        'document_number' => null,
            'title' => basename($filePath),
            'sender' => null,
            'receiver' => null,
            'issue_date' => date('Y-m-d'),
            'content_summary' => null,
            'notes' => "PDF分析發生錯誤: " . $e->getMessage(),
            'site' => null
        ];
    }
}

function determineSiteFromContent($docInfo, $pdo) {
    // 使用docInfo中的site字段確定工地
    if (!empty($docInfo['site'])) {
        try {
            $query = "SELECT id FROM sites WHERE name LIKE ?";
            $stmt = $pdo->prepare($query);
            $siteName = '%' . $docInfo['site'] . '%';
            $stmt->execute([$siteName]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                error_log("根據內容找到工地ID: " . $result['id'] . " 對應關鍵字: " . $docInfo['site']);
                return $result['id'];
            }
        } catch (PDOException $e) {
            error_log("查詢工地發生錯誤: " . $e->getMessage());
        }
    }

    // 如果沒有從內容確定工地，返回默認工地ID
    return 1; // 默認工地ID
}

/**
 * 生成分頁URL
 */
function paginationUrl($page, $per_page = null) {
    $params = $_GET;
    $params['page'] = $page;
    if ($per_page !== null) {
        $params['per_page'] = $per_page;
    }
    return '?' . http_build_query($params);
}

// 在 document_manager.php 中增加對失敗文件的重試機制
function retryFailedDocuments($failedFiles, $maxRetries = 3) {
    foreach ($failedFiles as $file) {
        for ($i = 0; $i < $maxRetries; $i++) {
            error_log("重試處理文件 (第" . ($i+1) . "次): " . $file);
            $result = analyzePDFContent($file);
            if ($result && !empty($result['document_number'])) {
                error_log("重試成功: " . $file);
                break;
            }
            // 等待一段時間再重試
            sleep(2);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>良有營造電子簽核系統 - 公文整理系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #9c27b0;
            --info-hover: #7b1fa2;
            --dark-bg: #f5f5f5;
            --card-bg: #ffffff;
            --card-header: #f8f9fa;
            --table-header: #f8f9fa;
            --table-row-hover: #f5f5f5;
            --border-color: #e0e0e0;
            --text-color: #333333;
            --text-secondary: #666666;
            --success-badge: rgba(52, 168, 83, 0.15);
            --warning-badge: rgba(251, 188, 5, 0.15);
            --danger-badge: rgba(234, 67, 53, 0.15);
            --info-badge: rgba(26, 115, 232, 0.15);
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        body.theme-dark {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #9c27b0;
            --info-hover: #7b1fa2;
            --dark-bg: #121212;
            --card-bg: #1e1e1e;
            --card-header: #252525;
            --table-header: #2c2c2c;
            --table-row-hover: #2a2a2a;
            --border-color: #333;
            --text-color: #ffffff;
            --text-secondary: #aaaaaa;
            --success-badge: rgba(52, 168, 83, 0.15);
            --warning-badge: rgba(251, 188, 5, 0.15);
            --danger-badge: rgba(234, 67, 53, 0.15);
            --info-badge: rgba(26, 115, 232, 0.15);
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

/* PDF.js 閱讀器樣式 */
#pdfToolbar {
    background: var(--card-header) !important;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

#pdfToolbar button {
    margin: 0 3px;
    padding: 2px 8px;
    border-radius: 3px;
    border: none;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.2s;
}

#pdfToolbar button:hover {
    background: var(--primary-hover);
}

#pdfViewer {
    background-color: var(--dark-bg);
    padding: 15px;
    overflow: auto;
}

#pdfViewer canvas {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.user-info i.fa-user-circle {
    font-size: 24px;
    color: #2196F3; /* 藍色 - 您可以根據需要調整此顏色代碼 */
    margin-right: 6px;
}

.user-info-details {
    display: flex;
    flex-direction: column;
}

.user-info-details .user-name {
    color: #fff; /* 用戶名稱為白色 */
    font-weight: 500;
    font-size: 14px;
}

.user-info-details .user-position {
    color: #34a853; /* 職位名稱為綠色 */
    font-size: 12px;
}

.user-info-details .user-site {
    color: rgba(255, 255, 255, 0.7); /* 工地名稱為半透明白色 */
    font-size: 12px;
}

/* PDF 預覽器樣式 */
.pdf-controls {
    background: var(--card-header);
    padding: 10px;
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid var(--border-color);
}


/* PDF內建預覽器樣式 */
.document-preview {
    min-height: 650px;  /* 從750px調降至650px */
    border: 1px solid var(--border-color);
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.document-preview iframe {
    width: 100%;
    height: 100%;
    min-height: 650px;  /* 從750px調降至650px */
    border: none;
}

.pdf-download-bar {
    padding: 10px;
    text-align: center;
    background-color: var(--card-header);
    border-top: 1px solid var(--border-color);
}

.pdf-download-bar .btn {
    min-width: 150px;
}
.pdf-controls button {
    margin: 0 3px;
    padding: 5px 8px;
    border-radius: 3px;
    border: none;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.2s;
}

.pdf-controls button:hover {
    background: var(--primary-hover);
}

.pdf-canvas-container {
    display: flex;
    justify-content: center;
    padding: 15px;
    background-color: var(--dark-bg);
    min-height: 700px;
    overflow: auto;
}

.pdf-page-info {
    margin: 0 10px;
    display: flex;
    align-items: center;
}

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft JhengHei', sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .navbar {
            background: var(--card-bg);
            padding: 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn-danger {
    background-color: var(--danger-color);
    color: white;
    font-weight: 500;
}

.btn-danger:hover {
    background-color: var(--danger-hover);
}

/* 批量刪除按鈕特殊樣式 */
#batchDeleteBtn {
    margin-right: 10px;
    display: inline-flex;
    align-items: center;
}

#batchDeleteBtn i {
    margin-right: 5px;
}

/* 優化表格對齊 */
.document-table {
    table-layout: fixed;
    width: 100%;
}

.document-table th, .document-table td {
    padding: 12px 10px;
    vertical-align: middle;
}

/* 複選框列樣式 */
.document-table th:first-child, .document-table td:first-child {
    width: 40px;
    text-align: center;
    padding: 0;
}

/* 複選框樣式 */
.doc-checkbox, #selectAll {
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0 auto;
    display: block;
}

/* 操作按鈕列樣式 */
.document-table th:last-child, .document-table td:last-child {
    width: 220px;
    text-align: center;
}

/* 統一所有操作按鈕的大小與風格 */
.action-buttons .btn {
    width: 80px;
    height: 34px;
    padding: 6px 0;
    font-size: 14px;
    margin: 3px 1px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.2s;
}

/* 查看按鈕 - 藍色 */
.btn-view {
    background-color: var(--primary-color);
    color: var(--navbar-text);
}

.btn-view:hover {
    background-color: var(--primary-hover);
}

/* 編輯按鈕 - 綠色 */
.btn-edit {
    background-color: var(--secondary-color);
    color: var(--navbar-text);
}

.btn-edit:hover {
    background-color: var(--secondary-hover);
}

/* 下載按鈕 - 紫色 */
.btn-download {
    background-color: var(--info-color);
    color: var(--navbar-text);
}

.btn-download:hover {
    background-color: var(--info-hover);
}

/* 刪除按鈕 - 紅色 */
.btn-delete {
    background-color: #ea4335;
    color: white;
}

.btn-delete:hover {
    background-color: #c5221f;
}

/* 改善列寬分配 */
.document-table th:nth-child(2) {
    width: 15%; /* 發文字號 */
}

.document-table th:nth-child(3) {
    width: 10%; /* 發文日期 */
}

.document-table th:nth-child(4) {
    width: 20%; /* 主旨 */
}

.document-table th:nth-child(5) {
    width: 20%; /* 說明 */
}

.document-table th:nth-child(6) {
    width: 10%; /* 工地/分類 */
}

.document-table th:nth-child(7) {
    width: 8%; /* 上傳者 */
}

/* PDF預覽改進樣式 */
.pdf-controls {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: var(--card-header);
    border-bottom: 1px solid var(--border-color);
}

.pdf-controls button {
    margin: 0 5px;
    padding: 5px 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.pdf-controls button i {
    margin-right: 5px;
}

.pdf-controls button.active {
    background-color: var(--secondary-color);
}

.pdf-page-wrapper {
    margin-bottom: 20px;
    position: relative;
}

.pdf-content-display {
    background-color: var(--dark-bg);
}

.pdf-sidebar {
    scrollbar-width: thin;
}

.pdf-sidebar::-webkit-scrollbar {
    width: 6px;
}

.pdf-sidebar::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

.pdf-thumbnail.active {
    border-color: var(--primary-color);
}


.doc-checkbox, #selectAll {
    width: 18px;
    height: 18px;
    cursor: pointer;
}
        .navbar-brand {
            display: flex;
            align-items: center;
            padding: 10px 15px;
        }

        .navbar-brand img {
            height: 40px;
            margin-right: 10px;
            border-radius: 50%;
        }

        .navbar-brand h1 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: var(--text-color);
        }

        .navbar-nav {
            display: flex;
            list-style: none;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            color: var(--text-color);
            text-decoration: none;
            padding: 15px;
            transition: all 0.3s;
        }

        .nav-link:hover {
            background-color: var(--navbar-hover);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 16px;
        }

.main-content {
    margin-top: 60px; /* 從70px改為60px，與header高度一致 */
    padding: 30px;
}

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
        }

        .dashboard-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: #2a2a2a;
            color: var(--text-color);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }

        .btn i {
            margin-right: 5px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .btn-success {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--secondary-hover);
        }

        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .alert-success {
            background-color: rgba(52, 168, 83, 0.1);
            border-left: 4px solid #34a853;
            color: #34a853;
        }

        .alert-danger {
            background-color: rgba(234, 67, 53, 0.1);
            border-left: 4px solid #ea4335;
            color: #ea4335;
        }

        .alert i {
            margin-right: 10px;
            font-size: 20px;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .document-table {
            width: 100%;
            border-collapse: collapse;
        }

        .document-table th {
            background-color: var(--table-header);
            color: var(--text-color);
            padding: 12px 15px;
            font-weight: 600;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .document-table td {
            padding: 12px 15px;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
        }

        .document-table tr:hover {
            background-color: var(--table-row-hover);
        }

        .document-title {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }

        .document-title:hover {
            color: var(--primary-hover);
            text-decoration: underline;
        }

        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .tag-site {
            background-color: rgba(26, 115, 232, 0.1);
            color: #1a73e8;
        }

        .tag-category {
            background-color: rgba(52, 168, 83, 0.1);
            color: #34a853;
        }

        .search-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .search-form .form-group {
            margin-bottom: 0;
            flex: 1;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .drop-area {
            border: 2px dashed var(--border-color);
            border-radius: 4px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .drop-area:hover, .drop-area.drag-over {
            border-color: var(--primary-color);
            background-color: rgba(26, 115, 232, 0.05);
        }

        .drop-area i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 15px;
        }

        .drop-area p {
            margin: 5px 0;
            color: var(--text-secondary);
        }

        .file-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-icon {
            font-size: 18px;
            margin-right: 10px;
            color: var(--text-secondary);
        }

        .file-name {
            flex: 1;
        }

        .file-remove {
            color: var(--danger-color);
            cursor: pointer;
            font-size: 18px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

.modal-content {
    background-color: var(--card-bg);
    margin: 0 auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 1600px;
    max-height: 98vh;
    min-height: 85vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* 新增以下樣式來優化PDF預覽視窗 */
#viewDocumentModal .modal-content {
    padding-top: 10px;
}

#viewDocumentModal .tabs {
    margin-top: 10px;
    margin-bottom: 15px;
}

#viewDocumentModal h2 {
    margin-top: 0;
    margin-bottom: 10px;
}

#previewTab {
    padding-top: 0;
}
        .close-modal {
            color: var(--text-secondary);
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-modal:hover {
            color: var(--text-color);
        }

.document-preview {
    min-height: 600px;
    border: 1px solid var(--border-color);
    margin-top: 10px;  /* 從20px減少到10px */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.document-preview iframe {
    width: 100%;
    height: 100%;
    min-height: 750px;
    border: none;
}

/* 添加PDF.js工具列優化樣式 */
#editDocumentModal .document-preview {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

#editDocumentModal .document-preview iframe {
    width: 100%;
    height: 100%;
    min-height: 750px;
    border: none;
}

/* 確保PDF.js工具列在iframe中可見 */
#editPdfPreview {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* 處理PDF.js工具列在iframe中的顯示問題 */
#toolbarContainer, #toolbarViewer, #toolbarViewerMiddle {
    height: auto !important;
    min-height: 32px !important;
    display: flex !important;
    align-items: center !important;
}

/* 調整頁頭區域，移除底部間距 */
header {
  margin-bottom: 0;
}

/* 調整主內容區的頂部間距，與頁頭高度一致 */
.main-content {
  margin-top: 60px; /* 可依據實際頁頭高度調整此值 */
}

/* 若空白仍然存在，添加以下代碼 */
body {
  padding-top: 0;
  overflow-x: hidden;
}

/* 新增或修改此處樣式，放在<style>標籤內 */
.modal-footer {
    margin-top: 15px;
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
    background-color: var(--card-bg);
    position: sticky;
    bottom: 0;
    z-index: 10;
}

        /* 分頁導覽樣式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 10px 15px;
            border-top: 1px solid var(--border-color);
            background-color: var(--card-bg);
            border-radius: 0 0 8px 8px;
        }

        .pagination-info {
            color: var(--text-color);
            font-size: 14px;
        }

        .pagination {
            display: flex;
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pagination li {
            margin: 0 2px;
        }

        .pagination li a, .pagination li span {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            color: var(--text-color);
            background-color: var(--card-header);
            transition: all 0.2s;
            border: 1px solid var(--border-color);
        }

        .pagination li a:hover {
            background-color: var(--primary-hover);
            color: #fff;
        }

        .pagination li.active span {
            background-color: var(--primary-color);
            color: #fff;
            font-weight: 600;
            border-color: var(--primary-color);
        }

        .pagination li.disabled span {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .per-page-select {
            display: flex;
            align-items: center;
            margin-left: 15px;
            padding: 3px 8px;
            border-radius: 4px;
            background-color: var(--card-header);
        }

        .per-page-select label {
            margin-right: 8px;
            color: var(--text-color);
            font-size: 14px;
        }

        .per-page-select select {
            padding: 5px 10px;
            border-radius: 4px;
            background-color: var(--card-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .per-page-select select:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        /* 加強深色模式下表格底部樣式 */
        body.theme-dark .pagination-container {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .pagination-container {
                flex-direction: column;
                gap: 10px;
            }

            .pagination {
                order: 2;
            }

            .pagination-info {
                order: 1;
            }

            .per-page-select {
                order: 3;
                margin-left: 0;
                margin-top: 10px;
            }
        }


        .navbar {
    background: var(--navbar-bg);
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    height: 36px;
    margin-right: 10px;
}

.navbar-brand h1 {
    font-size: 16px;
    margin: 0;
    color: var(--navbar-text);
    font-weight: 600;
}

.navbar-links {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.nav-link {
    display: flex;
    align-items: center;
    color: var(--navbar-text);
    padding: 0 15px;
    height: 60px;
    text-decoration: none;
    transition: background-color 0.2s;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: var(--navbar-hover);
}

.nav-link i {
    margin-right: 7px;
}

.navbar-actions {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    margin-left: 15px;
    color: var(--navbar-text);
}

.user-avatar {
    margin-right: 8px;
    font-size: 20px;
}

.user-name {
    font-weight: 500;
    margin-right: 5px;
}

.user-company {
    color: var(--text-secondary);
    font-size: 13px;
}


/* 頂部導航樣式 */
header {
    display: flex;
    align-items: center;
    background-color: var(--navbar-bg);
    padding: 0 20px;
    height: 60px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    margin-bottom: 0;
    padding-bottom: 0;
    justify-content: space-between;
}

.logo-area {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo-link img {
    height: 36px;
    margin-right: 10px;
}

.logo-link span {
    color: var(--navbar-text);
    font-size: 16px;
    font-weight: 600;
}

.main-nav {
    display: flex;
    align-items: center;
    margin-left: 20px; /* 減少左邊距 */
    gap: 5px; /* 添加間隔控制 */
}

.nav-item {
    display: flex;
    align-items: center;
    color: var(--navbar-text);
    padding: 0 12px; /* 減少左右內邊距 */
    height: 60px;
    text-decoration: none;
    transition: background-color 0.2s;
    white-space: nowrap; /* 防止文字換行 */
}

.nav-item:hover {
    background-color: var(--navbar-hover);
}

.nav-item.active {
    background-color: var(--navbar-hover);
}

.nav-item i {
    margin-right: 7px;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 60px;
    left: 0;
    background-color: var(--card-bg);
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    display: none;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: block;
    color: var(--text-color);
    padding: 12px 15px;
    text-decoration: none;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: var(--table-row-hover);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 0; /* 移除底部間距 */
    padding-bottom: 0; /* 移除底部內邊距 */
    margin-left: auto;
}

.user-info {
    display: flex;
    align-items: center;
    margin-left: 15px;
    color: var(--navbar-text);
}

.user-info i {
    font-size: 20px;
    margin-right: 8px;
}

.user-info span {
    font-weight: 500;
    margin-right: 5px;
}

.user-info small {
    color: var(--text-secondary);
    font-size: 12px;
}

/* 調整頁頭區域，移除底部間距 */
header {
    margin-bottom: 0;
}

/* 確保header與main-content之間沒有間隙 */
header + .main-content {
    margin-top: 60px;
}

/* 移除header與main-content之間可能存在的空白 */
header + * + .main-content {
    margin-top: 60px;
}

.notification-icon {
    position: relative;
    margin-left: 15px;
    cursor: pointer;
    display: inline-block;
}

.notification-icon i.fa-bell {
    color: var(--navbar-text);
    font-size: 18px;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
}

.notification-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    width: 300px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    margin-top: 10px;
}

#notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

#notifications-list::-webkit-scrollbar {
    width: 6px;
}

#notifications-list::-webkit-scrollbar-track {
    background: var(--card-bg);
}

#notifications-list::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

/* 用戶圖標樣式 */
.user-info i.fa-user-circle {
    font-size: 20px;
    color: var(--text-color);  /* 使用主題文本顏色變量替代固定顏色 #2196F3 */
    margin-right: 8px;
}

.user-info {
    display: flex;
    align-items: center;
    position: relative;
}

.user-info-details {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: bold;
    color: var(--text-color);  /* 使用主題文本顏色變量替代固定顏色 #fff */
}

.user-position, .user-site {
    font-size: 12px;
    color: var(--text-secondary);  /* 使用次要文本顏色變量 */
}

/* 通知圖標樣式 */
.notification-icon {
    position: relative;
    display: inline-block;
    margin-left: 15px;
    cursor: pointer;
}

.notification-icon i.fa-bell {
    color: var(--text-color);  /* 使用主題文本顏色變量替代固定顏色 #fff */
    font-size: 18px;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);  /* 使用危險顏色變量替代固定顏色 #f44336 */
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
}

#notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    margin-top: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

#notifications-dropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#notifications-dropdown li {
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 13px;
}

#notifications-dropdown li:last-child {
    border-bottom: none;
}

#notifications-dropdown li:hover {
    background-color: var(--table-row-hover);
}

/* 通知圖標和徽章樣式 */
.notification-icon {
    position: relative;
    margin-left: 15px;
    cursor: pointer;
}

.notification-icon i.fa-bell {
    color: #fff;
    font-size: 18px;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #f44336;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
}

/* 通知下拉選單樣式 */
#notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: #2c2c2c;
    border: 1px solid #444;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    margin-top: 10px;
    display: none;
    z-index: 1000;
}

.notification-header {
    padding: 12px 15px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
}

#mark-read-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#mark-read-btn:hover {
    background-color: var(--primary-hover);
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: var(--info-badge);
}

.notification-item.read {
    background-color: transparent;
}

.notification-content {
    color: var(--text-color);
    font-size: 13px;
    margin-bottom: 5px;
}

.notification-time {
    color: var(--text-secondary);
    font-size: 11px;
}

/* 滾動條樣式 */
.notification-list::-webkit-scrollbar {
    width: 6px;
}

.notification-list::-webkit-scrollbar-track {
    background: var(--card-bg);
}

.notification-list::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}
.user-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 0; /* 移除底部間距 */
    padding-bottom: 0; /* 移除底部內邊距 */
    margin-left: auto;
}

header {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* 響應式表格樣式 */
.table-responsive {
    overflow-x: auto;
    background-color: var(--card-bg);
    border-radius: 8px 8px 0 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

body.theme-dark .table-responsive {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.document-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-bg);
    overflow: hidden;
    margin-bottom: 0; /* 移除底部間距 */
}

/* 設置表格與分頁容器組合的樣式 */
.document-container {
    margin-bottom: 30px;
    overflow: hidden;
    border-radius: 8px;
    background-color: var(--card-bg);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

body.theme-dark .document-container {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

/* 確保分頁容器與表格連接 */
.table-responsive + .pagination-container,
.document-container .pagination-container {
    margin-top: 0;
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 8px 8px;
    background-color: var(--card-bg);
    padding: 10px 15px;
}

/* 分頁導覽樣式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--card-bg);
    border-radius: 0 0 8px 8px;
}

.pagination-info {
    color: var(--text-color);
    font-size: 14px;
}

.per-page-select {
    display: flex;
    align-items: center;
    margin-left: 15px;
    padding: 3px 8px;
    border-radius: 4px;
    background-color: var(--card-header);
}

.per-page-select label {
    margin-right: 8px;
    color: var(--text-color);
    font-size: 14px;
}

.per-page-select select {
    padding: 5px 10px;
    border-radius: 4px;
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.per-page-select select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* 加強深色模式下表格底部樣式 */
body.theme-dark .pagination-container {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
    }

    .pagination {
        order: 2;
    }

    .pagination-info {
        order: 1;
    }

    .per-page-select {
        order: 3;
        margin-left: 0;
        margin-top: 10px;
    }
}
    </style>
    <link rel="stylesheet" href="light-theme.css">
    <script src="theme-switcher.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.6.172/pdf.min.js"></script>

    <!-- 添加響應式樣式，優化移動設備上的公文列表顯示 -->
    <style>
        /* 響應式樣式 - 僅在移動設備上生效 */
        @media (max-width: 767px) {
            /* 表格容器在移動設備上的顯示調整 */
            .table-responsive {
                border: none;
                box-shadow: none;
                padding: 0;
            }

            /* 修改表格顯示為卡片式布局 */
            .document-table {
                width: 100%;
            }

            /* 隱藏表頭 */
            .document-table thead {
                display: none;
            }

            /* 修改表格行為卡片式顯示 */
            .document-table tbody tr {
                display: block;
                margin-bottom: 15px;
                border: 1px solid var(--border-color);
                border-radius: 8px;
                background-color: var(--card-bg);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                position: relative;
            }

            /* 修改表格單元格顯示 */
            .document-table tbody td {
                display: block;
                text-align: left;
                padding: 8px 12px;
                position: relative;
                border-bottom: 1px solid var(--border-color);
                min-height: 24px;
            }

            /* 最後一個單元格無底部邊框 */
            .document-table tbody td:last-child {
                border-bottom: none;
            }

            /* 添加標簽顯示 */
            .document-table tbody td::before {
                content: attr(data-label);
                display: inline-block;
                font-weight: 600;
                min-width: 90px;
                margin-right: 10px;
                color: var(--text-secondary);
            }

            /* 優化checkbox在移動設備上的顯示 */
            .document-table td:first-child {
                padding: 10px;
                text-align: right;
                position: absolute;
                top: 0;
                right: 0;
                width: auto;
                border-bottom: none;
                background-color: transparent;
                z-index: 2;
            }

            /* 移除checkbox的標籤顯示 */
            .document-table td:first-child::before {
                display: none;
            }

            /* 優化文檔標題顯示 */
            .document-table .document-title {
                font-size: 16px;
                display: block;
                margin-bottom: 4px;
                font-weight: 600;
            }

            /* 優化標籤顯示 */
            .tag {
                display: inline-block;
                margin: 2px;
                padding: 3px 6px;
                font-size: 11px;
                border-radius: 4px;
            }

            /* 優化操作按鈕區域 */
            .document-table td:last-child {
                padding: 12px;
                text-align: center;
                background-color: var(--card-header);
                border-radius: 0 0 8px 8px;
            }

            /* 移除操作按鈕的標籤 */
            .document-table td:last-child::before {
                display: none;
            }

            /* 調整操作按鈕樣式 */
            .action-buttons {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 8px;
            }

            .action-buttons .btn {
                min-width: 70px;
                font-size: 12px;
                padding: 6px 10px;
                margin: 0;
            }

            /* 優化分頁控制區域 */
            .pagination-container {
                flex-direction: column;
                align-items: center;
                gap: 15px;
                padding: 15px 10px;
            }

            .pagination {
                order: 2;
            }

            .pagination-info {
                order: 1;
                text-align: center;
                font-size: 13px;
            }

            .per-page-select {
                order: 3;
                margin-left: 0;
                width: 100%;
                display: flex;
                justify-content: center;
            }

            /* 優化分頁按鈕 */
            .pagination li a, .pagination li span {
                width: 32px;
                height: 32px;
                font-size: 13px;
            }

            /* 調整每頁顯示選項器 */
            .per-page-select select {
                max-width: 120px;
            }

            /* 搜索表單優化 */
            .search-form {
                flex-direction: column;
                gap: 15px;
            }

            .search-form .form-group {
                width: 100%;
            }

            .search-form button {
                width: 100%;
                padding: 10px;
                margin-top: 5px;
            }

            /* 頁面標題調整 */
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
                margin-bottom: 20px;
            }

            .page-header > div {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                width: 100%;
            }

            .page-header .btn {
                flex: 1;
                min-width: 120px;
                padding: 8px 5px;
                font-size: 13px;
                text-align: center;
            }

            /* 卡片內容調整 */
            .card-body {
                padding: 15px 10px;
            }

            /* 優化主內容區域間距 */
            .main-content {
                padding: 15px 10px;
            }

            /* 優化模態視窗 */
            .modal-content {
                width: 95%;
                margin: 5% auto;
                padding: 15px;
                max-height: 90vh;
            }

            /* 導覽列移動裝置優化 */
            header {
                height: auto;
                min-height: 60px;
                flex-wrap: wrap;
                padding: 0;
                position: relative;
            }

            .logo-area {
                padding: 10px 15px;
                width: calc(100% - 120px);
                z-index: 2;
            }

            .logo-link span {
                font-size: 14px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .logo-link img {
                height: 32px;
                margin-right: 8px;
            }

            /* 漢堡選單按鈕 */
            .mobile-menu-toggle {
                display: block;
                position: absolute;
                right: 15px;
                top: 15px;
                width: 40px;
                height: 40px;
                background: transparent;
                border: none;
                color: var(--navbar-text);
                font-size: 22px;
                padding: 0;
                z-index: 3;
                cursor: pointer;
            }

            /* 導覽選單 */
            .main-nav {
                display: none;
                width: 100%;
                order: 3;
                margin: 0;
                padding: 0;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .main-nav.active {
                display: flex;
                flex-direction: column;
            }

            .main-nav .nav-item {
                width: 100%;
                padding: 12px 15px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            /* 用戶操作區域 */
            .user-actions {
                display: none;
                width: 100%;
                order: 4;
                flex-direction: column;
                gap: 5px;
                margin: 0;
                padding: 10px 15px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .user-actions.active {
                display: flex;
            }

            .user-actions .theme-toggle {
                width: 100%;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                padding: 10px;
                color: var(--navbar-text);
                background-color: transparent;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                margin-bottom: 10px;
            }

            /* 在移動設備上隱藏主題切換按鈕和登出按鈕中的文字，只顯示圖標 */
            .user-actions .theme-toggle span,
            .user-actions .nav-item.logout .logout-text,
            .user-actions .notification-icon .notification-text {
                display: none;
            }

            /* 調整圖標大小和位置 */
            .user-actions .theme-toggle i,
            .user-actions .nav-item.logout i {
                font-size: 18px;
                margin-right: 0;
            }

            .user-actions .nav-item.logout {
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 10px;
                margin-top: 5px;
                border: 1px solid rgba(234, 67, 53, 0.3);
                border-radius: 4px;
            }

            /* 用戶信息 */
            .user-info {
                width: 100%;
                padding: 10px;
                margin: 0;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
            }

            /* 通知圖標 */
            .notification-icon {
                margin: 10px 0;
                align-self: flex-start;
                padding: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                width: 100%;
                display: flex;
                align-items: center;
            }

            .notification-icon i {
                margin-right: 5px;
            }

            /* 通知下拉選單 */
            .notification-dropdown {
                position: fixed;
                top: 60px;
                left: 0;
                width: 100%;
                max-width: 100%;
                height: auto;
                max-height: 80vh;
                margin-top: 0;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                z-index: 1000;
            }
        }
    </style>
    <script>
        // 全局函數：調整PDF查看器工具列，確保完整顯示
        function fixPdfViewerToolbar(iframeId) {
        // 確保登出按鈕在任何模式下顯示為紅色
        document.addEventListener('DOMContentLoaded', function() {
            // 選擇所有登出按鈕和圖示，無論當前主題
            const logoutButtons = document.querySelectorAll('.logout, .logout i');
            logoutButtons.forEach(button => {
                button.style.setProperty('color', '#ea4335', 'important');
            });
            console.log('已直接設置登出按鈕為紅色');

            // 監聽主題變化
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'class' && mutation.target === document.body) {
                        // 主題變化後，無論是何種主題，都設置登出按鈕為紅色
                        const logoutButtons = document.querySelectorAll('.logout, .logout i');
                        logoutButtons.forEach(button => {
                            button.style.setProperty('color', '#ea4335', 'important');
                        });
                        console.log('主題變化：已設置登出按鈕為紅色');
                    }
                });
            });

            // 開始觀察body的class變化
            observer.observe(document.body, { attributes: true });

            // 強制設置暗黑模式下的導覽列顏色
            function forceNavbarColor() {
                const isDarkMode = document.body.classList.contains('theme-dark');
                const isLightMode = document.body.classList.contains('theme-light');
                const navbar = document.querySelector('.navbar') || document.querySelector('header');

                if (navbar) {
                    if (isDarkMode) {
                        // 僅在暗黑模式下設置顏色為 #1e1e1e
                        navbar.style.backgroundColor = '#1e1e1e';
                        navbar.style.setProperty('background-color', '#1e1e1e', 'important');
                        console.log('強制設置暗黑模式下導覽列顏色為 #1e1e1e');

                        // 確保所有導覽連結為白色（除了登出按鈕）
                        const navLinks = navbar.querySelectorAll('a:not(.logout), .nav-item:not(.logout)');
                        navLinks.forEach(link => {
                            link.style.setProperty('color', '#ffffff', 'important');
                        });
                    }

                    if (isLightMode) {
                        // 確保淺色模式下導覽列為藍色
                        navbar.style.backgroundColor = '#4361EE';
                        navbar.style.setProperty('background-color', '#4361EE', 'important');
                        console.log('強制設置淺色模式下導覽列顏色為 #4361EE（藍色）');

                        // 確保所有導覽連結為白色（除了登出按鈕）
                        const navLinks = navbar.querySelectorAll('a:not(.logout), .nav-item:not(.logout)');
                        navLinks.forEach(link => {
                            link.style.setProperty('color', '#ffffff', 'important');
                        });
                    }

                    // 無論什麼模式，登出按鈕都是紅色
                    const logoutLinks = navbar.querySelectorAll('a.logout, .nav-item.logout, a.logout i, .nav-item.logout i');
                    logoutLinks.forEach(link => {
                        link.style.setProperty('color', '#ea4335', 'important');
                    });
                }
            }

            // 立即執行一次
            forceNavbarColor();

            // 然後每100毫秒檢查一次，持續1秒鐘
            let checkCount = 0;
            const intervalId = setInterval(() => {
                forceNavbarColor();
                checkCount++;
                if (checkCount >= 10) {
                    clearInterval(intervalId);
                }
            }, 100);
        });
    </script>
    <script>
        // 初始化主題
        document.addEventListener('DOMContentLoaded', initTheme);

        // 清除篩選條件函數
        function clearFilters() {
            // 重置發文單位選擇
            document.querySelector('select[name="sender"]').selectedIndex = 0;

            // 重置日期範圍
            document.querySelector('input[name="start_date"]').value = '';
            document.querySelector('input[name="end_date"]').value = '';

            // 重置文件類別選擇
            document.querySelector('select[name="category"]').selectedIndex = 0;

            // 如果有所有工地選擇，也重置它
            var siteSelect = document.querySelector('select[name="site"]');
            if (siteSelect && !siteSelect.disabled) {
                siteSelect.selectedIndex = 0;
            }

            // 提交表單以應用篩選條件
            // 注意：這將保留搜索框中的關鍵詞
            document.querySelector('.search-form').submit();
        }
    </script>
    <style>
        /* 減少頁面頂部和底部的間距 */
        body {
            margin: 0;
            padding: 0;
        }

        /* 調整主內容區域的間距 */
        .main-content {
            padding: 0;
        }

        /* 減少頂部導航欄的高度 */
        header {
            min-height: 50px;
            padding: 0 10px;
        }

        /* 減少標題的間距 */
        h1.page-title {
            margin: 0;
            font-size: 20px;
        }

        /* 減少標題圖標的間距 */
        h1.page-title i {
            margin-right: 5px;
        }

        /* 減少頁面標題的間距 */
        .page-header {
            margin: 0;
            padding: 10px 15px;
        }

        /* 減少卡片的間距 */
        .dashboard-card {
            margin-bottom: 10px;
        }

        /* 減少卡片頭部的間距 */
        .card-header {
            padding: 8px 15px;
        }

        /* 減少卡片內容的間距 */
        .card-body {
            padding: 10px 15px;
        }

        /* 減少搜索表單的間距 */
        .search-form {
            margin-bottom: 0;
        }

        /* 減少搜索按鈕的間距 */
        .search-form .btn {
            margin-top: 10px;
        }

        /* 確保暗黑模式下導覽列顏色固定為 #1e1e1e */
        body.theme-dark .navbar,
        body.theme-dark header {
            background-color: #1e1e1e !important;
        }

        /* 確保導覽列中的元素在暗黑模式下保持正確的顏色 */
        body.theme-dark .navbar a,
        body.theme-dark .navbar .nav-item,
        body.theme-dark header .nav-item {
            color: #ffffff !important;
        }

        body.theme-dark .navbar a.logout,
        body.theme-dark .navbar .nav-item.logout,
        body.theme-dark header .nav-item.logout {
            color: #ea4335 !important;
        }

        /* 所有篩選條件行樣式 */
        .all-filters-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 6px;
            margin-bottom: 10px;
            align-items: flex-start; /* 從頂部對齊 */
            height: 38px; /* 固定高度 */
        }

        .all-filters-row .form-group {
            flex: 1;
            margin-bottom: 0;
            height: 38px; /* 統一高度 */
            display: flex; /* 使用flex布局 */
            align-items: center; /* 垂直居中 */
        }

        /* 搜索按鈕容器樣式 */
        .search-btn-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 38px;
            margin: 0;
            padding: 0;
            flex: 0 0 auto; /* 不要伸縮，使用自然寬度 */
            width: auto;
        }

        /* 搜索按鈕和清除按鈕樣式 */
        .search-btn, .clear-btn {
            height: 38px;
            padding: 0 12px;
            margin: 0;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 0;
            vertical-align: top;
            box-sizing: border-box;
            font-size: 14px;
        }

        /* 確保按鈕內的圖標和文字垂直居中 */
        .search-btn i, .clear-btn i {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
        }

        /* 清除按鈕特定樣式 */
        .clear-btn {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .clear-btn:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        /* 確保所有下拉選單高度和樣式一致 */
        .all-filters-row select.form-control {
            width: 100%;
            height: 38px;
            padding: 0 10px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            background-color: var(--input-bg);
            color: var(--text-color);
            vertical-align: top;
            line-height: 38px;
        }

        /* 日期範圍選擇器樣式 */
        .date-range {
            display: flex;
            align-items: flex-start;
            height: 38px; /* 與下拉選單高度一致 */
            vertical-align: top;
        }

        .date-inputs {
            display: flex;
            align-items: center;
            justify-content: center; /* 居中對齊 */
            width: 100%;
            height: 38px; /* 與下拉選單高度一致 */
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--input-bg);
            padding: 0;
            overflow: hidden;
        }

        .date-inputs input[type="date"] {
            flex: 1;
            min-width: 70px;
            max-width: calc(50% - 5px);
            height: 38px; /* 與下拉選單高度一致 */
            border: none;
            background: transparent;
            padding: 0;
            color: var(--text-color);
            box-sizing: border-box;
            font-size: 14px; /* 確保字體大小一致 */
            text-align: center; /* 文字居中 */
            cursor: pointer; /* 使整個輸入框可點擊 */
            letter-spacing: -0.5px; /* 減少字元間距 */
        }

        .date-inputs input[type="date"]::-webkit-calendar-picker-indicator {
            margin: 0;
            padding: 0;
            width: 24px; /* 增大圖示尺寸 */
            height: 24px; /* 增大圖示尺寸 */
            cursor: pointer;
            opacity: 0.8; /* 增加可見度 */
        }

        .date-inputs input[type="date"]:focus {
            outline: none;
        }

        .date-separator {
            margin: 0;
            padding: 0;
            color: var(--text-color);
            line-height: 38px; /* 與下拉選單高度一致 */
            font-size: 14px;
            width: 10px; /* 減少寬度 */
            text-align: center;
            flex-shrink: 0;
        }

        /* 確保搜索按鈕與篩選器在同一行 */
        .search-form {
            display: flex;
            flex-direction: column;
        }

        .search-form .form-group:first-child {
            margin-bottom: 8px;
        }

        .search-form .form-group {
            margin-bottom: 8px;
        }

        .search-form .btn-search {
            align-self: flex-end;
            margin-top: 8px;
        }

        /* 減少搜索提示文字的間距 */
        .search-form .form-text {
            margin-top: 2px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .all-filters-row {
                flex-direction: column;
                align-items: stretch;
                height: auto; /* 移除固定高度，使其自適應內容 */
                padding-bottom: 10px;
            }

            .all-filters-row .form-group {
                width: 100%;
                margin-bottom: 8px;
                height: 38px; /* 保持高度一致 */
                display: block; /* 確保在移動設備上正確顯示 */
            }

            .search-btn, .clear-btn {
                width: 100%;
                margin-top: 8px;
                height: 38px;
                margin-right: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 1;
                padding: 0;
            }

            .clear-btn {
                margin-bottom: 8px;
            }

            .date-range {
                height: 38px; /* 保持高度一致 */
                width: 100%; /* 確保寬度為100% */
            }

            .date-inputs {
                flex-direction: row; /* 保持水平排列 */
                height: 38px; /* 保持高度一致 */
                padding: 0;
                width: 100%; /* 確保寬度為100% */
                justify-content: center; /* 居中對齊 */
            }

            .date-inputs input[type="date"] {
                padding: 0;
                height: 38px; /* 保持高度一致 */
                min-width: 70px;
                max-width: calc(50% - 5px);
                font-size: 14px; /* 確保字體大小一致 */
                cursor: pointer; /* 使整個輸入框可點擊 */
                letter-spacing: -0.5px; /* 減少字元間距 */
            }

            .date-inputs input[type="date"]::-webkit-calendar-picker-indicator {
                width: 24px; /* 增大圖示尺寸 */
                height: 24px; /* 增大圖示尺寸 */
                opacity: 0.8; /* 增加可見度 */
            }

            .date-separator {
                margin: 0;
                padding: 0;
                line-height: 38px; /* 保持高度一致 */
                width: 10px; /* 減少寬度 */
                text-align: center;
            }

            /* 確保所有下拉選單在移動設備上正確顯示 */
            .all-filters-row select.form-control {
                width: 100%;
                display: block;
            }
        }
    </style>
</head>
<body>
<header>
    <div class="logo-area">
        <a href="dashboard.php" class="logo-link">
            <img src="images/logo.png" alt="良有營造標誌">
            <span>良有營造電子簽核系統</span>
        </a>
    </div>

    <!-- 添加漢堡選單按鈕，在移動設備上顯示 -->
    <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <nav class="main-nav" id="mainNav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-tachometer-alt"></i> 簽核總覽
        </a>

        <a href="document_manager.php" class="nav-item active">
            <i class="fas fa-file-alt"></i> 公文整理系統
        </a>

        <?php if ($isAdmin): ?>
        <a href="auto_upload_manager.php" class="nav-item">
            <i class="fas fa-robot"></i> 公文自動上傳管理
        </a>
        <?php endif; ?>
    </nav>

    <div class="user-actions" id="userActions">
    <button onclick="toggleTheme()" class="theme-toggle" title="切換主題">
            <i id="theme-icon" class="fas fa-sun"></i>
            <span id="theme-text">切換淺色模式</span>
        </button>
        <a href="logout.php" class="nav-item logout">
            <i class="fas fa-sign-out-alt"></i> <span class="logout-text">登出</span>
        </a>

        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <div class="user-info-details">
                <span class="user-name"><?php echo htmlspecialchars($_SESSION['name'] ?? $username); ?></span>
                <span class="user-position">
                    <?php if (!empty($_SESSION['position_name'])): ?>
                        <i class="fas fa-id-badge" style="margin-right: 4px; font-size: 12px;"></i>
                        <?php echo htmlspecialchars($_SESSION['position_name']); ?>
                    <?php else: ?>
                        <i class="fas fa-user-tag" style="margin-right: 4px; font-size: 12px;"></i>
                        <?php echo $role_mapping[$role] ?? '未知'; ?>
                    <?php endif; ?>
                </span>
                <span class="user-site"><?php echo htmlspecialchars($site); ?></span>
            </div>
        </div>

        <div class="notification-icon" onclick="toggleNotifications(event)">
            <i class="fas fa-bell"></i>
            <span class="notification-text">通知</span>
            <span class="notification-badge" id="notification-badge" <?php echo $notification_count > 0 ? '' : 'style="display:none;"'; ?>>
                <?php echo $notification_count; ?>
            </span>
            <div class="notification-dropdown" id="notificationDropdown">
                <div class="notification-header" style="padding: 10px 15px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; font-size: 16px;">通知訊息</h3>
                    <?php if (count($all_notifications) > 0): ?>
                    <button id="mark-read-btn" class="btn btn-sm" style="padding: 2px 8px; font-size: 12px; background-color: var(--primary-color); color: var(--navbar-text); border: none; border-radius: 4px; cursor: pointer;" onclick="markAllAsRead(event)">
                        <i class="fas fa-check"></i> 全部已讀
                    </button>
                    <?php endif; ?>
                </div>
                <div id="notifications-list">
                    <?php if (count($all_notifications) > 0): ?>
                        <?php foreach ($all_notifications as $notification): ?>
                        <div class="notification-item <?php echo $notification['read'] ? 'read' : 'unread'; ?>">
                            <div><?php echo htmlspecialchars($notification['message']); ?></div>
                            <div class="notification-time"><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="notification-item">
                            <div>目前沒有新通知</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</header>
    <div class="main-content">
        <?php if ($isAdmin && $db_update_needed): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <div>系統檢測到數據庫結構需要更新，批量上傳功能可能無法正常工作。</div>
            <div class="mt-2">
                <a href="update_database.php" class="btn btn-sm btn-primary">
                    <i class="fas fa-database"></i> 立即更新數據庫
                </a>
            </div>
        </div>
        <?php endif; ?>

        <div class="page-header">
            <h1 class="page-title"><i class="fas fa-file-alt"></i> 公文整理系統</h1>
            <div>
                <?php
                // 定義可以進行文件操作的角色
                $isAdmin = $role === 'admin';
                $isHqSupervisor = $role === 'hq_supervisor';

                // 判斷是否為總公司人員，不限職位
                $isHqStaff = ($site === '總公司');

                // 合併權限判斷
                $canManageDocuments = ($isAdmin || $isHqSupervisor || $isHqStaff);

                if ($canManageDocuments):
                ?>
                <button type="button" class="btn btn-primary" onclick="showUploadModal()">
                    <i class="fas fa-plus"></i> 上傳公文
                </button>
                <button type="button" class="btn btn-success" onclick="showBatchUploadModal()">
                    <i class="fas fa-upload"></i> 批量上傳
                </button>
                <button type="button" id="batchDeleteBtn" class="btn btn-danger" style="display:none;">
                    <i class="fas fa-trash"></i> 批量刪除
                </button>
                <?php endif; ?>
            </div>
        </div>

        <?php if ($upload_success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <div><?php echo $upload_message; ?></div>
        </div>
        <?php elseif (!empty($upload_message)): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-circle"></i>
            <div><?php echo $upload_message; ?></div>

            <?php if (isset($_SESSION['skipped_file'])): ?>
            <div class="mt-2">
                <strong>已跳過文件：</strong> <?php echo htmlspecialchars($_SESSION['skipped_file']['name']); ?><br>
                <strong>原因：</strong> <?php echo htmlspecialchars($_SESSION['skipped_file']['reason']); ?><br>
                <?php if (!empty($_SESSION['skipped_file']['document_number'])): ?>
                <strong>發文字號：</strong> <?php echo htmlspecialchars($_SESSION['skipped_file']['document_number']); ?><br>
        <?php endif; ?>

                <?php if (!empty($_SESSION['skipped_file']['existing_id'])): ?>
                <div class="mt-2">
                    <a href="javascript:void(0)" class="btn btn-primary" onclick="viewDocument(<?php echo $_SESSION['skipped_file']['existing_id']; ?>)">
                        <i class="fas fa-search"></i> 查看已存在的文件
                    </a>
                </div>
                        <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="dashboard-card">
            <div class="card-header">
                <h2>公文搜索與篩選</h2>
            </div>
            <div class="card-body">
                <?php if (!$is_hq_role): ?>
                <div class="alert alert-info" style="margin-bottom: 15px;">
                    <i class="fas fa-info-circle"></i>
                    <span>您只能查看和搜索您所屬工地的公文。</span>
                </div>
                <?php endif; ?>

                <form class="search-form" method="GET" action="">
                    <div class="form-group">
                        <input type="text" class="form-control" name="search" placeholder="搜索公文內容、標題或字號..." value="<?php echo htmlspecialchars($search_term); ?>">
                        <small class="form-text text-muted">支持多關鍵詞搜索，例如：「計價 第12次」將同時匹配包含這兩個關鍵詞的文件</small>
                    </div>

                    <div class="all-filters-row">
                        <div class="form-group site-filter">
                            <select class="form-control" name="site" <?php echo !$is_hq_role ? 'disabled' : ''; ?>>
                                <?php if ($is_hq_role): ?>
                                <option value="0">所有工地</option>
                                <?php endif; ?>

                                <?php
                                // 如果不是總公司角色，只顯示自己的工地
                                foreach ($sites as $site):
                                    // 非總公司角色且不是自己的工地則跳過
                                    if (!$is_hq_role && $site['id'] != $site_filter) continue;
                                ?>
                                <option value="<?php echo $site['id']; ?>" <?php echo $site_filter == $site['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($site['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (!$is_hq_role): ?>
                            <input type="hidden" name="site" value="<?php echo $site_filter; ?>">
                            <?php endif; ?>
                        </div>

                        <div class="form-group sender-filter">
                            <select class="form-control" name="sender">
                                <option value="">發文單位</option>
                                <?php foreach ($senders as $sender): ?>
                                <option value="<?php echo htmlspecialchars($sender); ?>" <?php echo $sender_filter === $sender ? 'selected' : ''; ?>>
                                    <?php
                                    // 如果是分組的發文單位，顯示特殊標籤
                                    if ($sender === '慈濟基金會' && isset($_SESSION['sender_groups']['慈濟基金會'])):
                                        echo '慈濟基金會 (' . count($_SESSION['sender_groups']['慈濟基金會']) . ')';
                                    elseif ($sender === '內政部國土管理署' && isset($_SESSION['sender_groups']['內政部國土管理署'])):
                                        echo '內政部國土管理署 (' . count($_SESSION['sender_groups']['內政部國土管理署']) . ')';
                                    elseif ($sender === '內政部營建署' && isset($_SESSION['sender_groups']['內政部營建署'])):
                                        echo '內政部營建署 (' . count($_SESSION['sender_groups']['內政部營建署']) . ')';
                                    else:
                                        echo htmlspecialchars($sender);
                                    endif;
                                    ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group date-range">
                            <div class="date-inputs">
                                <input type="date" name="start_date" placeholder="開始日期" value="<?php echo isset($_GET['start_date']) ? htmlspecialchars($_GET['start_date']) : ''; ?>" onclick="this.showPicker()" title="選擇開始日期">
                                <span class="date-separator">至</span>
                                <input type="date" name="end_date" placeholder="結束日期" value="<?php echo isset($_GET['end_date']) ? htmlspecialchars($_GET['end_date']) : ''; ?>" onclick="this.showPicker()" title="選擇結束日期">
                            </div>
                        </div>

                        <div class="form-group category-filter">
                            <select class="form-control" name="category">
                                <option value="0">文件類別</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="button" class="btn btn-secondary clear-btn" style="margin-top: 0; vertical-align: top; margin-right: 5px;" onclick="clearFilters()">
                            <i class="fas fa-times" style="margin-right: 4px;"></i><span>清除條件</span>
                        </button>
                        <button type="submit" class="btn btn-primary search-btn" style="margin-top: 0; vertical-align: top;">
                            <i class="fas fa-search" style="margin-right: 4px;"></i><span>搜索</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="card-header">
                <h2>公文列表</h2>
                <span class="badge badge-info"><?php echo $total_documents; ?> 份公文</span>
            </div>
            <div class="card-body">
                <div class="document-container">
                    <div class="table-responsive">
                        <table class="document-table">
                            <thead>
                                <tr>
                                    <?php if ($is_hq_role): ?>
                                    <th style="width: 40px; text-align: center; vertical-align: middle;">
                                        <input type="checkbox" id="selectAll" title="全選/取消全選" style="margin: 0 auto; display: block;">
                                    </th>
                                    <?php endif; ?>
                                    <th>發文字號</th>
                                    <th>發文日期</th>
                                    <th>主旨</th>
                                    <th>說明</th>
                                    <th>工地/分類</th>
                                    <th>上傳者</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($documents) > 0): ?>
                                    <?php foreach ($documents as $doc): ?>
                                    <tr>
                                        <?php if ($is_hq_role): ?>
                                        <td style="text-align: center; vertical-align: middle; width: 40px;">
                                            <input type="checkbox" class="doc-checkbox" data-id="<?php echo $doc['id']; ?>" data-title="<?php echo htmlspecialchars($doc['title'] ?? $doc['file_name'], ENT_QUOTES); ?>">
                                        </td>
                                        <?php endif; ?>
                                        <td data-label="發文字號"><?php echo htmlspecialchars($doc['document_number'] ?? '未解析字號'); ?></td>
                                        <td data-label="發文日期"><?php echo $doc['issue_date'] ? date('Y-m-d', strtotime($doc['issue_date'])) : '未解析日期'; ?></td>
                                        <td data-label="主旨">
                                            <a href="javascript:void(0)" class="document-title" onclick="viewDocument(<?php echo $doc['id']; ?>)">
                                                <?php echo htmlspecialchars(mb_substr($doc['title'] ?? '未解析標題', 0, 15, 'UTF-8').(mb_strlen($doc['title'] ?? '', 'UTF-8') > 15 ? '...' : '')); ?>
                                            </a>
                                        </td>
                                        <td data-label="說明"><?php echo htmlspecialchars(mb_substr($doc['content_summary'] ?? '', 0, 30, 'UTF-8').(mb_strlen($doc['content_summary'] ?? '', 'UTF-8') > 30 ? '...' : '')); ?></td>
                                        <td data-label="工地/分類">
                                            <?php if ($doc['site_name']): ?>
                                            <span class="tag tag-site"><?php echo htmlspecialchars($doc['site_name']); ?></span>
                                            <?php endif; ?>

                                            <?php if ($doc['category_name']): ?>
                                            <span class="tag tag-category"><?php echo htmlspecialchars($doc['category_name']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td data-label="上傳者"><?php echo htmlspecialchars($doc['uploader_name'] ?? '未知'); ?></td>
                                        <td data-label="操作" style="text-align: center;">
                                            <div class="action-buttons">
                                                <button type="button" class="btn btn-view" onclick="viewDocument(<?php echo $doc['id']; ?>)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <?php if ($is_hq_role): ?>
                                                <button type="button" class="btn btn-edit" onclick="editDocument(<?php echo $doc['id']; ?>)">
                                                    <i class="fas fa-edit"></i> 編輯
                                                </button>
                                                <?php endif; ?>
                                                <a href="<?php echo $doc['file_path']; ?>" class="btn btn-download" target="_blank">
                                                    <i class="fas fa-download"></i> 下載
                                                </a>
                                                <?php if ($is_hq_role): ?>
                                                <button type="button" class="btn btn-delete" onclick="confirmDelete(<?php echo $doc['id']; ?>, '<?php echo htmlspecialchars($doc['title'] ?? $doc['file_name'], ENT_QUOTES); ?>')">
                                                    <i class="fas fa-trash"></i> 刪除
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">沒有找到符合條件的公文</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分頁導航 -->
                    <?php if ($total_pages > 0): ?>
                    <div class="pagination-container">
                        <div class="pagination-info">
                            顯示第 <?php echo min(($page-1) * $per_page + 1, $total_documents); ?>
                            至 <?php echo min($page * $per_page, $total_documents); ?> 筆，共 <?php echo $total_documents; ?> 筆公文
                        </div>

                        <ul class="pagination">
                            <!-- 上一頁按鈕 -->
                            <?php if ($page > 1): ?>
                                <li><a href="<?php echo paginationUrl($page - 1); ?>" aria-label="上一頁"><i class="fas fa-chevron-left"></i></a></li>
                            <?php else: ?>
                                <li class="disabled"><span><i class="fas fa-chevron-left"></i></span></li>
                            <?php endif; ?>

                            <!-- 頁碼 -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            // 確保至少顯示5頁（如果有的話）
                            if ($end_page - $start_page < 4) {
                                $end_page = min($total_pages, $start_page + 4);
                                if ($end_page - $start_page < 4) {
                                    $start_page = max(1, $end_page - 4);
                                }
                            }

                            // 第一頁
                            if ($start_page > 1) {
                                echo '<li><a href="' . paginationUrl(1) . '">1</a></li>';
                                if ($start_page > 2) {
                                    echo '<li class="disabled"><span>...</span></li>';
                                }
                            }

                            // 頁碼列表
                            for ($i = $start_page; $i <= $end_page; $i++) {
                                if ($i == $page) {
                                    echo '<li class="active"><span>' . $i . '</span></li>';
                                } else {
                                    echo '<li><a href="' . paginationUrl($i) . '">' . $i . '</a></li>';
                                }
                            }

                            // 最後一頁
                            if ($end_page < $total_pages) {
                                if ($end_page < $total_pages - 1) {
                                    echo '<li class="disabled"><span>...</span></li>';
                                }
                                echo '<li><a href="' . paginationUrl($total_pages) . '">' . $total_pages . '</a></li>';
                            }
                            ?>

                            <!-- 下一頁按鈕 -->
                            <?php if ($page < $total_pages): ?>
                                <li><a href="<?php echo paginationUrl($page + 1); ?>" aria-label="下一頁"><i class="fas fa-chevron-right"></i></a></li>
                            <?php else: ?>
                                <li class="disabled"><span><i class="fas fa-chevron-right"></i></span></li>
                            <?php endif; ?>
                        </ul>

                        <!-- 每頁顯示數量選擇器 -->
                        <div class="per-page-select">
                            <label for="per-page-select">每頁顯示:</label>
                            <select id="per-page-select" onchange="changePerPage(this.value)">
                                <?php foreach ($valid_per_page as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php echo $per_page == $option ? 'selected' : ''; ?>>
                                        <?php echo $option; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 公文上傳模態視窗 -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeUploadModal()">&times;</span>
            <h2>上傳公文</h2>

            <form action="document_manager.php" method="POST" enctype="multipart/form-data" id="singleUploadForm">
                <input type="hidden" name="action" value="upload">
                <input type="hidden" name="upload_type" value="single">

                <div class="form-group">
                    <label for="document">選擇PDF公文檔案：</label>
                    <input type="file" id="document" name="document" accept=".pdf" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="document_site">相關工地：</label>
                    <select id="document_site" name="site_id" class="form-control">
                        <option value="">-- 選擇工地 --</option>
                        <?php foreach ($sites as $site): ?>
                        <option value="<?php echo $site['id']; ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="document_category">公文類別：</label>
                    <select id="document_category" name="category_id" class="form-control">
                        <option value="">-- 選擇類別 --</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notify_sites_single">通知以下工地行政人員（可多選）：</label>
                    <select id="notify_sites_single" name="notify_sites[]" class="form-control" multiple style="height: 120px;">
                        <?php foreach ($sites as $site): ?>
                        <option value="<?php echo htmlspecialchars($site['name']); ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                    <small class="form-text text-muted">按住 Ctrl 鍵可多選。若不選擇，將只通知文件關聯工地的行政人員。</small>
                </div>

                <button type="submit" class="btn btn-success">
                    <i class="fas fa-upload"></i> 上傳並分析
                </button>
            </form>
        </div>
    </div>

    <!-- 批量上傳模態視窗 -->
    <div id="batchUploadModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeBatchUploadModal()">&times;</span>
            <h2>批量上傳公文</h2>

            <form id="batchUploadForm" action="javascript:void(0);" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="upload">

                <!-- 上傳方式選擇 -->
                <div class="upload-type-selector mb-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary active" id="fileUploadBtn" onclick="switchUploadType('file')">
                            <i class="fas fa-file-pdf"></i> 上傳文件
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="dirUploadBtn" onclick="switchUploadType('directory')">
                            <i class="fas fa-folder-open"></i> 上傳目錄
                        </button>
                    </div>
                </div>

                <!-- 文件拖放區域 -->
                <div class="drop-area" id="dropArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p id="dropAreaText">拖放公文PDF檔案至此處，或點擊上傳</p>
                    <p><small id="dropAreaHint">（支持多檔案上傳）</small></p>
                    <input type="file" id="documents" name="documents[]" accept=".pdf" multiple style="display: none;">
                    <input type="file" id="directory" name="directory" webkitdirectory directory style="display: none;">
                </div>

                <div id="selectedFilesList" class="mb-3">
                    <h4>已選擇的檔案：</h4>
                    <ul class="file-list" id="fileList"></ul>
                </div>

                <div class="form-group">
                    <label for="batch_site">共同工地（可選）：</label>
                    <select id="batch_site" name="site_id" class="form-control">
                        <option value="">-- 自動判斷工地 --</option>
                        <?php foreach ($sites as $site): ?>
                        <option value="<?php echo $site['id']; ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notify_sites">通知以下工地行政人員（可多選）：</label>
                    <select id="notify_sites" name="notify_sites[]" class="form-control" multiple style="height: 120px;">
                        <?php foreach ($sites as $site): ?>
                        <option value="<?php echo htmlspecialchars($site['name']); ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                    <small class="form-text text-muted">按住 Ctrl 鍵可多選。若不選擇，將只通知文件關聯工地的行政人員。</small>
                </div>

                <div class="form-group">
                    <label for="batch_category">共同類別（可選）：</label>
                    <select id="batch_category" name="category_id" class="form-control">
                        <option value="">-- 自動判斷類別 --</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <button type="button" class="btn btn-success" id="batchUploadBtn" disabled>
                <i class="fas fa-upload"></i> 批量上傳並分析
                </button>
            </form>
        </div>
    </div>

    <!-- 文件檢視模態視窗 -->
    <div id="viewDocumentModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeViewModal()">&times;</span>
            <h2 id="viewDocumentTitle">公文詳情</h2>

            <div class="tabs">
                <div class="tab active" data-tab="info">公文資訊</div>
                <div class="tab" data-tab="preview">公文預覽</div>
            </div>

            <div id="infoTab" class="tab-content active">
                <div class="document-info">
                    <div class="form-group">
                        <label>發文字號：</label>
                        <p id="viewDocumentNumber"></p>
                    </div>

                    <div class="form-group">
                        <label>發文單位：</label>
                        <p id="viewDocumentSender"></p>
                    </div>

                    <div class="form-group">
                        <label>受文者：</label>
                        <p id="viewDocumentReceiver"></p>
                    </div>

                    <div class="form-group">
                        <label>發文日期：</label>
                        <p id="viewDocumentDate"></p>
                    </div>

                    <div class="form-group">
                        <label>主旨：</label>
                        <p id="viewDocumentSubject"></p>
                    </div>

                    <div class="form-group">
                        <label>說明：</label>
                        <p id="viewDocumentSummary"></p>
                    </div>

                    <div class="form-group">
                        <label>相關工地：</label>
                        <p id="viewDocumentSite"></p>
                    </div>

                    <div class="form-group">
                        <label>公文類別：</label>
                        <p id="viewDocumentCategory"></p>
                    </div>

                    <div class="form-group">
                        <label>上傳者：</label>
                        <p id="viewDocumentUploader"></p>
                    </div>

                    <div class="form-group">
                        <label>上傳時間：</label>
                        <p id="viewDocumentUploadTime"></p>
                    </div>
                </div>

                <div class="modal-footer">
                    <?php if ($is_hq_role): ?>
                    <button type="button" class="btn btn-edit" id="editDocumentBtn">
                        <i class="fas fa-edit"></i> 編輯
                    </button>
                    <?php endif; ?>
                    <a href="#" class="btn btn-download" id="downloadDocumentBtn" target="_blank">
                        <i class="fas fa-download"></i> 下載
                    </a>
                    <button type="button" class="btn btn-primary" onclick="closeViewModal()">
                        <i class="fas fa-times"></i> 關閉
                    </button>
                </div>
            </div>

            <div id="previewTab" class="tab-content">
                <div class="document-preview">
                    <iframe id="pdfPreview" src="" width="100%" height="100%"></iframe>
                </div>

                <div class="modal-footer">
                    <a href="#" class="btn btn-download" id="downloadDocumentBtn2" target="_blank">
                        <i class="fas fa-download"></i> 下載
                    </a>
                    <button type="button" class="btn btn-primary" onclick="closeViewModal()">
                        <i class="fas fa-times"></i> 關閉
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件編輯模態視窗 -->
    <div id="editDocumentModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeEditModal()">&times;</span>
            <h2>編輯公文資訊</h2>

            <div class="edit-container" style="display: flex; gap: 20px;">
                <!-- 左側預覽區 -->
                <div class="preview-section" style="flex: 4; min-width: 65%;">
                    <h3>公文預覽</h3>
                    <div class="document-preview">
                        <iframe id="editPdfPreview" src="" width="100%" height="100%" style="border: none;"></iframe>
                    </div>
                </div>

                <!-- 右側編輯表單 -->
                <div class="edit-section" style="flex: 1; overflow-y: auto; max-height: 82vh;">
                    <form id="editDocumentForm" action="save_document_metadata.php" method="POST">
                        <input type="hidden" id="editDocumentId" name="id">

                        <div class="form-group">
                            <label for="editDocumentNumber">發文字號：</label>
                            <input type="text" id="editDocumentNumber" name="document_number" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="editIssueDate">發文日期：</label>
                            <input type="date" id="editIssueDate" name="issue_date" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="editSender">發文單位：</label>
                            <input type="text" id="editSender" name="sender" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="editReceiver">受文者：</label>
                            <input type="text" id="editReceiver" name="receiver" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="editTitle">主旨：</label>
                            <input type="text" id="editTitle" name="title" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="editSummary">說明：</label>
                            <textarea id="editSummary" name="content_summary" class="form-control" rows="4"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="editNotes">備註：</label>
                            <textarea id="editNotes" name="notes" class="form-control" rows="4"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="editSite">相關工地：</label>
                            <select id="editSite" name="site_id" class="form-control">
                                <option value="">-- 選擇工地 --</option>
                                <?php foreach ($sites as $site): ?>
                                <option value="<?php echo $site['id']; ?>"><?php echo htmlspecialchars($site['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="editCategory">公文類別：</label>
                            <select id="editCategory" name="category_id" class="form-control">
                                <option value="">-- 選擇類別 --</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="editTags">標籤（以逗號分隔）：</label>
                            <input type="text" id="editTags" name="tags" class="form-control" placeholder="例如：會議紀錄,決標,許可證">
                        </div>

                        <div class="edit-buttons" style="margin-top: 15px; display: flex; justify-content: space-between;">
                            <button type="button" class="btn btn-primary" onclick="closeEditModal()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


<script>
// 全局變數和輔助函數
window.selectedFiles = [];

// 格式化檔案大小的輔助函數
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 每頁數量變更
function changePerPage(perPage) {
    const url = new URL(window.location.href);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', 1); // 切換每頁數量時回到第一頁
    window.location.href = url.toString();
}

// 模態視窗相關函數
function showUploadModal() {
    document.getElementById('uploadModal').style.display = 'block';
}

function closeUploadModal() {
    document.getElementById('uploadModal').style.display = 'none';
}

function showBatchUploadModal() {
    document.getElementById('batchUploadModal').style.display = 'block';
}

function closeBatchUploadModal() {
    document.getElementById('batchUploadModal').style.display = 'none';
}

function closeEditModal() {
    // 確保釋放掉iframe中的資源
    const iframe = document.getElementById('editPdfPreview');
    if (iframe && iframe.src && iframe.src.startsWith('blob:')) {
        const blobUrl = iframe.src;
        iframe.src = '';
        URL.revokeObjectURL(blobUrl);
    }

    // 隱藏模態視窗
    const editModal = document.getElementById('editDocumentModal');
    if (editModal) {
        editModal.style.display = 'none';
    }
}

// 查看文件功能
function viewDocument(id) {
    // 重置預覽區域
    document.getElementById('viewDocumentModal').style.display = 'none';

    // 重置預覽容器並顯示初始載入狀態
    const previewContainer = document.querySelector('.document-preview');
    if (previewContainer) {
        previewContainer.innerHTML = `
            <div class="pdf-loading-container" style="text-align:center; padding:30px;">
                <i class="fas fa-spinner fa-spin" style="font-size:36px; margin-bottom:15px; color:#1a73e8;"></i>
                <h3>正在載入文件資訊...</h3>
                <div id="downloadProgress" style="width:80%; max-width:400px; margin:20px auto; display:none;">
                    <div style="height:8px; background-color:#e0e0e0; border-radius:4px; overflow:hidden; margin-bottom:10px;">
                        <div id="progressBar" style="height:100%; width:0%; background-color:#1a73e8; transition:width 0.3s;"></div>
                    </div>
                    <div id="progressText">準備下載中...</div>
                </div>
            </div>
        `;
    }

    // 顯示模態視窗
    document.getElementById('viewDocumentModal').style.display = 'block';

    // 首先獲取文件詳情
    fetch(`get_document_details.php?id=${id}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP錯誤：${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                const doc = data.document;

                // 設置文件資訊部分 - 使用安全的設置方式
                const setElementText = (id, text) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = text;
                    } else {
                        console.warn(`Element with id '${id}' not found`);
                    }
                };

                setElementText('viewDocumentTitle', '公文詳情');
                setElementText('viewDocumentSubject', doc.title || '未解析');
                setElementText('viewDocumentNumber', doc.document_number || '未解析');
                setElementText('viewDocumentSender', doc.sender || '未解析');
                setElementText('viewDocumentReceiver', doc.receiver || '未解析');
                setElementText('viewDocumentDate', doc.issue_date ? new Date(doc.issue_date).toLocaleDateString() : '未解析');
                setElementText('viewDocumentSummary', doc.content_summary || '未解析');
                setElementText('viewDocumentSite', doc.site_name || '未分類');
                setElementText('viewDocumentCategory', doc.category_name || '未分類');

                // 設置下載連結 - 先檢查元素是否存在
                const downloadBtn = document.getElementById('downloadDocumentBtn');
                if (downloadBtn) {
                    downloadBtn.href = doc.file_path;
                }

                // 設置編輯按鈕事件 - 先檢查元素是否存在
                const editBtn = document.getElementById('editDocumentBtn');
                if (editBtn) {
                    editBtn.onclick = function() {
                        closeViewModal();
                        editDocument(id);
                    };
                }

                // 現在處理PDF預覽
                const progressText = document.getElementById('progressText');
                const progressBar = document.getElementById('progressBar');
                const downloadProgress = document.getElementById('downloadProgress');

                // 更新顯示狀態
                previewContainer.querySelector('h3').textContent = '正在下載文件...';
                downloadProgress.style.display = 'block';

                // 使用fetch API下載PDF並監控進度
                downloadPdfWithProgress(doc.file_path, progressBar, progressText)
                    .then(blob => {
                        progressText.textContent = '處理中...';
                        const url = URL.createObjectURL(blob);

                        // 創建預覽iframe
                        previewContainer.innerHTML = `<iframe id="pdfPreview" src="${url}" width="100%" height="100%"></iframe>`;

                        // 調用修復PDF工具列的函數
                        setTimeout(() => {
                            fixPdfViewerToolbar('pdfPreview');
                        }, 500);
                    })
                    .catch(error => {
                        console.error('PDF載入錯誤:', error);
                        previewContainer.innerHTML = `
                            <div style="text-align:center; padding:30px;">
                                <i class="fas fa-exclamation-triangle" style="font-size:36px; color:#e74c3c; margin-bottom:15px;"></i>
                                <h3>無法載入PDF</h3>
                                <p>${error.message}</p>
                            </div>
                        `;
                    });
            } else {
                // 顯示具體的錯誤訊息
                console.error('載入公文資訊失敗:', data.message);
                alert('載入公文資訊失敗：' + data.message);
                closeViewModal();
            }
        })
        .catch(error => {
            // 捕獲並顯示網絡或解析錯誤
            console.error('載入公文資訊時發生錯誤:', error.message);
            alert('載入公文資訊時發生錯誤：' + error.message);
            closeViewModal();
        });
}

// PDF下載函數
function downloadPdfWithProgress(url, progressBarElement, progressTextElement) {
    return new Promise((resolve, reject) => {
        // 添加時間戳和隨機數防止緩存
        const pdfUrl = `${url}?nocache=${Date.now()}-${Math.random()}`;

        progressTextElement.textContent = '下載中...';

        fetch(pdfUrl, {
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`伺服器回應錯誤: ${response.status}`);
            }
            return response.blob();
        })
        .then(blob => {
            progressBarElement.style.width = '100%';
            progressTextElement.textContent = '下載完成';
            resolve(blob);
        })
        .catch(error => {
            console.error('下載出錯:', error.message);
            progressBarElement.style.backgroundColor = '#e74c3c';
            progressTextElement.textContent = `下載失敗: ${error.message}`;
            reject(error);
        });
    });
}

// 關閉查看模態視窗
function closeViewModal() {
    // 清除模態視窗中的內容
    const previewContainer = document.querySelector('.document-preview');
    if (previewContainer) {
        // 獲取並清理所有可能的資源
        const iframe = previewContainer.querySelector('iframe');
        if (iframe && iframe.src && iframe.src.startsWith('blob:')) {
            const blobUrl = iframe.src;
            iframe.src = '';
            URL.revokeObjectURL(blobUrl);
        }

        const canvas = previewContainer.querySelector('canvas');
        if (canvas) {
            const context = canvas.getContext('2d');
            if (context) {
                context.clearRect(0, 0, canvas.width, canvas.height);
            }
        }

        // 完全清空預覽容器
        previewContainer.innerHTML = '';
    }

    // 隱藏模態視窗
    const viewModal = document.getElementById('viewDocumentModal');
    if (viewModal) {
        viewModal.style.display = 'none';
    }

    // 重置標籤頁
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => tab.classList.remove('active'));

    const infoTab = document.querySelector('.tab[data-tab="info"]');
    if (infoTab) {
        infoTab.classList.add('active');
    }

    const contents = document.querySelectorAll('.tab-content');
    contents.forEach(content => content.classList.remove('active'));

    const infoContent = document.getElementById('infoTab');
    if (infoContent) {
        infoContent.classList.add('active');
    }
}

// 文件編輯功能
function editDocument(id) {
    // 先獲取文件詳情
    fetch(`get_document_details.php?id=${id}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP錯誤：${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                const doc = data.document;

                // 安全地設置表單值
                const setInputValue = (id, value) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = value || '';
                    } else {
                        console.warn(`Element with id '${id}' not found`);
                    }
                };

                // 填充表單
                setInputValue('editDocumentId', doc.id);
                setInputValue('editTitle', doc.title);
                setInputValue('editDocumentNumber', doc.document_number);
                setInputValue('editSender', doc.sender);
                setInputValue('editReceiver', doc.receiver);
                setInputValue('editIssueDate', doc.issue_date ? doc.issue_date.split(' ')[0] : '');
                setInputValue('editSummary', doc.content_summary);
                setInputValue('editNotes', doc.notes);

                if (doc.site_id) {
                    setInputValue('editSite', doc.site_id);
                }

                if (doc.category_id) {
                    setInputValue('editCategory', doc.category_id);
                }

                if (doc.tags) {
                    setInputValue('editTags', doc.tags);
                }

                // 設置PDF預覽
                const previewFrame = document.getElementById('editPdfPreview');
                if (previewFrame && doc.file_path) {
                    // 清除舊的預覽
                    if (previewFrame.src && previewFrame.src.startsWith('blob:')) {
                        URL.revokeObjectURL(previewFrame.src);
                    }

                    // 載入新的PDF
                    fetch(doc.file_path)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`預覽文件獲取失敗：${response.status}`);
                            }
                            return response.blob();
                        })
                        .then(blob => {
                            const blobUrl = URL.createObjectURL(blob);
                            previewFrame.src = blobUrl;

                            // 監聽iframe加載完成事件，調整PDF預覽區域
                            previewFrame.onload = function() {
                                try {
                                    // 確保iframe內容完全加載
                                    setTimeout(() => {
                                        // 嘗試調整iframe的高度與樣式，確保工具列完整顯示
                                        const frameDoc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                                        if (frameDoc) {
                                            // 向iframe注入樣式，確保工具列正確顯示
                                            const styleEl = frameDoc.createElement('style');
                                            styleEl.textContent = `
                                                #toolbarContainer {
                                                    position: sticky !important;
                                                    top: 0 !important;
                                                    z-index: 100 !important;
                                                    height: auto !important;
                                                    min-height: 32px !important;
                                                }
                                                #toolbarViewer, #toolbarViewerMiddle {
                                                    height: auto !important;
                                                    min-height: 32px !important;
                                                    display: flex !important;
                                                    align-items: center !important;
                                                }
                                            `;
                                            frameDoc.head.appendChild(styleEl);
                                        }
                                    }, 500);
                                } catch (e) {
                                    console.warn('無法調整PDF預覽樣式:', e);
                                }
                            };
                        })
                        .catch(error => {
                            console.error('無法載入PDF:', error.message);
                            previewFrame.src = 'about:blank';
                            if (previewFrame.contentDocument) {
                                previewFrame.contentDocument.body.innerHTML = `
                                <div style="padding: 20px; text-align: center;">
                                    <p>無法載入PDF預覽: ${error.message}</p>
                                </div>`;
                            }
                        });
                } else if (previewFrame) {
                    previewFrame.src = 'about:blank';
                    if (previewFrame.contentDocument) {
                        previewFrame.contentDocument.body.innerHTML = '<div style="padding: 20px; text-align: center;"><p>此文件沒有可用的預覽</p></div>';
                    }
                } else {
                    console.warn('找不到預覽框架元素(editPdfPreview)');
                }

                // 安全顯示模態視窗
                const editModal = document.getElementById('editDocumentModal');
                if (editModal) {
                    editModal.style.display = 'block';

                    // 調用修復PDF工具列的函數
                    setTimeout(() => {
                        fixPdfViewerToolbar('editPdfPreview');
                    }, 500);
                } else {
                    console.error('找不到模態視窗元素 editDocumentModal');
                }
            } else {
                console.error('載入公文資訊失敗:', data.message);
                alert('載入公文資訊失敗：' + data.message);
            }
        })
        .catch(error => {
            console.error('載入公文資訊時發生錯誤:', error.message);
            alert('載入公文資訊時發生錯誤：' + error.message);
        });
}

// 確認刪除
function confirmDelete(id, title) {
    if (confirm(`您確定要刪除公文 "${title}" 嗎？此操作無法復原!`)) {
        window.location.href = `delete_document.php?id=${id}&token=<?php echo md5(session_id()); ?>`;
    }
}

// 初始化批量上傳功能
function initializeBatchUpload() {
    console.log("初始化批量上傳功能...");

    const dropArea = document.getElementById('dropArea');
    const fileInput = document.getElementById('documents');
    const directoryInput = document.getElementById('directory');
    const fileList = document.getElementById('fileList');
    const uploadBtn = document.getElementById('batchUploadBtn');
    const fileUploadBtn = document.getElementById('fileUploadBtn');
    const dirUploadBtn = document.getElementById('dirUploadBtn');

    if (!dropArea || !fileInput || !directoryInput || !fileList || !uploadBtn) {
        console.error('批量上傳所需的元素未找到', {
            dropArea: !!dropArea,
            fileInput: !!fileInput,
            directoryInput: !!directoryInput,
            fileList: !!fileList,
            uploadBtn: !!uploadBtn
        });
        return;
    }

    // 全局變數，記錄當前的上傳類型 ('file' 或 'directory')
    window.currentUploadType = 'file';

    console.log("批量上傳元素初始化成功");

    // 切換上傳類型的函數
    window.switchUploadType = function(type) {
        console.log("切換上傳類型為:", type);
        window.currentUploadType = type;

        // 切換按鈕樣式
        if (type === 'file') {
            fileUploadBtn.classList.add('active');
            fileUploadBtn.classList.remove('btn-outline-primary');
            fileUploadBtn.classList.add('btn-primary');

            dirUploadBtn.classList.remove('active');
            dirUploadBtn.classList.remove('btn-primary');
            dirUploadBtn.classList.add('btn-outline-primary');

            // 更新拖放區文字
            document.getElementById('dropAreaText').textContent = '拖放公文PDF檔案至此處，或點擊上傳';
            document.getElementById('dropAreaHint').textContent = '（支持多檔案上傳）';
        } else {
            dirUploadBtn.classList.add('active');
            dirUploadBtn.classList.remove('btn-outline-primary');
            dirUploadBtn.classList.add('btn-primary');

            fileUploadBtn.classList.remove('active');
            fileUploadBtn.classList.remove('btn-primary');
            fileUploadBtn.classList.add('btn-outline-primary');

            // 更新拖放區文字
            document.getElementById('dropAreaText').textContent = '點擊選擇資料夾上傳';
            document.getElementById('dropAreaHint').textContent = '（將處理資料夾中所有PDF檔案）';
        }

        // 清除選擇的檔案
        clearSelectedFiles();
    };

    // 清除選定的檔案
    function clearSelectedFiles() {
        window.selectedFiles = [];
        fileList.innerHTML = '';
        updateUploadButton();
    }

    // 更新上傳按鈕狀態
    function updateUploadButton() {
        uploadBtn.disabled = window.selectedFiles.length === 0;
    }

    // 初始化進度條元素
    function createProgressInterface() {
        const progressInterface = document.createElement('div');
        progressInterface.className = 'upload-progress';
        progressInterface.innerHTML = `
            <div class="progress-header">
                <h4>批量上傳進度</h4>
                <span class="progress-status">準備中...</span>
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar"></div>
            </div>
            <div class="progress-detail">
                已上傳：<span class="uploaded-count">0</span>/<span class="total-count">0</span>
            </div>
        `;

        // 添加樣式
        if (!document.querySelector('style[data-for="upload-progress"]')) {
            const style = document.createElement('style');
            style.setAttribute('data-for', 'upload-progress');
            style.textContent = `
                .upload-progress {
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: var(--card-bg);
                    border-radius: 8px;
                    border: 1px solid var(--border-color);
                }
                .progress-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                }
                .progress-bar-container {
                    height: 8px;
                    background-color: var(--border-color);
                    border-radius: 4px;
                    overflow: hidden;
                    margin-bottom: 10px;
                }
                .progress-bar {
                    height: 100%;
                    width: 0%;
                    background-color: var(--primary-color);
                    transition: width 0.3s ease;
                }
                .progress-detail {
                    font-size: 14px;
                    color: var(--text-secondary);
                }
                .progress-status {
                    font-size: 14px;
                    font-weight: 500;
                }
            `;
            document.head.appendChild(style);
        }

        return progressInterface;
    }

    // 處理文件選擇
    function handleFiles(files) {
        console.log("處理選擇的文件...", files.length);

        // 清空舊的文件列表
        window.selectedFiles = [];
        fileList.innerHTML = '';

        // 過濾並處理檔案
        let processedFiles = 0;

        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // 只處理PDF檔案
            if (file.type !== 'application/pdf') {
                // 如果是目錄上傳，不顯示跳過非PDF檔案的訊息
                if (window.currentUploadType === 'file') {
                    alert(`檔案 "${file.name}" 不是PDF格式，已跳過。`);
                }
                continue;
            }

            // 檢查檔案大小
            if (file.size > 100 * 1024 * 1024) {
                alert(`檔案 "${file.name}" 大小為 ${formatFileSize(file.size)}，超過 Cloudflare 100MB 限制，無法上傳。`);
                continue;
            }

            window.selectedFiles.push(file);
            addFileToList(file, window.selectedFiles.length - 1);
            processedFiles++;
        }

        // 如果沒有有效檔案，顯示提示
        if (processedFiles === 0) {
            if (window.currentUploadType === 'directory') {
                alert('選擇的資料夾中沒有有效的PDF檔案，請選擇包含PDF檔案的資料夾。');
            } else {
                alert('沒有選擇有效的PDF檔案，請重新選擇。');
            }
        }

        // 啟用/禁用上傳按鈕
        console.log("文件處理完成，選擇了", window.selectedFiles.length, "個有效文件");
        updateUploadButton();
    }

    // 將檔案添加到列表
    function addFileToList(file, index) {
        const li = document.createElement('li');
        li.className = 'file-item';
        li.innerHTML = `
            <i class="file-icon fas fa-file-pdf"></i>
            <div class="file-name">${file.name} (${formatFileSize(file.size)})</div>
            <i class="file-remove fas fa-times" data-index="${index}"></i>
        `;
        fileList.appendChild(li);

        // 添加移除事件
        const removeBtn = li.querySelector('.file-remove');
        if (removeBtn) {
            removeBtn.addEventListener('click', function() {
                console.log("移除文件:", file.name, "索引:", this.dataset.index);
                window.selectedFiles.splice(parseInt(this.dataset.index), 1);
                updateFileList();
            });
        }
    }

    // 更新檔案列表
    function updateFileList() {
        console.log("更新文件列表...");
        fileList.innerHTML = '';
        window.selectedFiles.forEach((file, index) => {
            addFileToList(file, index);
        });
        updateUploadButton();
    }

    // 啟動後台上傳處理
    function startBackgroundUpload() {
        console.log("開始批量上傳...");

        if (window.selectedFiles.length === 0) {
            console.log("沒有選擇文件，無法上傳");
            return;
        }

        // 獲取共同的站點和類別選擇
        const siteId = document.getElementById('batch_site').value;
        const categoryId = document.getElementById('batch_category').value;

        // 獲取要通知的工地列表
        const notifySitesSelect = document.getElementById('notify_sites');
        const notifySites = Array.from(notifySitesSelect.selectedOptions).map(option => option.value);

        // 創建上傳任務ID (使用時間戳和隨機數)
        const taskId = Date.now() + '-' + Math.floor(Math.random() * 10000);
        console.log("創建上傳任務:", taskId);

        // 創建進度介面
        const progressInterface = createProgressInterface();

        // 插入到頁面標題上方
        const pageHeader = document.querySelector('.page-header');
        document.querySelector('.main-content').insertBefore(progressInterface, pageHeader);

        // 更新總數量顯示
        const totalCountElement = progressInterface.querySelector('.total-count');
        totalCountElement.textContent = window.selectedFiles.length;

        // 準備檔案資訊列表
        const filesList = window.selectedFiles.map(file => ({
            name: file.name,
            size: file.size,
            type: file.type
        }));

        // 建立上傳任務
        fetch('create_upload_task.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                taskId: taskId,
                fileCount: window.selectedFiles.length,
                siteId: siteId,
                categoryId: categoryId,
                notify_sites: notifySites,
                files: filesList
            })
        })
        .then(response => response.json())
        .then(result => {
            console.log("上傳任務建立結果:", result);

            if (result.success) {
                // 保存上傳任務資訊到localStorage
                localStorage.setItem('uploadTaskId', taskId);
                localStorage.setItem('uploadTaskTotal', window.selectedFiles.length);
                localStorage.setItem('uploadTaskStarted', Date.now());

                // 開始上傳檔案
                uploadFiles(taskId, progressInterface);

                // 顯示提示訊息
                alert('批量上傳任務已啟動，您可以在上傳過程中繼續使用系統的其他功能。即使切換頁面，上傳也會在背景繼續進行。');

                // 關閉批量上傳模態視窗
                closeBatchUploadModal();
            } else {
                alert('建立上傳任務失敗：' + result.message);
                progressInterface.remove();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('建立上傳任務時發生錯誤');
            progressInterface.remove();
        });
    }

    // 防止拖放預設行為
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(event => {
        dropArea.addEventListener(event, e => {
            e.preventDefault();
            e.stopPropagation();
        });
    });

    // 高亮拖放區域
    ['dragenter', 'dragover'].forEach(event => {
        dropArea.addEventListener(event, () => {
            dropArea.classList.add('drag-over');
        });
    });

    ['dragleave', 'drop'].forEach(event => {
        dropArea.addEventListener(event, () => {
            dropArea.classList.remove('drag-over');
        });
    });

    // 處理拖放文件
    dropArea.addEventListener('drop', e => {
        if (window.currentUploadType === 'file') {
        console.log("檔案已拖放到上傳區域");
        handleFiles(e.dataTransfer.files);
        } else {
            alert('請使用目錄選擇按鈕選擇資料夾，不支援拖放資料夾。');
        }
    });

    // 處理點擊選擇文件或資料夾
    dropArea.addEventListener('click', () => {
        if (window.currentUploadType === 'file') {
        console.log("點擊上傳區域，打開文件選擇對話框");
        fileInput.click();
        } else {
            console.log("點擊上傳區域，打開資料夾選擇對話框");
            directoryInput.click();
        }
    });

    fileInput.addEventListener('change', function() {
        console.log("用戶選擇了文件:", this.files.length);
        handleFiles(this.files);
    });

    directoryInput.addEventListener('change', function() {
        console.log("用戶選擇了資料夾，包含文件:", this.files.length);
        handleFiles(this.files);
    });

    // 處理上傳按鈕點擊
    console.log("綁定上傳按鈕點擊事件");
    uploadBtn.onclick = startBackgroundUpload;

    // 初始化上傳按鈕狀態
    window.selectedFiles = [];
    updateUploadButton();
    console.log("初始化完成，按鈕狀態:", uploadBtn.disabled ? "禁用" : "啟用");
}

// 上傳檔案
async function uploadFiles(taskId, progressInterface) {
    const progressBar = progressInterface.querySelector('.progress-bar');
    const uploadedCount = progressInterface.querySelector('.uploaded-count');
    const progressStatus = progressInterface.querySelector('.progress-status');

    // 用於收集失敗文件和跳過文件的陣列
    const failedFiles = [];
    const skippedFiles = [];

    // 逐一上傳檔案
    for (let i = 0; i < window.selectedFiles.length; i++) {
        const file = window.selectedFiles[i];

        // 更新狀態
        progressStatus.textContent = `正在上傳: ${file.name}`;

        // 建立表單資料
        const formData = new FormData();
        formData.append('taskId', taskId);
        formData.append('fileIndex', i);
        formData.append('document', file);

        try {
            // 上傳檔案
            const response = await fetch('upload_task_file.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            // 更新進度
            uploadedCount.textContent = i + 1;
            const progressPercent = ((i + 1) / window.selectedFiles.length) * 100;
            progressBar.style.width = `${progressPercent}%`;

            if (!result.success) {
                // 檢查是否是因為重複發文字號而跳過
                if (result.skipped && result.duplicate) {
                    // 收集跳過的文件資訊
                    skippedFiles.push({
                        name: file.name,
                        reason: result.message,
                        document_number: result.existing_document?.document_number || '未知',
                        existing_id: result.existing_document?.id || ''
                    });
                    progressStatus.textContent = `跳過 ${file.name}: ${result.message}`;
                    console.warn('File skipped (duplicate):', result.message);
                } else {
                // 收集失敗文件資訊
                failedFiles.push({
                    name: file.name,
                    reason: result.message || '處理失敗'
                });
                progressStatus.textContent = `上傳 ${file.name} 失敗: ${result.message}`;
                console.error('File upload failed:', result.message);
                }
            }
        } catch (error) {
            // 收集錯誤文件資訊
            failedFiles.push({
                name: file.name,
                reason: '伺服器錯誤'
            });
            console.error('Error uploading file:', error);
            progressStatus.textContent = `上傳 ${file.name} 失敗: 伺服器錯誤`;
        }
    }

    // 儲存失敗和跳過的文件信息
    if (failedFiles.length > 0) {
        localStorage.setItem('failedUploadFiles', JSON.stringify(failedFiles));
    }

    if (skippedFiles.length > 0) {
        localStorage.setItem('skippedUploadFiles', JSON.stringify(skippedFiles));
    }

    // 所有檔案上傳完成後，開始後台處理
    try {
        const response = await fetch('process_uploaded_files.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                taskId: taskId,
                failedFiles: failedFiles.length > 0 ? failedFiles : null,
                skippedFiles: skippedFiles.length > 0 ? skippedFiles : null
            })
        });

        const result = await response.json();

        if (result.success) {
            progressStatus.textContent = '處理中...';

            // 開始輪詢處理進度
            pollProcessingStatus(taskId, progressInterface);
        } else {
            progressStatus.textContent = `處理失敗: ${result.message}`;
        }
    } catch (error) {
        console.error('Error starting processing:', error);
        progressStatus.textContent = '啟動處理失敗';
    }
}

// 輪詢處理進度
function pollProcessingStatus(taskId, progressInterface) {
    const progressBar = progressInterface.querySelector('.progress-bar');
    const progressStatus = progressInterface.querySelector('.progress-status');

    const checkStatus = () => {
        fetch(`get_task_status.php?taskId=${taskId}`)
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    if (result.status === 'completed' || result.status === 'failed') {
                        // 處理完成或失敗
                        progressBar.style.width = '100%';

                        console.log('原始任務結果:', result);

                        // 檢查原始返回的結果，可能包含嵌套的JSON字符串
                        if (result.failed && typeof result.failed === 'string' && result.failed.trim()) {
                            try {
                                let parsedFailed = JSON.parse(result.failed);
                                console.log('解析後的failed字段:', parsedFailed);
                                result.failed = parsedFailed;
                            } catch (e) {
                                console.error('解析任務結果中的failed字段錯誤:', e, result.failed);
                            }
                        }

                        if (result.skipped && typeof result.skipped === 'string' && result.skipped.trim()) {
                            try {
                                let parsedSkipped = JSON.parse(result.skipped);
                                console.log('解析後的skipped字段:', parsedSkipped);
                                result.skipped = parsedSkipped;
                            } catch (e) {
                                console.error('解析任務結果中的skipped字段錯誤:', e, result.skipped);
                            }
                        }

                        // 檢查是否有失敗或跳過的文件
                        let failedFiles = result.failed || [];
                        let skippedFiles = result.skipped || [];

                        // 處理failedFiles可能是JSON字符串的情況
                        if (typeof failedFiles === 'string' && failedFiles.trim()) {
                            try {
                                // 嘗試解析JSON字符串
                                failedFiles = JSON.parse(failedFiles);
                                // 如果解析結果不是數組，將其包裝為數組
                                if (!Array.isArray(failedFiles)) {
                                    failedFiles = [failedFiles];
                                }
                            } catch (e) {
                                console.error('解析失敗文件列表錯誤:', e, failedFiles);
                                // 如果解析失敗，將字符串作為一個元素放入數組
                                failedFiles = [{
                                    name: failedFiles,
                                    file: failedFiles,
                                    reason: '解析錯誤'
                                }];
                            }
                        }

                        // 確保failedFiles是數組
                        if (!Array.isArray(failedFiles)) {
                            console.error('失敗文件列表不是數組:', failedFiles);
                            failedFiles = [{
                                name: '未知文件',
                                file: '未知文件',
                                reason: '格式錯誤: ' + (typeof failedFiles === 'string' ? failedFiles.substring(0, 50) : JSON.stringify(failedFiles).substring(0, 50))
                            }];
                        }

                        // 檢查數組中的每個元素，如果是JSON字符串則解析
                        failedFiles = failedFiles.map(file => {
                            console.log('處理失敗文件:', file);
                            if (typeof file === 'string') {
                                try {
                                    // 嘗試解析所有可能的JSON字符串
                                    if (file.trim() && file.trim().startsWith('{')) {
                                        const parsedFile = JSON.parse(file);
                                        console.log('解析後的失敗文件:', parsedFile);
                                        return parsedFile;
                                    }
                                } catch (e) {
                                    console.error('解析失敗文件對象錯誤:', e, file);
                                }
                                // 如果不是JSON或解析失敗，返回包含字符串的對象
                                return {
                                    name: file,
                                    file: file,
                                    reason: '未知錯誤'
                                };
                            }
                            return file;
                        });

                        // 處理skippedFiles可能是JSON字符串的情況
                        if (typeof skippedFiles === 'string' && skippedFiles.trim()) {
                            try {
                                // 嘗試解析JSON字符串
                                skippedFiles = JSON.parse(skippedFiles);
                                // 如果解析結果不是數組，將其包裝為數組
                                if (!Array.isArray(skippedFiles)) {
                                    skippedFiles = [skippedFiles];
                                }
                            } catch (e) {
                                console.error('解析跳過文件列表錯誤:', e, skippedFiles);
                                // 如果解析失敗，將字符串作為一個元素放入數組
                                skippedFiles = [skippedFiles];
                            }
                        }

                        // 確保skippedFiles是數組
                        if (!Array.isArray(skippedFiles)) {
                            skippedFiles = [];
                        }

                        // 檢查數組中的每個元素，如果是JSON字符串則解析
                        skippedFiles = skippedFiles.map(file => {
                            if (typeof file === 'string') {
                                try {
                                    // 嘗試解析所有可能的JSON字符串
                                    if (file.trim() && file.trim().startsWith('{')) {
                                        return JSON.parse(file);
                                    }
                                } catch (e) {
                                    console.error('解析跳過文件對象錯誤:', e, file);
                                }
                                // 如果不是JSON或解析失敗，返回包含字符串的對象
                                return {
                                    name: file,
                                    file: file,
                                    reason: '未知錯誤'
                                };
                            }
                            return file;
                        });

                        let hasIssues = false;

                        // 顯示上傳狀態摘要
                        const uploadSummary = document.createElement('div');
                        uploadSummary.className = 'upload-summary alert alert-primary mt-3';

                        // 計算成功上傳的文件數量
                        const totalFiles = parseInt(result.total) || 0;
                        const failedCount = failedFiles.length || 0;
                        const skippedCount = skippedFiles.length || 0;
                        const successCount = totalFiles - failedCount - skippedCount;

                        uploadSummary.innerHTML = `
                            <h4>批量上傳完成</h4>
                            <div class="upload-stats">
                                <p><i class="fas fa-check-circle text-success"></i> 成功上傳：${successCount} 個文件</p>
                                ${failedCount > 0 ? `<p><i class="fas fa-times-circle text-danger"></i> 上傳失敗：${failedCount} 個文件</p>` : ''}
                                ${skippedCount > 0 ? `<p><i class="fas fa-exclamation-circle text-warning"></i> 已跳過：${skippedCount} 個文件（發文字號重複）</p>` : ''}
                            </div>
                        `;

                        progressInterface.appendChild(uploadSummary);

                        // 處理失敗的文件
                        if (failedFiles.length > 0) {
                            hasIssues = true;
                            // 顯示失敗文件列表
                            const failedFilesList = document.createElement('div');
                            failedFilesList.className = 'failed-files-list';
                            failedFilesList.innerHTML = `
                                <h4 class="text-danger mb-3">以下文件處理失敗</h4>
                                <div class="failed-files">
                                    ${failedFiles.map(file => {
                                        console.log('最終顯示的失敗文件:', file);

                                        let fileName, errorReason;

                                        // 處理文件對象可能是字符串的情況
                                        if (typeof file === 'string') {
                                            try {
                                                // 嘗試解析JSON字符串
                                                if (file.trim() && file.trim().startsWith('{')) {
                                                    let parsedFile = JSON.parse(file);
                                                    fileName = parsedFile.name || parsedFile.file || '未知文件';
                                                    errorReason = parsedFile.reason || '未知錯誤';
                                                } else {
                                                    fileName = file;
                                                    errorReason = '未知錯誤';
                                                }
                                            } catch (e) {
                                                console.error('解析失敗文件信息錯誤:', e, file);
                                                fileName = file;
                                                errorReason = '解析錯誤';
                                            }
                                        } else {
                                            // 從對象中獲取信息
                                            fileName = file.name || file.file || '未知文件';
                                            errorReason = file.reason || '未知錯誤';
                                        }

                                        // 替換源文件不存在的信息為發文字號已存在
                                        if (errorReason.includes('源文件不存在')) {
                                            errorReason = '此發文字號已存在資料庫中';
                                        }

                                        return `<div class="alert alert-danger">
                                            <p class="mb-0"><strong>失敗文件：</strong> ${fileName}</p>
                                            <p class="mb-0"><strong>原因：</strong> ${errorReason}</p>
                                        </div>`;
                                    }).join('')}
                                </div>
                            `;

                            // 添加失敗文件列表到頁面
                            progressInterface.appendChild(failedFilesList);
                        }

                        // 處理跳過的文件（重複發文字號）
                        if (skippedFiles.length > 0) {
                            hasIssues = true;
                            // 顯示跳過文件列表
                            const skippedFilesList = document.createElement('div');
                            skippedFilesList.className = 'skipped-files-list';
                            skippedFilesList.innerHTML = `
                                <h4 class="text-warning mb-3">以下文件已跳過上傳</h4>
                                <div class="skipped-files">
                                    ${skippedFiles.map(file => {
                                        console.log('最終顯示的跳過文件:', file);

                                        let fileName, errorReason, docNumber, existingId;

                                        // 處理文件對象可能是字符串的情況
                                        if (typeof file === 'string') {
                                            try {
                                                // 嘗試解析JSON字符串
                                                if (file.trim() && file.trim().startsWith('{')) {
                                                    let parsedFile = JSON.parse(file);
                                                    fileName = parsedFile.name || parsedFile.file || '未知文件';
                                                    errorReason = parsedFile.reason || '重複發文字號';
                                                    docNumber = parsedFile.document_number || '';
                                                    existingId = parsedFile.existing_id || '';
                        } else {
                                                    fileName = file;
                                                    errorReason = '重複發文字號';
                                                    docNumber = '';
                                                    existingId = '';
                                                }
                                            } catch (e) {
                                                console.error('解析跳過文件信息錯誤:', e, file);
                                                fileName = file;
                                                errorReason = '解析錯誤';
                                                docNumber = '';
                                                existingId = '';
                                            }
                                        } else {
                                            // 從對象中獲取信息
                                            fileName = file.name || file.file || '未知文件';
                                            errorReason = file.reason || '重複發文字號';
                                            docNumber = file.document_number || '';
                                            existingId = file.existing_id || '';
                                        }

                                        return `<div class="alert alert-warning">
                                            <p class="mb-0"><strong>已跳過文件：</strong> ${fileName}</p>
                                            <p class="mb-0"><strong>原因：</strong> ${errorReason}</p>
                                            ${docNumber ? `<p class="mb-0"><strong>發文字號：</strong> ${docNumber}</p>` : ''}
                                            ${existingId ? `<p class="mb-0"><a href="javascript:void(0)" class="btn btn-sm btn-info mt-2" onclick="viewDocument(${existingId})">查看已存在的文件</a></p>` : ''}
                                        </div>`;
                                    }).join('')}
                                </div>
                            `;

                            // 添加跳過文件列表到頁面
                            progressInterface.appendChild(skippedFilesList);
                        }

                        if (!hasIssues) {
                            progressStatus.textContent = '處理完成！';

                            // 延遲後重新載入頁面
                            setTimeout(() => {
                                location.reload();
                            }, 2000);
                        }
                    } else if (result.status === 'processing') {
                        // 仍在處理中
                        const processed = result.processed;
                        const total = result.total;
                        const percentage = (processed / total) * 100;

                        progressBar.style.width = `${percentage}%`;
                        progressStatus.textContent = `處理中... ${processed}/${total} (${Math.round(percentage)}%)`;

                        // 繼續輪詢
                        setTimeout(checkStatus, 1000);
                    } else {
                        // 其他狀態
                        progressStatus.textContent = result.message || `未知狀態: ${result.status}`;
                    }
                } else {
                    progressStatus.textContent = `檢查狀態失敗: ${result.message}`;
                }
            });
    };

    // 開始輪詢
    setTimeout(checkStatus, 2000);
}
// 檢查是否有正在進行的上傳任務
function checkPendingUploadTask() {
    const taskId = localStorage.getItem('uploadTaskId');
    if (taskId) {
        console.log("發現未完成的上傳任務:", taskId);
        // 可以實現繼續檢查該任務的狀態
    }
}

// 初始化批量刪除功能
function initializeBatchDelete() {
    // 檢查用戶是否有權限使用批量刪除功能
    const canManageDocuments = <?php echo ($is_hq_role) ? 'true' : 'false'; ?>;
    if (!canManageDocuments) {
        console.log('當前用戶無權使用批量刪除功能');
        return;
    }

    const selectAllCheckbox = document.getElementById('selectAll');
    const docCheckboxes = document.querySelectorAll('.doc-checkbox');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');

    if (!selectAllCheckbox || !docCheckboxes.length || !batchDeleteBtn) {
        console.error('批量刪除所需的元素未找到');
        return;
    }

    // 全選/取消全選
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;

        docCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });

        updateBatchDeleteButton();
    });

    // 監聽各個文件的選擇狀態
    docCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBatchDeleteButton();

            // 檢查是否所有項目都被選中，更新全選框狀態
            const allChecked = Array.from(docCheckboxes).every(box => box.checked);
            selectAllCheckbox.checked = allChecked;
        });
    });

    // 更新批量刪除按鈕狀態
    function updateBatchDeleteButton() {
        const selectedCount = Array.from(docCheckboxes).filter(box => box.checked).length;

        if (selectedCount > 0) {
            batchDeleteBtn.style.display = 'inline-flex';
            batchDeleteBtn.innerHTML = `<i class="fas fa-trash"></i> 批量刪除 (${selectedCount})`;
        } else {
            batchDeleteBtn.style.display = 'none';
        }
    }

    // 批量刪除按鈕點擊事件
    batchDeleteBtn.addEventListener('click', function() {
        const selectedDocs = Array.from(docCheckboxes)
            .filter(box => box.checked)
            .map(box => ({
                id: box.dataset.id,
                title: box.dataset.title
            }));

        if (selectedDocs.length === 0) {
            alert('請至少選擇一個文件進行刪除');
            return;
        }

        confirmBatchDelete(selectedDocs);
    });

    // 批量刪除確認
    function confirmBatchDelete(docs) {
        const docCount = docs.length;
        let confirmMessage = `您確定要刪除選擇的 ${docCount} 個文件嗎？此操作無法復原！`;

        // 如果選擇的文件超過5個，顯示數量；如果少於5個，顯示文件名稱
        if (docCount <= 5) {
            confirmMessage += '\n\n要刪除的文件：\n';
            docs.forEach((doc, index) => {
                confirmMessage += `${index + 1}. ${doc.title}\n`;
            });
        }

        if (confirm(confirmMessage)) {
            // 建立表單提交
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'batch_delete_documents.php';
            form.style.display = 'none';

            // 添加文件ID
            const idsField = document.createElement('input');
            idsField.type = 'hidden';
            idsField.name = 'document_ids';
            idsField.value = JSON.stringify(docs.map(doc => doc.id));
            form.appendChild(idsField);

            // 添加安全令牌
            const tokenField = document.createElement('input');
            tokenField.type = 'hidden';
            tokenField.name = 'token';
            tokenField.value = '<?php echo md5(session_id()); ?>';
            form.appendChild(tokenField);

            // 添加到頁面並提交
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 初始化按鈕狀態
    updateBatchDeleteButton();
}

// 頁面加載完成後初始化所有功能
document.addEventListener('DOMContentLoaded', function() {
    console.log("頁面已完全載入，開始初始化功能...");

    // 清理會話中的跳過文件信息
    <?php if (isset($_SESSION['skipped_file']) && !isset($_POST['action'])): ?>
    <?php
    // 如果是GET請求且有跳過文件信息，則清除
    unset($_SESSION['skipped_file']);
    ?>
    <?php endif; ?>

    // 初始化標籤頁
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有標籤的 active 狀態
            tabs.forEach(t => t.classList.remove('active'));
            // 為當前標籤添加 active 狀態
            this.classList.add('active');

            // 隱藏所有內容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));

            // 顯示當前標籤對應的內容
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId + 'Tab').classList.add('active');
        });
    });

    // 批量上傳功能
    console.log("初始化批量上傳功能...");
    // 初始化全局變數以便在任何需要的地方訪問
    window.selectedFiles = [];

    // 初始化批量上傳功能
    initializeBatchUpload();

    // 確保默認顯示文件上傳選項
    if (typeof switchUploadType === 'function') {
        switchUploadType('file');
    }

    // 初始化批量刪除功能
    console.log("初始化批量刪除功能...");
    initializeBatchDelete();

    // 檢查是否有正在進行的上傳任務
    console.log("檢查是否有正在進行的上傳任務...");
    checkPendingUploadTask();

    // 關閉模態視窗的其它方法 (點擊視窗外部分)
    window.addEventListener('click', function(event) {
        if (event.target.className === 'modal') {
            event.target.style.display = 'none';

            // 如果是查看文件的模態視窗，清空 iframe 源
            if (event.target.id === 'viewDocumentModal') {
                const iframe = document.getElementById('pdfPreview');
                if (iframe && iframe.src) {
                    iframe.src = '';
                }
            }
        }
    });

    console.log("頁面初始化完成");
});

// 將所有通知標記為已讀
function markAllAsRead(event) {
    fetch('mark_notifications_read.php', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新通知計數顯示
            const badge = document.getElementById('notification-badge');
            badge.style.display = 'none';
            badge.textContent = '0';

            // 更新通知項目樣式
            const notifications = document.querySelectorAll('.notification-item.unread');
            notifications.forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
            });

            // 如果關閉通知下拉選單，等待一段時間讓用戶看到效果
            setTimeout(() => {
                toggleNotifications();
            }, 1000);
        } else {
            console.error('標記通知失敗:', data.message);
        }
    })
    .catch(error => {
        console.error('處理通知時發生錯誤:', error);
    });

    // 防止事件冒泡，避免觸發通知下拉選單的關閉
    if (event) event.stopPropagation();
}

// 通知訊息切換
function toggleNotifications(event) {
    if (event) {
        event.stopPropagation(); // 防止事件冒泡
    }

    const dropdown = document.getElementById('notificationDropdown');
    if (dropdown.style.display === 'block') {
        dropdown.style.display = 'none';
    } else {
        dropdown.style.display = 'block';

        // 在移動設備上，調整通知下拉選單的位置
        if (window.innerWidth <= 767) {
            // 獲取通知圖標的位置和大小
            const notificationIcon = document.querySelector('.notification-icon');
            if (notificationIcon) {
                const rect = notificationIcon.getBoundingClientRect();

                // 設置下拉選單的位置（相對於通知圖標）
                dropdown.style.top = (rect.bottom + window.scrollY) + 'px';
                dropdown.style.left = '0';
                dropdown.style.width = '100%';
                dropdown.style.maxWidth = '100%';
            }
        }
    }
}

// 點擊頁面其他區域時關閉通知下拉選單
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('notificationDropdown');
    const notificationIcon = document.querySelector('.notification-icon');

    if (dropdown && dropdown.style.display === 'block' &&
        !notificationIcon.contains(event.target)) {
        dropdown.style.display = 'none';
    }
});

// 加入防止意外關閉的功能
document.addEventListener('DOMContentLoaded', function() {
    const editModal = document.getElementById('editDocumentModal');

    // 防止點擊視窗外部關閉
    window.addEventListener('click', function(event) {
        if (event.target === editModal) {
            // 不做任何事情，防止關閉
            event.stopPropagation();
        }
    });

    // 處理表單提交
    const editForm = document.getElementById('editDocumentForm');
    editForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(editForm);

        fetch(editForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message || '公文資訊已成功更新');
                closeEditModal();
                // 如果有需要刷新頁面
                if (data.redirect) {
                    window.location.href = data.redirect;
                } else {
                    location.reload();
                }
            } else {
                alert(data.message || '更新失敗，請稍後重試');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('發生錯誤，請重試');
        });
    });
});

// 確保批量上傳按鈕狀態一致性的監控函數
function monitorUploadButton() {
    setInterval(function() {
        const uploadBtn = document.getElementById('submitBatchUpload');
        if (uploadBtn) {
            const shouldBeEnabled = window.selectedFiles && window.selectedFiles.length > 0;
            if (uploadBtn.disabled !== !shouldBeEnabled) {
                console.log("修正批量上傳按鈕狀態:", shouldBeEnabled ? "啟用" : "禁用");
                uploadBtn.disabled = !shouldBeEnabled;
            }
        }
    }, 1000);
}

// 在頁面加載後啟動監控
window.addEventListener('load', monitorUploadButton);
</script>

    <!-- 添加移動設備導覽列控制腳本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 漢堡選單控制
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mainNav = document.getElementById('mainNav');
            const userActions = document.getElementById('userActions');

            if (mobileMenuToggle && mainNav && userActions) {
                // 初始化時隱藏在大螢幕上的漢堡選單按鈕
                if (window.innerWidth > 767) {
                    mobileMenuToggle.style.display = 'none';
                } else {
                    // 在小螢幕上初始化隱藏選單
                    mainNav.style.display = 'none';
                    userActions.style.display = 'none';
                }

                // 點擊漢堡選單按鈕時切換選單顯示狀態
                mobileMenuToggle.addEventListener('click', function() {
                    mainNav.classList.toggle('active');
                    userActions.classList.toggle('active');

                    if (mainNav.classList.contains('active')) {
                        mainNav.style.display = 'flex';
                        userActions.style.display = 'flex';
                    } else {
                        mainNav.style.display = 'none';
                        userActions.style.display = 'none';
                    }

                    // 切換漢堡選單圖標
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        if (icon.classList.contains('fa-bars')) {
                            icon.classList.remove('fa-bars');
                            icon.classList.add('fa-times');
                        } else {
                            icon.classList.remove('fa-times');
                            icon.classList.add('fa-bars');
                        }
                    }
                });

                // 監聽視窗大小變化，調整顯示
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 767) {
                        // 在大螢幕上，隱藏漢堡選單按鈕並恢復選單
                        mobileMenuToggle.style.display = 'none';
                        mainNav.classList.remove('active');
                        mainNav.style.display = '';
                        userActions.classList.remove('active');
                        userActions.style.display = '';

                        // 恢復漢堡圖標
                        const icon = mobileMenuToggle.querySelector('i');
                        if (icon) {
                            icon.classList.remove('fa-times');
                            icon.classList.add('fa-bars');
                        }
                    } else {
                        // 在小螢幕上，顯示漢堡選單按鈕
                        mobileMenuToggle.style.display = 'block';

                        // 恢復選單隱藏狀態(若未active)
                        if (!mainNav.classList.contains('active')) {
                            mainNav.style.display = 'none';
                        }
                        if (!userActions.classList.contains('active')) {
                            userActions.style.display = 'none';
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>