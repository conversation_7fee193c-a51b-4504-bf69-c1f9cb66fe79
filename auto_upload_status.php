<?php
session_start();
require_once 'db.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

// 獲取系統狀態
$status = [
    'database' => false,
    'tables' => false,
    'configs' => 0,
    'logs' => 0,
    'smbclient' => false,
    'directories' => false
];

try {
    // 檢查資料庫連接
    $status['database'] = $pdo instanceof PDO;
    
    // 檢查表格
    $stmt = $pdo->query("SHOW TABLES LIKE 'auto_upload%'");
    $status['tables'] = $stmt->rowCount() >= 2;
    
    // 檢查設定數量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM auto_upload_configs");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $status['configs'] = $result['count'];
    
    // 檢查日誌數量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM auto_upload_logs");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $status['logs'] = $result['count'];
    
} catch (Exception $e) {
    // 資料庫錯誤
}

// 檢查smbclient
$smbclient_check = shell_exec('which smbclient 2>/dev/null');
$status['smbclient'] = !empty(trim($smbclient_check));

// 檢查目錄
$upload_dir = 'uploads/auto_upload/';
$log_dir = 'logs/';
if (!is_dir($upload_dir)) mkdir($upload_dir, 0755, true);
if (!is_dir($log_dir)) mkdir($log_dir, 0755, true);
$status['directories'] = is_writable($upload_dir) && is_writable($log_dir);

// 獲取設定列表
$configs = [];
try {
    $stmt = $pdo->query("
        SELECT auc.*, s.name as site_name 
        FROM auto_upload_configs auc 
        LEFT JOIN sites s ON auc.site_id = s.id 
        ORDER BY auc.name
    ");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // 忽略錯誤
}

// 獲取最近的日誌
$recent_logs = [];
try {
    $stmt = $pdo->query("
        SELECT aul.*, auc.name as config_name 
        FROM auto_upload_logs aul 
        LEFT JOIN auto_upload_configs auc ON aul.config_id = auc.id 
        ORDER BY aul.scan_time DESC 
        LIMIT 10
    ");
    $recent_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // 忽略錯誤
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自動上傳系統狀態 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #9c27b0;
            --info-hover: #7b1fa2;
            --dark-bg: #f5f5f5;
            --card-bg: #ffffff;
            --card-header: #f8f9fa;
            --table-header: #f8f9fa;
            --table-row-hover: #f5f5f5;
            --border-color: #e0e0e0;
            --text-color: #333333;
            --text-muted: #6c757d;
            --text-secondary: #6c757d;
            --light-bg: #f8f9fa;
            --navbar-bg: #ffffff;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        /* 暗黑模式變數 */
        body.theme-dark {
            --dark-bg: #121212;
            --card-bg: #1e1e1e;
            --card-header: #2d2d2d;
            --table-header: #2d2d2d;
            --table-row-hover: #2d2d2d;
            --border-color: #404040;
            --text-color: #ffffff;
            --text-muted: #b0b0b0;
            --text-secondary: #b0b0b0;
            --light-bg: #2d2d2d;
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body {
            font-family: 'Segoe UI', 'Microsoft JhengHei', sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        /* 頂部導航樣式 */
        header {
            display: flex;
            align-items: center;
            background-color: var(--navbar-bg);
            padding: 0 20px;
            height: 60px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            margin-bottom: 0;
            padding-bottom: 0;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .logo-area {
            display: flex;
            align-items: center;
        }

        .logo-link {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-link img {
            height: 36px;
            margin-right: 10px;
        }

        .logo-link span {
            color: var(--text-color);
            font-size: 16px;
            font-weight: 600;
        }

        .main-nav {
            display: flex;
            align-items: center;
            margin-left: 20px;
            gap: 5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            color: var(--text-color);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s;
            font-size: 14px;
            white-space: nowrap;
        }

        .nav-item:hover {
            background-color: var(--navbar-hover);
        }

        .nav-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-item i {
            margin-right: 6px;
            font-size: 14px;
        }

        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .theme-toggle:hover {
            background-color: var(--navbar-hover);
        }

        .logout {
            color: #ea4335 !important;
            font-weight: 500;
        }

        .logout:hover {
            background-color: rgba(234, 67, 53, 0.1) !important;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-color);
        }

        .user-info i {
            font-size: 14px;
            color: #4285f4 !important;
        }

        .user-info-details {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color);
        }

        .user-position {
            font-size: 11px;
            color: var(--text-muted);
        }

        .user-site {
            font-size: 14px;
            color: var(--text-color);
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: var(--text-color);
        }

        .notification-icon:hover {
            background-color: var(--navbar-hover);
        }

        .container {
            max-width: 1200px;
            margin: 80px auto 20px;
            padding: 20px;
        }
        .card {
            background: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .header {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .header h1 {
            color: var(--text-color);
            margin: 0 0 15px 0;
        }

        /* 暗黑模式下的特殊樣式 */
        body.theme-dark {
            background-color: var(--dark-bg);
        }

        body.theme-dark .user-name {
            color: #ffffff !important;
        }

        body.theme-dark .user-position {
            color: #34a853 !important;
        }

        body.theme-dark .user-site {
            color: #aaaaaa !important;
        }

        body.theme-dark header {
            background-color: #1e1e1e !important;
        }

        body.theme-dark .status-ok {
            background-color: rgba(40, 167, 69, 0.2);
            color: #4caf50;
            border-color: rgba(40, 167, 69, 0.3);
        }

        body.theme-dark .status-error {
            background-color: rgba(220, 53, 69, 0.2);
            color: #f44336;
            border-color: rgba(220, 53, 69, 0.3);
        }

        body.theme-dark .status-warning {
            background-color: rgba(251, 188, 5, 0.2);
            color: #ffc107;
            border-color: rgba(251, 188, 5, 0.3);
        }

        body.theme-dark .badge-success {
            background-color: rgba(40, 167, 69, 0.2);
            color: #4caf50;
        }

        body.theme-dark .badge-danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #f44336;
        }

        body.theme-dark .badge-warning {
            background-color: rgba(251, 188, 5, 0.2);
            color: #ffc107;
        }

        body.theme-dark .table th {
            background: var(--table-header);
            color: var(--text-color);
        }

        body.theme-dark .table td {
            color: var(--text-color);
            border-bottom-color: var(--border-color);
        }

        body.theme-dark code {
            background: var(--card-header) !important;
            color: var(--text-color) !important;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .main-nav {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: var(--navbar-bg);
                flex-direction: column;
                padding: 10px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }

            .main-nav.show {
                display: flex;
            }

            .nav-item {
                width: 100%;
                justify-content: flex-start;
                padding: 12px 16px;
                margin-bottom: 5px;
            }

            .user-actions {
                gap: 10px;
            }

            .theme-text,
            .logout-text,
            .notification-text {
                display: none;
            }

            .user-info-details {
                display: none;
            }

            .container {
                padding: 10px;
            }
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-item {
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .status-ok {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .table th, .table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .badge-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
<header>
    <div class="logo-area">
        <a href="dashboard.php" class="logo-link">
            <img src="images/logo.png" alt="良有營造標誌">
            <span>良有營造電子簽核系統</span>
        </a>
    </div>

    <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <nav class="main-nav" id="mainNav">
        <a href="dashboard.php" class="nav-item">
            <i class="fas fa-tachometer-alt"></i> 簽核總覽
        </a>
        <a href="document_manager.php" class="nav-item">
            <i class="fas fa-file-alt"></i> 公文整理系統
        </a>
        <a href="auto_upload_manager.php" class="nav-item">
            <i class="fas fa-robot"></i> 公文自動上傳管理
        </a>
    </nav>

    <div class="user-actions" id="userActions">
        <button onclick="toggleTheme()" class="theme-toggle" title="切換主題">
            <i id="theme-icon" class="fas fa-sun"></i>
            <span id="theme-text">切換深色模式</span>
        </button>
        <a href="logout.php" class="nav-item logout">
            <i class="fas fa-sign-out-alt"></i> <span class="logout-text">登出</span>
        </a>

        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <div class="user-info-details">
                <span class="user-name">系統管理員</span>
                <span class="user-position">
                    <i class="fas fa-user-tag" style="margin-right: 4px; font-size: 12px;"></i>
                    管理員
                </span>
                <span class="user-site">總公司</span>
            </div>
        </div>

        <div class="notification-icon">
            <i class="fas fa-bell"></i>
            <span class="notification-text">通知</span>
        </div>
    </div>
</header>

    <div class="container">
        <div class="card">
            <div class="header">
                <h1><i class="fas fa-tachometer-alt"></i> 自動上傳系統狀態</h1>
                <a href="auto_upload_manager.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> 管理設定
                </a>
                <a href="manual_upload_test.php" class="btn btn-success">
                    <i class="fas fa-play"></i> 手動測試
                </a>
                <a href="document_manager.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回公文系統
                </a>
            </div>

            <h3>系統狀態檢查</h3>
            <div class="status-grid">
                <div class="status-item <?php echo $status['database'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-database"></i><br>
                    <strong>資料庫連接</strong><br>
                    <?php echo $status['database'] ? '正常' : '異常'; ?>
                </div>
                
                <div class="status-item <?php echo $status['tables'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-table"></i><br>
                    <strong>資料表</strong><br>
                    <?php echo $status['tables'] ? '已建立' : '未建立'; ?>
                </div>
                
                <div class="status-item <?php echo $status['smbclient'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-network-wired"></i><br>
                    <strong>SMB客戶端</strong><br>
                    <?php echo $status['smbclient'] ? '已安裝' : '未安裝'; ?>
                </div>
                
                <div class="status-item <?php echo $status['directories'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-folder"></i><br>
                    <strong>目錄權限</strong><br>
                    <?php echo $status['directories'] ? '正常' : '異常'; ?>
                </div>
                
                <div class="status-item <?php echo $status['configs'] > 0 ? 'status-ok' : 'status-warning'; ?>">
                    <i class="fas fa-cogs"></i><br>
                    <strong>設定數量</strong><br>
                    <?php echo $status['configs']; ?> 個
                </div>
                
                <div class="status-item status-ok">
                    <i class="fas fa-list"></i><br>
                    <strong>執行日誌</strong><br>
                    <?php echo $status['logs']; ?> 筆
                </div>
            </div>
        </div>

        <div class="card">
            <h3>自動上傳設定</h3>
            <?php if (empty($configs)): ?>
            <p>尚未建立任何自動上傳設定。</p>
            <a href="auto_upload_manager.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增設定
            </a>
            <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>設定名稱</th>
                        <th>SMB路徑</th>
                        <th>對應工地</th>
                        <th>狀態</th>
                        <th>最後掃描</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($configs as $config): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($config['name']); ?></td>
                        <td><code><?php echo htmlspecialchars($config['smb_path']); ?></code></td>
                        <td><?php echo htmlspecialchars($config['site_name'] ?? '未設定'); ?></td>
                        <td>
                            <span class="badge <?php echo $config['is_active'] ? 'badge-success' : 'badge-danger'; ?>">
                                <?php echo $config['is_active'] ? '啟用' : '停用'; ?>
                            </span>
                        </td>
                        <td><?php echo $config['last_scan_time'] ? date('Y-m-d H:i', strtotime($config['last_scan_time'])) : '未掃描'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h3>最近執行日誌</h3>
            <?php if (empty($recent_logs)): ?>
            <p>尚無執行日誌。</p>
            <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>時間</th>
                        <th>設定</th>
                        <th>檔案</th>
                        <th>狀態</th>
                        <th>訊息</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_logs as $log): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i', strtotime($log['scan_time'])); ?></td>
                        <td><?php echo htmlspecialchars($log['config_name'] ?? '未知'); ?></td>
                        <td><?php echo htmlspecialchars($log['file_name']); ?></td>
                        <td>
                            <span class="badge <?php 
                                echo $log['status'] === 'success' ? 'badge-success' : 
                                    ($log['status'] === 'failed' ? 'badge-danger' : 'badge-warning'); 
                            ?>">
                                <?php 
                                $status_map = [
                                    'success' => '成功',
                                    'failed' => '失敗',
                                    'skipped' => '跳過',
                                    'duplicate' => '重複'
                                ];
                                echo $status_map[$log['status']] ?? $log['status'];
                                ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($log['error_message'] ?? ''); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h3>系統資訊</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div>
                    <strong>PHP版本:</strong> <?php echo PHP_VERSION; ?><br>
                    <strong>系統時間:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                    <strong>時區:</strong> <?php echo date_default_timezone_get(); ?>
                </div>
                <div>
                    <strong>上傳目錄:</strong> uploads/auto_upload/<br>
                    <strong>日誌目錄:</strong> logs/<br>
                    <strong>SMB帳號:</strong> paper
                </div>
            </div>
        </div>

        <div class="card">
            <h3>快速操作</h3>
            <p>
                <a href="auto_upload_manager.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> 管理自動上傳設定
                </a>
                <a href="manual_upload_test.php" class="btn btn-success">
                    <i class="fas fa-play"></i> 手動測試上傳
                </a>
                <a href="?refresh=1" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> 重新整理狀態
                </a>
            </p>
            
            <h4>設定Cron Job</h4>
            <p>要啟用自動排程，請在伺服器上執行以下命令：</p>
            <code style="background: #f8f9fa; padding: 10px; display: block; border-radius: 4px;">
                chmod +x setup_cron.sh<br>
                ./setup_cron.sh
            </code>
            
            <h4>手動執行測試</h4>
            <p>您也可以手動執行自動上傳腳本：</p>
            <code style="background: #f8f9fa; padding: 10px; display: block; border-radius: 4px;">
                php auto_upload_cron.php
            </code>
        </div>
    </div>

    <script>
        // 主題切換功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (newTheme === 'dark') {
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切換淺色模式';
                document.body.classList.add('theme-dark');
            } else {
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切換深色模式';
                document.body.classList.remove('theme-dark');
            }
        }

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (savedTheme === 'dark') {
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切換淺色模式';
                document.body.classList.add('theme-dark');
            } else {
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切換深色模式';
                document.body.classList.remove('theme-dark');
            }

            // 移動選單切換
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mainNav = document.getElementById('mainNav');

            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    mainNav.classList.toggle('show');
                });
            }
        });
    </script>
</body>
</html>
