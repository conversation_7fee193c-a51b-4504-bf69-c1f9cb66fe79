<?php
session_start();
require_once 'db.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

// 獲取系統狀態
$status = [
    'database' => false,
    'tables' => false,
    'configs' => 0,
    'logs' => 0,
    'smbclient' => false,
    'directories' => false
];

try {
    // 檢查資料庫連接
    $status['database'] = $pdo instanceof PDO;
    
    // 檢查表格
    $stmt = $pdo->query("SHOW TABLES LIKE 'auto_upload%'");
    $status['tables'] = $stmt->rowCount() >= 2;
    
    // 檢查設定數量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM auto_upload_configs");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $status['configs'] = $result['count'];
    
    // 檢查日誌數量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM auto_upload_logs");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $status['logs'] = $result['count'];
    
} catch (Exception $e) {
    // 資料庫錯誤
}

// 檢查smbclient
$smbclient_check = shell_exec('which smbclient 2>/dev/null');
$status['smbclient'] = !empty(trim($smbclient_check));

// 檢查目錄
$upload_dir = 'uploads/auto_upload/';
$log_dir = 'logs/';
if (!is_dir($upload_dir)) mkdir($upload_dir, 0755, true);
if (!is_dir($log_dir)) mkdir($log_dir, 0755, true);
$status['directories'] = is_writable($upload_dir) && is_writable($log_dir);

// 獲取設定列表
$configs = [];
try {
    $stmt = $pdo->query("
        SELECT auc.*, s.name as site_name 
        FROM auto_upload_configs auc 
        LEFT JOIN sites s ON auc.site_id = s.id 
        ORDER BY auc.name
    ");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // 忽略錯誤
}

// 獲取最近的日誌
$recent_logs = [];
try {
    $stmt = $pdo->query("
        SELECT aul.*, auc.name as config_name 
        FROM auto_upload_logs aul 
        LEFT JOIN auto_upload_configs auc ON aul.config_id = auc.id 
        ORDER BY aul.scan_time DESC 
        LIMIT 10
    ");
    $recent_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // 忽略錯誤
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自動上傳系統狀態 - 良有營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .header {
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-item {
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .status-ok {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin: 5px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .table th, .table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .badge-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1><i class="fas fa-tachometer-alt"></i> 自動上傳系統狀態</h1>
                <a href="auto_upload_manager.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> 管理設定
                </a>
                <a href="manual_upload_test.php" class="btn btn-success">
                    <i class="fas fa-play"></i> 手動測試
                </a>
                <a href="document_manager.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回公文系統
                </a>
            </div>

            <h3>系統狀態檢查</h3>
            <div class="status-grid">
                <div class="status-item <?php echo $status['database'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-database"></i><br>
                    <strong>資料庫連接</strong><br>
                    <?php echo $status['database'] ? '正常' : '異常'; ?>
                </div>
                
                <div class="status-item <?php echo $status['tables'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-table"></i><br>
                    <strong>資料表</strong><br>
                    <?php echo $status['tables'] ? '已建立' : '未建立'; ?>
                </div>
                
                <div class="status-item <?php echo $status['smbclient'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-network-wired"></i><br>
                    <strong>SMB客戶端</strong><br>
                    <?php echo $status['smbclient'] ? '已安裝' : '未安裝'; ?>
                </div>
                
                <div class="status-item <?php echo $status['directories'] ? 'status-ok' : 'status-error'; ?>">
                    <i class="fas fa-folder"></i><br>
                    <strong>目錄權限</strong><br>
                    <?php echo $status['directories'] ? '正常' : '異常'; ?>
                </div>
                
                <div class="status-item <?php echo $status['configs'] > 0 ? 'status-ok' : 'status-warning'; ?>">
                    <i class="fas fa-cogs"></i><br>
                    <strong>設定數量</strong><br>
                    <?php echo $status['configs']; ?> 個
                </div>
                
                <div class="status-item status-ok">
                    <i class="fas fa-list"></i><br>
                    <strong>執行日誌</strong><br>
                    <?php echo $status['logs']; ?> 筆
                </div>
            </div>
        </div>

        <div class="card">
            <h3>自動上傳設定</h3>
            <?php if (empty($configs)): ?>
            <p>尚未建立任何自動上傳設定。</p>
            <a href="auto_upload_manager.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增設定
            </a>
            <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>設定名稱</th>
                        <th>SMB路徑</th>
                        <th>對應工地</th>
                        <th>狀態</th>
                        <th>最後掃描</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($configs as $config): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($config['name']); ?></td>
                        <td><code><?php echo htmlspecialchars($config['smb_path']); ?></code></td>
                        <td><?php echo htmlspecialchars($config['site_name'] ?? '未設定'); ?></td>
                        <td>
                            <span class="badge <?php echo $config['is_active'] ? 'badge-success' : 'badge-danger'; ?>">
                                <?php echo $config['is_active'] ? '啟用' : '停用'; ?>
                            </span>
                        </td>
                        <td><?php echo $config['last_scan_time'] ? date('Y-m-d H:i', strtotime($config['last_scan_time'])) : '未掃描'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h3>最近執行日誌</h3>
            <?php if (empty($recent_logs)): ?>
            <p>尚無執行日誌。</p>
            <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>時間</th>
                        <th>設定</th>
                        <th>檔案</th>
                        <th>狀態</th>
                        <th>訊息</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_logs as $log): ?>
                    <tr>
                        <td><?php echo date('Y-m-d H:i', strtotime($log['scan_time'])); ?></td>
                        <td><?php echo htmlspecialchars($log['config_name'] ?? '未知'); ?></td>
                        <td><?php echo htmlspecialchars($log['file_name']); ?></td>
                        <td>
                            <span class="badge <?php 
                                echo $log['status'] === 'success' ? 'badge-success' : 
                                    ($log['status'] === 'failed' ? 'badge-danger' : 'badge-warning'); 
                            ?>">
                                <?php 
                                $status_map = [
                                    'success' => '成功',
                                    'failed' => '失敗',
                                    'skipped' => '跳過',
                                    'duplicate' => '重複'
                                ];
                                echo $status_map[$log['status']] ?? $log['status'];
                                ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($log['error_message'] ?? ''); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h3>系統資訊</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div>
                    <strong>PHP版本:</strong> <?php echo PHP_VERSION; ?><br>
                    <strong>系統時間:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                    <strong>時區:</strong> <?php echo date_default_timezone_get(); ?>
                </div>
                <div>
                    <strong>上傳目錄:</strong> uploads/auto_upload/<br>
                    <strong>日誌目錄:</strong> logs/<br>
                    <strong>SMB帳號:</strong> paper
                </div>
            </div>
        </div>

        <div class="card">
            <h3>快速操作</h3>
            <p>
                <a href="auto_upload_manager.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> 管理自動上傳設定
                </a>
                <a href="manual_upload_test.php" class="btn btn-success">
                    <i class="fas fa-play"></i> 手動測試上傳
                </a>
                <a href="?refresh=1" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> 重新整理狀態
                </a>
            </p>
            
            <h4>設定Cron Job</h4>
            <p>要啟用自動排程，請在伺服器上執行以下命令：</p>
            <code style="background: #f8f9fa; padding: 10px; display: block; border-radius: 4px;">
                chmod +x setup_cron.sh<br>
                ./setup_cron.sh
            </code>
            
            <h4>手動執行測試</h4>
            <p>您也可以手動執行自動上傳腳本：</p>
            <code style="background: #f8f9fa; padding: 10px; display: block; border-radius: 4px;">
                php auto_upload_cron.php
            </code>
        </div>
    </div>
</body>
</html>
