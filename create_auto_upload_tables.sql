-- 建立自動上傳設定表
CREATE TABLE IF NOT EXISTS `auto_upload_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '設定名稱',
  `smb_path` varchar(500) NOT NULL COMMENT 'SMB路徑',
  `site_id` int(11) NOT NULL COMMENT '對應工地ID',
  `category_id` int(11) DEFAULT NULL COMMENT '預設文件類別ID',
  `upload_time` time NOT NULL DEFAULT '09:00:00' COMMENT '每日上傳時間',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否啟用',
  `last_scan_time` datetime DEFAULT NULL COMMENT '最後掃描時間',
  `scan_subdirs` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否掃描子目錄',
  `file_extensions` varchar(255) DEFAULT 'pdf,doc,docx,jpg,jpeg,png' COMMENT '允許的檔案副檔名',
  `created_by` int(11) NOT NULL COMMENT '建立者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_upload_time` (`upload_time`),
  FOREIGN KEY (`site_id`) REFERENCES `sites`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`category_id`) REFERENCES `document_categories`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自動上傳設定表';

-- 建立自動上傳日誌表
CREATE TABLE IF NOT EXISTS `auto_upload_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_id` int(11) NOT NULL COMMENT '設定ID',
  `file_path` varchar(500) NOT NULL COMMENT '檔案路徑',
  `file_name` varchar(255) NOT NULL COMMENT '檔案名稱',
  `file_size` bigint(20) DEFAULT NULL COMMENT '檔案大小',
  `status` enum('success','failed','skipped','duplicate') NOT NULL COMMENT '上傳狀態',
  `error_message` text DEFAULT NULL COMMENT '錯誤訊息',
  `document_id` int(11) DEFAULT NULL COMMENT '上傳成功後的文件ID',
  `scan_time` datetime NOT NULL COMMENT '掃描時間',
  `upload_time` datetime DEFAULT NULL COMMENT '上傳時間',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '檔案雜湊值',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_status` (`status`),
  KEY `idx_scan_time` (`scan_time`),
  KEY `idx_file_hash` (`file_hash`),
  FOREIGN KEY (`config_id`) REFERENCES `auto_upload_configs`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`document_id`) REFERENCES `official_documents`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自動上傳日誌表';

-- 建立檔案雜湊索引表（避免重複上傳）
CREATE TABLE IF NOT EXISTS `uploaded_file_hashes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `file_hash` varchar(64) NOT NULL COMMENT '檔案雜湊值',
  `file_name` varchar(255) NOT NULL COMMENT '檔案名稱',
  `file_path` varchar(500) NOT NULL COMMENT '原始檔案路徑',
  `document_id` int(11) NOT NULL COMMENT '對應的文件ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_file_hash` (`file_hash`),
  KEY `idx_document_id` (`document_id`),
  FOREIGN KEY (`document_id`) REFERENCES `official_documents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='已上傳檔案雜湊表';

-- 插入初始設定資料（根據用戶提供的資料夾）
INSERT INTO `auto_upload_configs` (`name`, `smb_path`, `site_id`, `upload_time`, `created_by`) VALUES
('中壢前寮段收文', '\\\\192.168.55.251\\公文\\04中壢前寮段\\收文', 1, '09:00:00', 1),
('花蓮慈濟醫院收文', '\\\\192.168.55.251\\公文\\05花蓮慈濟醫院\\收文', 2, '09:00:00', 1),
('花蓮美崙安居收文', '\\\\192.168.55.251\\公文\\06花蓮美崙安居\\收文', 3, '09:00:00', 1),
('大愛屋新建工程收文', '\\\\192.168.55.251\\公文\\12主商段大愛屋新建工程(中繼屋)\\收文', 4, '09:00:00', 1),
('基隆麥金段新建工程收文', '\\\\192.168.55.251\\公文\\13基隆麥金段新建工程\\收文', 5, '09:00:00', 1),
('鳳林台電收文', '\\\\192.168.55.251\\公文\\14鳳林台電\\收文', 6, '09:00:00', 1),
('新莊福營段社會福利設施收文', '\\\\192.168.55.251\\公文\\15新莊福營段社會福利設施老人日間照顧中心\\收文', 7, '09:00:00', 1);
