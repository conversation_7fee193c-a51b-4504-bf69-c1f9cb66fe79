<?php
session_start();
require_once 'db.php';

// 檢查用戶權限 - 只有管理員可以使用
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '權限不足']);
    exit;
}

$config_id = $_GET['id'] ?? 0;

if (!$config_id) {
    echo json_encode(['success' => false, 'message' => '缺少設定ID']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT * FROM auto_upload_configs WHERE id = ?");
    $stmt->execute([$config_id]);
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        echo json_encode(['success' => true, 'config' => $config]);
    } else {
        echo json_encode(['success' => false, 'message' => '找不到指定的設定']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '資料庫錯誤：' . $e->getMessage()]);
}
?>
